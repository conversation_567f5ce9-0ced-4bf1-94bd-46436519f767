{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { BelatedTraineeNotifyListRoutingModule } from './belated-trainee-notify-list-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport { NgbDatepickerModule, NgbTimepickerModule } from '@ng-bootstrap/ng-bootstrap';\nimport { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';\nimport * as i0 from \"@angular/core\";\nexport let BelatedTraineeNotifyListModule = /*#__PURE__*/(() => {\n  class BelatedTraineeNotifyListModule {}\n\n  BelatedTraineeNotifyListModule.ɵfac = function BelatedTraineeNotifyListModule_Factory(t) {\n    return new (t || BelatedTraineeNotifyListModule)();\n  };\n\n  BelatedTraineeNotifyListModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: BelatedTraineeNotifyListModule\n  });\n  BelatedTraineeNotifyListModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, BelatedTraineeNotifyListRoutingModule, SharedModule, NgxMaterialTimepickerModule, NgbDatepickerModule, NgbTimepickerModule]]\n  });\n  return BelatedTraineeNotifyListModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}