{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { BlockUI } from \"ng-block-ui\";\nimport { environment } from \"src/environments/environment\";\nimport { Subject } from \"rxjs\";\nimport * as moment from \"moment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../_services/authentication.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../_services/common.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"ng-block-ui\";\nimport * as i6 from \"ngx-bootstrap/datepicker\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"ngx-extended-pdf-viewer\";\nconst _c0 = [\"pdfViewerOnDemand\"];\n\nfunction TimeWiseLearningHourStudyReportComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelementStart(1, \"ngx-extended-pdf-viewer\", 15);\n    i0.ɵɵlistener(\"srcChange\", function TimeWiseLearningHourStudyReportComponent_div_21_Template_ngx_extended_pdf_viewer_srcChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return ctx_r1.url = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r0.url)(\"zoom\", \"page-width\")(\"showPrintButton\", true)(\"showRotateButton\", true)(\"showOpenFileButton\", true)(\"showSecondaryToolbarButton\", true)(\"useBrowserLocale\", true);\n  }\n}\n\nexport class TimeWiseLearningHourStudyReportComponent {\n  constructor(authService, formBuilder, _service, toastr) {\n    this.authService = authService;\n    this.formBuilder = formBuilder;\n    this._service = _service;\n    this.toastr = toastr;\n    this.courseList = [];\n    this.traineeList = [];\n    this.baseUrl = environment.baseUrl;\n    this.submitted = false;\n    this.traineeLoading = false;\n    this.traineeInput$ = new Subject();\n    this.traineeDisabled = false;\n    this.bsValue = [];\n    this.authService.getCurrentUser().subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  ngOnInit() {\n    this.bsConfig = Object.assign({}, {\n      maxDate: new Date(),\n      containerClass: \"theme-blue\",\n      rangeInputFormat: \"DD MMM YYYY\"\n    });\n    this.filterForm = this.formBuilder.group({\n      dates: [[]] // traineeId: [null,],\n\n    });\n  }\n\n  get f() {\n    return this.filterForm.controls;\n  }\n\n  showReport(reportType) {\n    this.submitted = true;\n    if (this.filterForm.invalid) return;\n    this.blockUI.start(\"Generating report. Please wait...\");\n    const obj = {\n      reportType: reportType === \"WebView\" ? \"Pdf\" : reportType,\n      startDate: this.filterForm.value.dates.length === 2 ? moment(this.filterForm.value.dates[0]).format(\"DD-MMM-YYYY\") : null,\n      endDate: this.filterForm.value.dates.length === 2 ? moment(this.filterForm.value.dates[1]).format(\"DD-MMM-YYYY\") : null\n    };\n\n    this._service.downloadFile(\"open-material/get-time-wise-learning-hour-study-report\", obj).subscribe(res => {\n      this.submitted = false;\n      this.blockUI.stop();\n\n      if (reportType === \"WebView\") {\n        this.reportFileName = \"Time_Wise_Learning_Hour_Study_Report.pdf\";\n        this.url = res;\n        return;\n      }\n\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement(\"a\");\n      link.href = url;\n      link.download = \"Time_Wise_Learning_Hour_Study_Report_\" + (reportType === \"Excel\" ? \"xlsx\" : \"pdf\");\n      link.click();\n    }, err => {\n      this.toastr.warning(err.message || err, \"Warning!\");\n      this.blockUI.stop();\n    });\n  }\n\n}\n\nTimeWiseLearningHourStudyReportComponent.ɵfac = function TimeWiseLearningHourStudyReportComponent_Factory(t) {\n  return new (t || TimeWiseLearningHourStudyReportComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.CommonService), i0.ɵɵdirectiveInject(i4.ToastrService));\n};\n\nTimeWiseLearningHourStudyReportComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: TimeWiseLearningHourStudyReportComponent,\n  selectors: [[\"app-time-wise-learning-hour-study-report\"]],\n  viewQuery: function TimeWiseLearningHourStudyReportComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.pdfViewerOnDemand = _t.first);\n    }\n  },\n  decls: 22,\n  vars: 3,\n  consts: [[1, \"row\"], [1, \"col-lg-12\"], [1, \"card\", \"card-border-default\"], [1, \"card-header\"], [1, \"card-block\"], [\"autocomplete\", \"off\", 1, \"col-12\", \"custom-inline-form\", 3, \"formGroup\"], [1, \"mb-3\", \"col-lg-3\", \"col-12\"], [1, \"col-form-label\", \"col-form-label-sm\", \"text-right\"], [\"type\", \"text\", \"readonly\", \"\", \"formControlName\", \"dates\", \"bsDaterangepicker\", \"\", \"placeholder\", \"Select a date range\", 1, \"form-control\", \"form-control-sm\", 3, \"bsConfig\"], [\"type\", \"button\", 1, \"btn\", \"btn-theme\", \"btn-testz\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"feather\", \"icon-eye\"], [1, \"btn\", \"btn-theme\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"feather\", \"icon-download\"], [\"class\", \"w-100 admin-height-605\", 4, \"ngIf\"], [1, \"w-100\", \"admin-height-605\"], [3, \"src\", \"zoom\", \"showPrintButton\", \"showRotateButton\", \"showOpenFileButton\", \"showSecondaryToolbarButton\", \"useBrowserLocale\", \"srcChange\"]],\n  template: function TimeWiseLearningHourStudyReportComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"h5\");\n      i0.ɵɵtext(6, \" Time Wise Learning Hour Study Report\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 4);\n      i0.ɵɵelementStart(8, \"form\", 5);\n      i0.ɵɵelementStart(9, \"div\", 0);\n      i0.ɵɵelementStart(10, \"div\", 6);\n      i0.ɵɵelementStart(11, \"label\", 7);\n      i0.ɵɵtext(12, \" Date Range \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(13, \"input\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"div\", 6);\n      i0.ɵɵelementStart(15, \"button\", 9);\n      i0.ɵɵlistener(\"click\", function TimeWiseLearningHourStudyReportComponent_Template_button_click_15_listener() {\n        return ctx.showReport(\"WebView\");\n      });\n      i0.ɵɵelement(16, \"i\", 10);\n      i0.ɵɵtext(17, \" Preview \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"button\", 11);\n      i0.ɵɵlistener(\"click\", function TimeWiseLearningHourStudyReportComponent_Template_button_click_18_listener() {\n        return ctx.showReport(\"Excel\");\n      });\n      i0.ɵɵelement(19, \"i\", 12);\n      i0.ɵɵtext(20, \" Download \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(21, TimeWiseLearningHourStudyReportComponent_div_21_Template, 2, 7, \"div\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"bsConfig\", ctx.bsConfig);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngIf\", ctx.url != null);\n    }\n  },\n  directives: [i5.BlockUIComponent, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.DefaultValueAccessor, i6.BsDaterangepickerInputDirective, i2.NgControlStatus, i2.FormControlName, i6.BsDaterangepickerDirective, i7.NgIf, i8.NgxExtendedPdfViewerComponent],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], TimeWiseLearningHourStudyReportComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}