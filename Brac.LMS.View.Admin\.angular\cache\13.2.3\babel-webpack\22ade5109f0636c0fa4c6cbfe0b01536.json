{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { BelatedTraineeNotifyRoutingModule } from './belated-trainee-notify-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport { OwlDateTimeModule, OwlNativeDateTimeModule } from '@danielmoncada/angular-datetime-picker';\nimport * as i0 from \"@angular/core\";\nexport let BelatedTraineeNotifyModule = /*#__PURE__*/(() => {\n  class BelatedTraineeNotifyModule {}\n\n  BelatedTraineeNotifyModule.ɵfac = function BelatedTraineeNotifyModule_Factory(t) {\n    return new (t || BelatedTraineeNotifyModule)();\n  };\n\n  BelatedTraineeNotifyModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: BelatedTraineeNotifyModule\n  });\n  BelatedTraineeNotifyModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, BelatedTraineeNotifyRoutingModule, SharedModule, OwlDateTimeModule, OwlNativeDateTimeModule]]\n  });\n  return BelatedTraineeNotifyModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}