{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { BelatedTraineeNotifyListComponent } from './belated-trainee-notify-list.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: BelatedTraineeNotifyListComponent\n}];\nexport let BelatedTraineeNotifyListRoutingModule = /*#__PURE__*/(() => {\n  class BelatedTraineeNotifyListRoutingModule {}\n\n  BelatedTraineeNotifyListRoutingModule.ɵfac = function BelatedTraineeNotifyListRoutingModule_Factory(t) {\n    return new (t || BelatedTraineeNotifyListRoutingModule)();\n  };\n\n  BelatedTraineeNotifyListRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: BelatedTraineeNotifyListRoutingModule\n  });\n  BelatedTraineeNotifyListRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return BelatedTraineeNotifyListRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}