{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { environment } from 'src/environments/environment';\nimport { Validators } from '@angular/forms';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { Page } from '../_models/page';\nimport { BlockUI } from 'ng-block-ui';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"ng-block-ui\";\nimport * as i7 from \"ngx-bootstrap/popover\";\nimport * as i8 from \"@angular/flex-layout/extended\";\nimport * as i9 from \"ngx-bootstrap/collapse\";\nimport * as i10 from \"../_helpers/safe-pipe\";\nimport * as i11 from \"ngx-moment\";\n\nfunction ForumDetailsComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tag_r13 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" #\", tag_r13, \" \");\n  }\n}\n\nfunction ForumDetailsComponent_img_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 55);\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r1.topic.Creator);\n  }\n}\n\nfunction ForumDetailsComponent_img_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 56);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r2.topic.Creator);\n  }\n}\n\nfunction ForumDetailsComponent_img_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 57);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r3.topic.Creator);\n  }\n}\n\nfunction ForumDetailsComponent_img_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 58);\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r4.topic.Creator);\n    i0.ɵɵproperty(\"src\", ctx_r4.baseUrl + ctx_r4.topic.CreatorImage, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction ForumDetailsComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 59);\n    i0.ɵɵelement(1, \"i\", 60);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.topic.NoOfReplies, \" \");\n  }\n}\n\nfunction ForumDetailsComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelementStart(1, \"a\", 62);\n    i0.ɵɵlistener(\"click\", function ForumDetailsComponent_div_47_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return ctx_r14.isCollapsedComment = !ctx_r14.isCollapsedComment;\n    });\n    i0.ɵɵtext(2, \"Write A Comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"aria-expanded\", !ctx_r6.isCollapsedComment);\n  }\n}\n\nconst _c0 = function (a0, a1) {\n  return {\n    \"bg-warning\": a0,\n    \"bg-danger\": a1\n  };\n};\n\nfunction ForumDetailsComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelementStart(1, \"span\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c0, ctx_r7.topic.Status === \"Pending\", ctx_r7.topic.Status === \"Closed\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.topic.Status, \" \");\n  }\n}\n\nfunction ForumDetailsComponent_div_58_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 67);\n    i0.ɵɵtext(1, \" Comment is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumDetailsComponent_div_58_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 67);\n    i0.ɵɵtext(1, \" Comment can not more than 2000 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumDetailsComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtemplate(1, ForumDetailsComponent_div_58_span_1_Template, 2, 0, \"span\", 66);\n    i0.ɵɵtemplate(2, ForumDetailsComponent_div_58_span_2_Template, 2, 0, \"span\", 66);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.cf[\"comment\"].errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.cf[\"comment\"].errors[\"maxLength\"]);\n  }\n}\n\nfunction ForumDetailsComponent_div_64_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 88);\n  }\n\n  if (rf & 2) {\n    const item_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r18.Creator);\n  }\n}\n\nfunction ForumDetailsComponent_div_64_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 89);\n  }\n\n  if (rf & 2) {\n    const item_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r18.Creator);\n  }\n}\n\nfunction ForumDetailsComponent_div_64_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 90);\n  }\n\n  if (rf & 2) {\n    const item_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r18.Creator);\n  }\n}\n\nfunction ForumDetailsComponent_div_64_img_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 91);\n  }\n\n  if (rf & 2) {\n    const item_r18 = i0.ɵɵnextContext().$implicit;\n    const ctx_r22 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r18.Creator);\n    i0.ɵɵproperty(\"src\", ctx_r22.baseUrl + item_r18.CreatorImage, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction ForumDetailsComponent_div_64_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 93);\n    i0.ɵɵtext(4, \" \\u2013 \");\n    i0.ɵɵelementStart(5, \"span\", 94);\n    i0.ɵɵlistener(\"onShown\", function ForumDetailsComponent_div_64_div_22_Template_span_onShown_5_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const reply_r30 = restoredCtx.$implicit;\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return ctx_r31.onTitleClick(reply_r30.CreatorId);\n    });\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 95);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"amCalendar\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const reply_r30 = ctx.$implicit;\n    i0.ɵɵnextContext(2);\n\n    const _r11 = i0.ɵɵreference(68);\n\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", reply_r30.Comment, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"popover\", _r11)(\"outsideClick\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", reply_r30.Creator, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 5, reply_r30.CreatedDate), \" \");\n  }\n}\n\nfunction ForumDetailsComponent_div_64_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function ForumDetailsComponent_div_64_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const item_r18 = i0.ɵɵnextContext().$implicit;\n      const ctx_r33 = i0.ɵɵnextContext();\n      return ctx_r33.onCollapse(item_r18);\n    });\n    i0.ɵɵelement(1, \"i\", 97);\n    i0.ɵɵtext(2, \" Reply \");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵattribute(\"aria-expanded\", !item_r18.IsCollapsed)(\"aria-controls\", item_r18.Id);\n  }\n}\n\nfunction ForumDetailsComponent_div_64_div_34_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 67);\n    i0.ɵɵtext(1, \" Reply is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumDetailsComponent_div_64_div_34_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 67);\n    i0.ɵɵtext(1, \" Reply can not more than 1000 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumDetailsComponent_div_64_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtemplate(1, ForumDetailsComponent_div_64_div_34_span_1_Template, 2, 0, \"span\", 66);\n    i0.ɵɵtemplate(2, ForumDetailsComponent_div_64_div_34_span_2_Template, 2, 0, \"span\", 66);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.rf[\"comment\"].errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.rf[\"comment\"].errors[\"maxLength\"]);\n  }\n}\n\nconst _c1 = function (a0, a1) {\n  return {\n    \"fa-solid text-primary\": a0,\n    \"fa-regular\": a1\n  };\n};\n\nfunction ForumDetailsComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵelement(1, \"p\", 69);\n    i0.ɵɵpipe(2, \"safe\");\n    i0.ɵɵelementStart(3, \"div\", 70);\n    i0.ɵɵelementStart(4, \"div\", 71);\n    i0.ɵɵtemplate(5, ForumDetailsComponent_div_64_img_5_Template, 1, 1, \"img\", 72);\n    i0.ɵɵtemplate(6, ForumDetailsComponent_div_64_img_6_Template, 1, 1, \"img\", 73);\n    i0.ɵɵtemplate(7, ForumDetailsComponent_div_64_img_7_Template, 1, 1, \"img\", 74);\n    i0.ɵɵtemplate(8, ForumDetailsComponent_div_64_img_8_Template, 1, 2, \"img\", 75);\n    i0.ɵɵelementStart(9, \"div\", 76);\n    i0.ɵɵelementStart(10, \"h4\", 77);\n    i0.ɵɵlistener(\"onShown\", function ForumDetailsComponent_div_64_Template_h4_onShown_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const item_r18 = restoredCtx.$implicit;\n      const ctx_r39 = i0.ɵɵnextContext();\n      return ctx_r39.onTitleClick(item_r18.CreatorId);\n    });\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 27);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"amTimeAgo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 30);\n    i0.ɵɵelementStart(16, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function ForumDetailsComponent_div_64_Template_button_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const item_r18 = restoredCtx.$implicit;\n      const ctx_r41 = i0.ɵɵnextContext();\n      return ctx_r41.onLikeButtonClick(item_r18);\n    });\n    i0.ɵɵelement(17, \"i\", 79);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 80);\n    i0.ɵɵelement(20, \"i\", 81);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, ForumDetailsComponent_div_64_div_22_Template, 10, 7, \"div\", 82);\n    i0.ɵɵelementStart(23, \"div\", 8);\n    i0.ɵɵelementStart(24, \"div\", 83);\n    i0.ɵɵtemplate(25, ForumDetailsComponent_div_64_button_25_Template, 3, 2, \"button\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 85);\n    i0.ɵɵelementStart(27, \"form\", 86);\n    i0.ɵɵelementStart(28, \"div\", 41);\n    i0.ɵɵelementStart(29, \"label\", 42);\n    i0.ɵɵtext(30, \" Your Reply \");\n    i0.ɵɵelementStart(31, \"sup\", 43);\n    i0.ɵɵtext(32, \"*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"textarea\", 87);\n    i0.ɵɵtemplate(34, ForumDetailsComponent_div_64_div_34_Template, 3, 2, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 46);\n    i0.ɵɵelementStart(36, \"div\", 47);\n    i0.ɵɵelementStart(37, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ForumDetailsComponent_div_64_Template_button_click_37_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r40);\n      const item_r18 = restoredCtx.$implicit;\n      const ctx_r42 = i0.ɵɵnextContext();\n      return ctx_r42.onSubmitReplyForm(item_r18);\n    });\n    i0.ɵɵtext(38, \" Post \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r18 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext();\n\n    const _r11 = i0.ɵɵreference(68);\n\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(2, 20, item_r18.Comment, \"html\"), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !item_r18.CreatorImage && item_r18.CreatorGender == \"Male\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r18.CreatorImage && item_r18.CreatorGender == \"Female\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r18.CreatorImage && item_r18.CreatorGender == \"Others\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r18.CreatorImage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"popover\", _r11)(\"outsideClick\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r18.Creator, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 23, item_r18.CreatedDate), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r9.topic.Status !== \"Open\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(25, _c1, item_r18.Liked, !item_r18.Liked));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r18.NoOfLikes, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r18.Replies.length, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", item_r18.Replies);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.topic.Status === \"Open\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"id\", item_r18.Id);\n    i0.ɵɵproperty(\"collapse\", item_r18.IsCollapsed)(\"isAnimated\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r9.replyForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.replySubmitted && ctx_r9.rf[\"comment\"].errors);\n  }\n}\n\nconst _c2 = function (a1) {\n  return [\"/forum-details\", a1];\n};\n\nfunction ForumDetailsComponent_div_66_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵelementStart(1, \"div\", 102);\n    i0.ɵɵelementStart(2, \"h4\", 103);\n    i0.ɵɵelementStart(3, \"a\", 104);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r44 = ctx.$implicit;\n    const i_r45 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c2, item_r44.Id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", i_r45 + 1, \". \", item_r44.Title, \"\");\n  }\n}\n\nfunction ForumDetailsComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵelementStart(1, \"h2\", 99);\n    i0.ɵɵtext(2, \"My Popular Posts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ForumDetailsComponent_div_66_div_3_Template, 5, 5, \"div\", 100);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.popularList);\n  }\n}\n\nfunction ForumDetailsComponent_ng_template_67_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelementStart(1, \"div\", 107);\n    i0.ɵɵelementStart(2, \"h5\", 108);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 109);\n    i0.ɵɵelementStart(5, \"table\", 110);\n    i0.ɵɵelementStart(6, \"tbody\");\n    i0.ɵɵelementStart(7, \"tr\");\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 111);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Division\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 111);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"tr\");\n    i0.ɵɵelementStart(17, \"th\");\n    i0.ɵɵtext(18, \"Phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 111);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\");\n    i0.ɵɵtext(22, \"Department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 111);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"tr\");\n    i0.ɵɵelementStart(26, \"th\");\n    i0.ɵɵtext(27, \"Position\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"td\", 111);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"th\");\n    i0.ɵɵtext(31, \"Unit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"td\", 111);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"tr\");\n    i0.ɵɵelementStart(35, \"th\");\n    i0.ɵɵtext(36, \"Work Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"td\", 111);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"th\");\n    i0.ɵɵtext(40, \"Sub Unit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"td\", 111);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"tr\");\n    i0.ɵɵelementStart(44, \"th\");\n    i0.ɵɵtext(45, \"Line Manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"td\", 111);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"th\");\n    i0.ɵɵtext(49, \"Grade\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"td\", 111);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r46.popoverObj.Name, \" - \", ctx_r46.popoverObj.PIN, \"\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r46.popoverObj.Email);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r46.popoverObj.Division);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r46.popoverObj.PhoneNo);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r46.popoverObj.Department);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r46.popoverObj.Position);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r46.popoverObj.Unit);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r46.popoverObj.WorkLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r46.popoverObj.SubUnit);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r46.popoverObj.LineManagerName, \" - \", ctx_r46.popoverObj.LineManagerPIN, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r46.popoverObj.Grade);\n  }\n}\n\nfunction ForumDetailsComponent_ng_template_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ForumDetailsComponent_ng_template_67_div_0_Template, 52, 13, \"div\", 105);\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.popoverObj);\n  }\n}\n\nexport class ForumDetailsComponent {\n  constructor(router, _service, toastr, route, formBuilder, _location) {\n    this.router = router;\n    this._service = _service;\n    this.toastr = toastr;\n    this.route = route;\n    this.formBuilder = formBuilder;\n    this._location = _location;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.timestamp = new Date().getTime();\n    this.postList = [];\n    this.myTopicList = [];\n    this.popularList = [];\n    this.page = new Page();\n    this.submitted = false;\n    this.replySubmitted = false;\n    this.isCollapsedComment = true;\n    this.topicId = this.route.snapshot.paramMap.get('id');\n    this.page.pageNumber = 0;\n    this.page.size = 10;\n  }\n\n  ngOnInit() {\n    this.commentForm = this.formBuilder.group({\n      comment: [null, [Validators.required, Validators.maxLength(2000)]]\n    });\n    this.replyForm = this.formBuilder.group({\n      comment: [null, [Validators.required, Validators.maxLength(1000)]]\n    });\n    this.getTopicDetails();\n    this.getTopicPosts();\n  }\n\n  get cf() {\n    return this.commentForm.controls;\n  }\n\n  get rf() {\n    return this.replyForm.controls;\n  }\n\n  setPage(pageNumber) {\n    this.page.pageNumber = pageNumber;\n    this.getTopicPosts();\n  }\n\n  getTopicDetails() {\n    this._service.get('forum/get-topic-details/' + this.topicId).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.topic = res.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => console.log('complete')\n    });\n  }\n\n  getTopicPosts() {\n    const obj = {\n      id: this.topicId,\n      size: this.page.size,\n      pageNumber: this.page.pageNumber\n    };\n\n    this._service.get('forum/topic/get-posts', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.postList = res.Data.Records;\n        this.postList.forEach(element => {\n          element.IsCollapsed = true;\n        });\n        this.page.totalElements = res.Data.Total;\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => console.log('complete')\n    });\n  }\n\n  getMyPopularTopicList() {\n    const obj = {\n      limit: 5,\n      categoryIds: null\n    };\n\n    this._service.get('forum/' + this.topicId + '/get-my-popular-topic-titles', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.popularList = res.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => console.log('complete')\n    });\n  }\n\n  backClicked() {\n    localStorage.setItem('reload-with-page', '1');\n\n    this._location.back();\n  }\n\n  onSubmitPostForm() {\n    this.submitted = true;\n\n    if (this.commentForm.invalid) {\n      return;\n    }\n\n    const obj = {\n      TopicId: this.topicId,\n      Comment: this.commentForm.value.comment.trim()\n    };\n    this.blockUI.start('Saving data. Please wait...');\n\n    this._service.post('forum/post/save-or-update', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.topic.NoOfReplies = res.Data; // this.toastr.success(res.Message, 'Success!', { timeOut: 2000 });\n\n        this.submitted = false;\n        this.isCollapsedComment = !this.isCollapsedComment;\n        this.commentForm.reset();\n        this.setPage(0);\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  onSubmitReplyForm(item) {\n    this.replySubmitted = true;\n\n    if (this.replyForm.invalid) {\n      return;\n    }\n\n    const obj = {\n      Reply: this.replyForm.value.comment.trim(),\n      PostId: item.Id\n    };\n    this.blockUI.start('Saving data. Please wait...');\n\n    this._service.post('forum/reply/save-or-update', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        } // this.toastr.success(res.Message, 'Success!', { timeOut: 2000 });\n\n\n        this.replySubmitted = false;\n        this.replyForm.reset();\n        item.IsCollapsed = !item.IsCollapsed;\n        item.Replies.push(res.Data.Reply);\n        this.topic.NoOfReplies = res.Data.NoOfReplies;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  onCollapse(item) {\n    if (item.IsCollapsed) this.postList.forEach(element => {\n      element.IsCollapsed = element.Id !== item.Id;\n    });else item.IsCollapsed = true;\n  }\n\n  onLikeButtonClick(item) {\n    this.blockUI.start('Saving data. Please wait...');\n\n    this._service.post('forum/post/' + item.Id + '/like-or-unlike').subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        item.Liked = res.Data.Liked;\n        item.NoOfLikes = res.Data.NoOfLikes;\n        item.NoOfLikes = res.Data.NoOfLikes;\n        this.topic.NoOfLikes = res.Data.TopicLikes;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  onTitleClick(id) {\n    if (this.popoverObj && this.popoverObj.UserId === id) return;\n    this.blockUI.start('Getting data. Please wait...');\n\n    this._service.get('trainee/get-user-profile/' + id).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.popoverObj = res.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n}\n\nForumDetailsComponent.ɵfac = function ForumDetailsComponent_Factory(t) {\n  return new (t || ForumDetailsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.Location));\n};\n\nForumDetailsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ForumDetailsComponent,\n  selectors: [[\"app-forum-details\"]],\n  decls: 69,\n  vars: 26,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-break\"], [1, \"btn\", \"btn-link\", \"text-decoration-none\", \"fs-4\", \"fw-bold\", \"btn-sm\", \"mb-2\", 3, \"click\"], [1, \"fs-4\", \"ai-arrow-left\", \"fs-base\", \"me-2\"], [1, \"mt-1\", \"mb-4\"], [1, \"row\"], [1, \"col-lg-8\", \"border-end\"], [1, \"border-bottom\"], [1, \"mb-1\", 3, \"innerHTML\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-start\", \"text-center\", \"text-sm-start\", \"py-2\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", \"py-1\", \"px-2\", \"fs-7\"], [\"type\", \"button\", \"class\", \"btn btn-outline-primary btn-sm me-2 py-1 px-2 fs-7\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"position-relative\", \"g-0\", \"align-items-center\", \"border-top\", \"border-bottom\", \"mb-4\"], [1, \"col-md-6\", \"py-3\", \"pe-md-3\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\", \"justify-content-md-start\"], [1, \"d-flex\", \"align-items-center\", \"me-grid-gutter\"], [\"class\", \"rounded-circle me-1\", \"src\", \"assets/img/user/male.jpg\", \"width\", \"64\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"rounded-circle me-1\", \"src\", \"assets/img/user/female.jpg\", \"width\", \"64\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"rounded-circle me-1\", \"src\", \"assets/img/user/other.jpg\", \"width\", \"64\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"rounded-circle me-1\", \"width\", \"64\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"ps-2\"], [1, \"text-nowrap\"], [1, \"fs-sm\", \"mb-n1\"], [\"containerClass\", \"custom-popover\", \"container\", \"body\", 1, \"text-primary\", \"cursor-pointer\", 3, \"popover\", \"outsideClick\", \"onShown\"], [1, \"fs-xs\", \"text-muted\"], [1, \"d-none\", \"d-md-block\", \"position-absolute\", \"border-start\", \"h-100\", \"fc-style-1\"], [1, \"col-md-6\", \"ps-md-3\", \"py-3\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\", \"justify-content-md-end\"], [\"class\", \"btn-comment cursor-default\", \"type\", \"button\", 4, \"ngIf\"], [1, \"col-md-8\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-2\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-0\", \"text-nowrap\"], [1, \"col-md-4\"], [\"class\", \"d-flex align-items-center justify-content-center\", 4, \"ngIf\"], [\"class\", \"d-sm-flex align-items-center justify-content-end text-center text-sm-start\", 4, \"ngIf\"], [1, \"col-md-12\"], [\"id\", \"collapseComment\", 1, \"collapse\", \"py-3\", \"pe-md-3\", 3, \"collapse\", \"isAnimated\"], [\"autocomplete\", \"off\", 1, \"needs-validation\", \"bg-light\", \"rounded-3\", \"shadow\", \"p-4\", 3, \"formGroup\"], [1, \"mb-3\"], [\"for\", \"com-text\", 1, \"form-label\"], [1, \"text-danger\", \"ms-1\"], [\"id\", \"com-text\", \"rows\", \"4\", \"placeholder\", \"Write your comment  here\", \"formControlName\", \"comment\", \"required\", \"\", 1, \"form-control\"], [\"class\", \"error-text\", 4, \"ngIf\"], [1, \"col-4\", \"float-end\"], [1, \"d-grid\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"py-3\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-4\", \"sidebar\", \"bg-secondary\", \"pt-5\", \"ps-lg-4\", \"pb-md-2\", \"border-end\"], [\"class\", \"widget mt-n1\", 4, \"ngIf\"], [\"popTemplate\", \"\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"me-2\", \"py-1\", \"px-2\", \"fs-7\"], [\"src\", \"assets/img/user/male.jpg\", \"width\", \"64\", 1, \"rounded-circle\", \"me-1\", 3, \"alt\"], [\"src\", \"assets/img/user/female.jpg\", \"width\", \"64\", 1, \"rounded-circle\", \"me-1\", 3, \"alt\"], [\"src\", \"assets/img/user/other.jpg\", \"width\", \"64\", 1, \"rounded-circle\", \"me-1\", 3, \"alt\"], [\"width\", \"64\", 1, \"rounded-circle\", \"me-1\", 3, \"src\", \"alt\"], [\"type\", \"button\", 1, \"btn-comment\", \"cursor-default\"], [1, \"far\", \"fa-comment-alt\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\"], [\"aria-controls\", \"collapseComment\", 1, \"btn\", \"btn-primary\", \"d-block\", \"w-100\", 3, \"click\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-end\", \"text-center\", \"text-sm-start\"], [1, \"badge\", \"bg-success\", \"me-2\", \"px-3\", \"py-2\", \"fs-6\", \"rounded-3\", 3, \"ngClass\"], [1, \"error-text\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [1, \"comment\"], [3, \"innerHTML\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"d-flex\", \"align-items-center\"], [\"class\", \"rounded-circle\", \"src\", \"assets/img/user/male.jpg\", \"width\", \"42\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"rounded-circle\", \"src\", \"assets/img/user/female.jpg\", \"width\", \"42\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"rounded-circle\", \"src\", \"assets/img/user/other.jpg\", \"width\", \"42\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"rounded-circle\", \"width\", \"42\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"ps-2\", \"ms-1\"], [\"containerClass\", \"custom-popover\", \"container\", \"body\", 1, \"fs-sm\", \"mb-0\", \"cursor-pointer\", 3, \"popover\", \"outsideClick\", \"onShown\"], [\"type\", \"button\", 1, \"btn-comment-like\", \"fs-5\", \"fw-bold\", \"me-2\", 3, \"disabled\", \"click\"], [1, \"fa-thumbs-up\", \"fa-lg\", \"me-1\", 3, \"ngClass\"], [\"type\", \"button\", 1, \"btn-comment\", \"fs-5\", \"fw-bold\", \"cursor-default\"], [1, \"far\", \"fa-comment-alt\", \"fa-lg\", \"me-1\"], [\"class\", \"comment mt-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"text-end\", \"mt-3\"], [\"type\", \"button\", \"class\", \"btn btn-outline-primary btn-sm py-1\", 3, \"click\", 4, \"ngIf\"], [1, \"col-12\", 3, \"id\", \"collapse\", \"isAnimated\"], [\"autocomplete\", \"off\", 1, \"needs-validation\", \"bg-light\", \"rounded-3\", \"shadow\", 3, \"formGroup\"], [\"id\", \"com-text\", \"rows\", \"4\", \"placeholder\", \"Write your reply here\", \"formControlName\", \"comment\", \"required\", \"\", 1, \"form-control\"], [\"src\", \"assets/img/user/male.jpg\", \"width\", \"42\", 1, \"rounded-circle\", 3, \"alt\"], [\"src\", \"assets/img/user/female.jpg\", \"width\", \"42\", 1, \"rounded-circle\", 3, \"alt\"], [\"src\", \"assets/img/user/other.jpg\", \"width\", \"42\", 1, \"rounded-circle\", 3, \"alt\"], [\"width\", \"42\", 1, \"rounded-circle\", 3, \"src\", \"alt\"], [1, \"comment\", \"mt-3\"], [1, \"d-inline-flex\", \"align-items-center\"], [\"containerClass\", \"custom-popover\", \"container\", \"body\", 1, \"fw-bold\", \"text-primary\", \"cursor-pointer\", 3, \"popover\", \"outsideClick\", \"onShown\"], [1, \"text-muted\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"py-1\", 3, \"click\"], [1, \"ai-corner-up-left\", \"fs-base\", \"me-2\", \"ms-n1\"], [1, \"widget\", \"mt-n1\"], [1, \"h3\", \"pb-1\"], [\"class\", \"d-flex align-items-center pb-1 mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\", \"pb-1\", \"mb-3\"], [1, \"ms-1\"], [1, \"fs-md\", \"nav-heading\", \"mb-1\"], [\"routerLinkActive\", \"router-link-active\", 1, \"fw-medium\", \"fs-5\", 3, \"routerLink\"], [\"class\", \"card wid-100 mb-0\", 4, \"ngIf\"], [1, \"card\", \"wid-100\", \"mb-0\"], [1, \"card-header\", \"p-2\"], [1, \"card-title\", \"mb-0\"], [1, \"card-body\", \"p-2\"], [1, \"table\", \"table-sm\", \"table-borderless\", \"table-hover\", \"mb-0\"], [1, \"text-wrap\"]],\n  template: function ForumDetailsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"h1\", 4);\n      i0.ɵɵtext(6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"a\", 5);\n      i0.ɵɵlistener(\"click\", function ForumDetailsComponent_Template_a_click_7_listener() {\n        return ctx.backClicked();\n      });\n      i0.ɵɵelement(8, \"i\", 6);\n      i0.ɵɵtext(9, \"Go Back \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(10, \"hr\", 7);\n      i0.ɵɵelementStart(11, \"div\", 8);\n      i0.ɵɵelementStart(12, \"div\", 9);\n      i0.ɵɵelementStart(13, \"div\", 10);\n      i0.ɵɵelement(14, \"p\", 11);\n      i0.ɵɵpipe(15, \"safe\");\n      i0.ɵɵelementStart(16, \"div\", 12);\n      i0.ɵɵelementStart(17, \"button\", 13);\n      i0.ɵɵtext(18);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(19, ForumDetailsComponent_button_19_Template, 2, 1, \"button\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"div\", 15);\n      i0.ɵɵelementStart(21, \"div\", 16);\n      i0.ɵɵelementStart(22, \"div\", 17);\n      i0.ɵɵelementStart(23, \"div\", 18);\n      i0.ɵɵtemplate(24, ForumDetailsComponent_img_24_Template, 1, 1, \"img\", 19);\n      i0.ɵɵtemplate(25, ForumDetailsComponent_img_25_Template, 1, 1, \"img\", 20);\n      i0.ɵɵtemplate(26, ForumDetailsComponent_img_26_Template, 1, 1, \"img\", 21);\n      i0.ɵɵtemplate(27, ForumDetailsComponent_img_27_Template, 1, 2, \"img\", 22);\n      i0.ɵɵelementStart(28, \"div\", 23);\n      i0.ɵɵelementStart(29, \"div\", 24);\n      i0.ɵɵelementStart(30, \"h6\", 25);\n      i0.ɵɵtext(31, \" Posted by: \");\n      i0.ɵɵelementStart(32, \"span\", 26);\n      i0.ɵɵlistener(\"onShown\", function ForumDetailsComponent_Template_span_onShown_32_listener() {\n        return ctx.onTitleClick(ctx.topic.CreatorId);\n      });\n      i0.ɵɵtext(33);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(34, \"span\", 27);\n      i0.ɵɵtext(35);\n      i0.ɵɵpipe(36, \"amTimeAgo\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(37, \"div\", 28);\n      i0.ɵɵelementStart(38, \"div\", 29);\n      i0.ɵɵelementStart(39, \"div\", 30);\n      i0.ɵɵtemplate(40, ForumDetailsComponent_button_40_Template, 3, 1, \"button\", 31);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(41, \"div\", 8);\n      i0.ɵɵelementStart(42, \"div\", 32);\n      i0.ɵɵelementStart(43, \"div\", 33);\n      i0.ɵɵelementStart(44, \"h1\", 34);\n      i0.ɵɵtext(45, \"Comments\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(46, \"div\", 35);\n      i0.ɵɵtemplate(47, ForumDetailsComponent_div_47_Template, 3, 1, \"div\", 36);\n      i0.ɵɵtemplate(48, ForumDetailsComponent_div_48_Template, 3, 5, \"div\", 37);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(49, \"div\", 38);\n      i0.ɵɵelementStart(50, \"div\", 39);\n      i0.ɵɵelementStart(51, \"form\", 40);\n      i0.ɵɵelementStart(52, \"div\", 41);\n      i0.ɵɵelementStart(53, \"label\", 42);\n      i0.ɵɵtext(54, \" Your Comment \");\n      i0.ɵɵelementStart(55, \"sup\", 43);\n      i0.ɵɵtext(56, \"*\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(57, \"textarea\", 44);\n      i0.ɵɵtemplate(58, ForumDetailsComponent_div_58_Template, 3, 2, \"div\", 45);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(59, \"div\", 46);\n      i0.ɵɵelementStart(60, \"div\", 47);\n      i0.ɵɵelementStart(61, \"button\", 48);\n      i0.ɵɵlistener(\"click\", function ForumDetailsComponent_Template_button_click_61_listener() {\n        return ctx.onSubmitPostForm();\n      });\n      i0.ɵɵtext(62, \" Post \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(63, \"div\", 49);\n      i0.ɵɵtemplate(64, ForumDetailsComponent_div_64_Template, 39, 28, \"div\", 50);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(65, \"div\", 51);\n      i0.ɵɵtemplate(66, ForumDetailsComponent_div_66_Template, 4, 1, \"div\", 52);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(67, ForumDetailsComponent_ng_template_67_Template, 1, 1, \"ng-template\", null, 53, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      const _r11 = i0.ɵɵreference(68);\n\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate(ctx.topic.Title);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(15, 21, ctx.topic.Description, \"html\"), i0.ɵɵsanitizeHtml);\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate1(\" #\", ctx.topic.Category, \" \");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.topic.Tags);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", !ctx.topic.CreatorImage && ctx.topic.CreatorGender == \"Male\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.topic.CreatorImage && ctx.topic.CreatorGender == \"Female\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.topic.CreatorImage && ctx.topic.CreatorGender == \"Others\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.topic.CreatorImage);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"popover\", _r11)(\"outsideClick\", true);\n      i0.ɵɵadvance(1);\n      i0.ɵɵtextInterpolate1(\" \", ctx.topic.Creator, \" \");\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(36, 24, ctx.topic.CreatedDate), \" \");\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.topic.NoOfReplies);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngIf\", ctx.topic.Status === \"Open\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.topic.Status !== \"Open\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"collapse\", ctx.isCollapsedComment)(\"isAnimated\", true);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"formGroup\", ctx.commentForm);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.cf[\"comment\"].errors);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngForOf\", ctx.postList);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.popularList.length > 0);\n    }\n  },\n  directives: [i6.BlockUIComponent, i5.NgForOf, i5.NgIf, i7.PopoverDirective, i5.NgClass, i8.DefaultClassDirective, i9.CollapseDirective, i4.ɵNgNoValidate, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.DefaultValueAccessor, i4.NgControlStatus, i4.FormControlName, i4.RequiredValidator, i1.RouterLinkWithHref, i1.RouterLinkActive],\n  pipes: [i10.SafePipe, i11.TimeAgoPipe, i11.CalendarPipe],\n  styles: [\".custom-popover{width:800px!important;max-width:100%!important}.custom-popover .popover-body{padding:0}.custom-popover .card{background:#f1eee1;border-radius:.75rem}.custom-popover .card .card-header{background:#e7dcab!important;border-radius:.75rem .75rem 0 0}.custom-popover.bs-popover-top>.popover-arrow:after,.custom-popover.bs-popover-top>.popover-arrow:before{border-top-color:#e7dcab}.custom-popover.bs-popover-bottom>.popover-arrow:after,.custom-popover.bs-popover-bottom>.popover-arrow:before{border-bottom-color:#e7dcab}.custom-popover.bs-popover-start>.popover-arrow:after,.custom-popover.bs-popover-start>.popover-arrow:before{border-left-color:#e7dcab}.custom-popover.bs-popover-end>.popover-arrow:after,.custom-popover.bs-popover-end>.popover-arrow:before{border-right-color:#e7dcab}.fc-style-1{top:0;left:50%;width:1px}\\n\"],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], ForumDetailsComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}