{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { TemplateRef } from '@angular/core';\nimport { environment } from '../../environments/environment';\nimport { BlockUI } from 'ng-block-ui';\nimport { Timer } from 'src/app/_models/timer';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { moveItemInArray } from '@angular/cdk/drag-drop';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { DOCUMENT } from '@angular/common';\nimport { Page } from '../_models/page';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../_services/common.service\";\nimport * as i2 from \"ngx-smart-modal\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"src/app/_helpers/confirm-dialog/confirm.service\";\nimport * as i5 from \"../_services/authentication.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"../shared/pagination/pagination.service\";\nimport * as i8 from \"ngx-bootstrap/modal\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"ng-block-ui\";\nimport * as i11 from \"@angular/flex-layout/extended\";\nimport * as i12 from \"../shared/pagination/pagination.component\";\nimport * as i13 from \"ngx-bootstrap/accordion\";\nimport * as i14 from \"@angular/forms\";\nimport * as i15 from \"ngx-toggle-switch\";\nimport * as i16 from \"@angular/cdk/drag-drop\";\nimport * as i17 from \"@ng-select/ng-select\";\nimport * as i18 from \"ngx-bootstrap/rating\";\nimport * as i19 from \"ngx-moment\";\nimport * as i20 from \"../_helpers/safe-pipe\";\n\nfunction EvaluationTestComponent_div_3_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelementStart(1, \"div\", 17);\n    i0.ɵɵelementStart(2, \"app-pagination\", 18);\n    i0.ɵɵlistener(\"pageChange\", function EvaluationTestComponent_div_3_div_14_Template_app_pagination_pageChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return ctx_r7.onPageChange($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"totalItems\", ctx_r5.questionList.length)(\"paginationControlToShow\", 10)(\"itemsPerPage\", ctx_r5.page.size)(\"definitions\", ctx_r5.questionList)(\"currentPage\", ctx_r5.page.pageNumber)(\"mqAnswered\", ctx_r5.mqAnswered);\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_1_li_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵelement(1, \"i\", 26);\n    i0.ɵɵtext(2, \" Open in : \");\n    i0.ɵɵelementStart(3, \"b\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" to \");\n    i0.ɵɵelementStart(7, \"b\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(5, 2, ctx_r12.pagedetail.StartDate, \"MMM DD, YYYY hh:mm A\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 5, ctx_r12.pagedetail.EndDate, \"MMM DD, YYYY hh:mm A\"), \" \");\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_1_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelementStart(1, \"accordion\", 32);\n    i0.ɵɵelementStart(2, \"accordion-group\", 33);\n    i0.ɵɵelement(3, \"div\", 34);\n    i0.ɵɵpipe(4, \"safe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"isAnimated\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(4, 2, ctx_r13.pagedetail.ExamInstructions, \"html\"), i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelementStart(1, \"div\", 22);\n    i0.ɵɵelementStart(2, \"div\", 23);\n    i0.ɵɵelementStart(3, \"div\", 1);\n    i0.ɵɵelementStart(4, \"div\", 24);\n    i0.ɵɵelementStart(5, \"ul\", 25);\n    i0.ɵɵelementStart(6, \"li\");\n    i0.ɵɵelement(7, \"i\", 26);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"li\");\n    i0.ɵɵelement(10, \"i\", 26);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 24);\n    i0.ɵɵelementStart(13, \"ul\", 25);\n    i0.ɵɵelementStart(14, \"li\");\n    i0.ɵɵelement(15, \"i\", 26);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"li\");\n    i0.ɵɵelement(18, \"i\", 26);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 7);\n    i0.ɵɵelementStart(21, \"ul\", 27);\n    i0.ɵɵtemplate(22, EvaluationTestComponent_div_3_div_16_div_1_li_22_Template, 10, 8, \"li\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 15);\n    i0.ɵɵelementStart(24, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function EvaluationTestComponent_div_3_div_16_div_1_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(3);\n\n      const _r1 = i0.ɵɵreference(5);\n\n      return ctx_r14.openFeedBackModal(_r1);\n    });\n    i0.ɵɵelement(25, \"i\", 30);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, EvaluationTestComponent_div_3_div_16_div_1_div_27_Template, 5, 5, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" Total Marks : \", ctx_r9.pagedetail.Marks, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Question Type : \", ctx_r9.pagedetail.MCQOnly ? \"MCQ\" : \"Mixed\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" Total Duration : \", ctx_r9.getHourMint(ctx_r9.pagedetail.DurationMnt), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Pending Quota : \", ctx_r9.pagedetail.PendingQuota, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.pagedetail.StartDate && ctx_r9.pagedetail.EndDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r9.pagedetail.FeedbackGiven);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.pagedetail.FeedbackGiven ? \"Feedback Given\" : \"Give Your Feedback\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.pagedetail.ExamInstructions);\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_2_h3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 40);\n    i0.ɵɵtext(1, \" You have already appeared in the exam. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_2_h3_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.notAllowMessage, \" \");\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_2_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function EvaluationTestComponent_div_3_div_16_div_2_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(4);\n\n      const _r3 = i0.ɵɵreference(7);\n\n      return ctx_r19.startQuiz(_r3);\n    });\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵtext(2, \" Start Exam \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelementStart(1, \"div\", 36);\n    i0.ɵɵtemplate(2, EvaluationTestComponent_div_3_div_16_div_2_h3_2_Template, 2, 0, \"h3\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 38);\n    i0.ɵɵtemplate(4, EvaluationTestComponent_div_3_div_16_div_2_h3_4_Template, 3, 1, \"h3\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, EvaluationTestComponent_div_3_div_16_div_2_button_5_Template, 3, 0, \"button\", 39);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.pagedetail.Attempt > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.pagedetail.Allow);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.pagedetail.Allow);\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_3_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 60);\n    i0.ɵɵelementStart(3, \"p\", 61);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 62);\n    i0.ɵɵelementStart(6, \"p\", 61);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.questionList[ctx_r22.qIndex].Question, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" [\", ctx_r22.questionList[ctx_r22.qIndex].Mark, \"] \");\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_3_div_14_ul_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 65);\n    i0.ɵɵelementStart(1, \"input\", 66);\n    i0.ɵɵlistener(\"click\", function EvaluationTestComponent_div_3_div_16_div_3_div_14_ul_2_li_1_Template_input_click_1_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext(6);\n      return ctx_r31.selectSpecific(ctx_r31.questionList[ctx_r31.qIndex].Options);\n    })(\"ngModelChange\", function EvaluationTestComponent_div_3_div_16_div_3_div_14_ul_2_li_1_Template_input_ngModelChange_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const option_r29 = restoredCtx.$implicit;\n      return option_r29.Selected = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 67);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const option_r29 = ctx.$implicit;\n    const oi_r30 = ctx.index;\n    const ctx_r28 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"name\", \"radioGroup\" + ctx_r28.qIndex)(\"ngModel\", option_r29.Selected)(\"id\", \"option\" + oi_r30 + \"-\" + ctx_r28.qIndex)(\"value\", option_r29.Text);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", \"option\" + oi_r30 + \"-\" + ctx_r28.qIndex);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r29.Text, \" \");\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_3_div_14_ul_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 63);\n    i0.ɵɵtemplate(1, EvaluationTestComponent_div_3_div_16_div_3_div_14_ul_2_li_1_Template, 4, 6, \"li\", 64);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r23.questionList[ctx_r23.qIndex].Options);\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_3_div_14_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 60);\n    i0.ɵɵelementStart(3, \"p\", 61);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 62);\n    i0.ɵɵelementStart(6, \"p\", 61);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 68);\n    i0.ɵɵelementStart(9, \"div\", 1);\n    i0.ɵɵelementStart(10, \"div\", 7);\n    i0.ɵɵelementStart(11, \"ui-switch\", 69);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestComponent_div_3_div_16_div_3_div_14_div_3_Template_ui_switch_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext(5);\n      return ctx_r34.questionList[ctx_r34.qIndex].Answer = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 7);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r24.questionList[ctx_r24.qIndex].Question, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" [\", ctx_r24.questionList[ctx_r24.qIndex].Mark, \"] \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r24.questionList[ctx_r24.qIndex].Answer);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r24.questionList[ctx_r24.qIndex].Answer == true ? \"Selected True\" : \"Selected False\", \" \");\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_3_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 60);\n    i0.ɵɵelementStart(3, \"p\", 61);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 62);\n    i0.ɵɵelementStart(6, \"p\", 61);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 68);\n    i0.ɵɵelementStart(9, \"div\", 70);\n    i0.ɵɵelementStart(10, \"span\", 71);\n    i0.ɵɵtext(11, \"Answer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 72);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestComponent_div_3_div_16_div_3_div_14_div_4_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext(5);\n      return ctx_r36.questionList[ctx_r36.qIndex].Answer = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.questionList[ctx_r25.qIndex].Question, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" [\", ctx_r25.questionList[ctx_r25.qIndex].Mark, \"] \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r25.questionList[ctx_r25.qIndex].Answer);\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_3_div_14_div_5_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵelementStart(1, \"div\", 81);\n    i0.ɵɵelementStart(2, \"b\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 82);\n    i0.ɵɵelementStart(5, \"b\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r40 = ctx.$implicit;\n    const i_r41 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", i_r41 + 1, \". \", item_r40.LeftSide, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"[\", item_r40.Mark, \"]\");\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_3_div_14_div_5_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 85);\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_3_div_14_div_5_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵtemplate(1, EvaluationTestComponent_div_3_div_16_div_3_div_14_div_5_div_13_div_1_Template, 1, 0, \"div\", 84);\n    i0.ɵɵelementStart(2, \"b\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r42 = ctx.$implicit;\n    const i_r43 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", i_r43 + 1, \". \", item_r42, \"\");\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_3_div_14_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 7);\n    i0.ɵɵelementStart(3, \"div\", 73);\n    i0.ɵɵelementStart(4, \"div\", 45);\n    i0.ɵɵelementStart(5, \"h4\", 74);\n    i0.ɵɵtext(6, \"Matching Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 75);\n    i0.ɵɵelementStart(8, \"div\", 1);\n    i0.ɵɵelementStart(9, \"div\", 76);\n    i0.ɵɵtemplate(10, EvaluationTestComponent_div_3_div_16_div_3_div_14_div_5_div_10_Template, 7, 3, \"div\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 78);\n    i0.ɵɵelementStart(12, \"div\", 79);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function EvaluationTestComponent_div_3_div_16_div_3_div_14_div_5_Template_div_cdkDropListDropped_12_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext(5);\n      return ctx_r45.drop($event);\n    });\n    i0.ɵɵtemplate(13, EvaluationTestComponent_div_3_div_16_div_3_div_14_div_5_div_13_Template, 4, 2, \"div\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r26.questionList[ctx_r26.qIndex].LeftSides);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r26.questionList[ctx_r26.qIndex].RightSides);\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_3_div_14_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 60);\n    i0.ɵɵelementStart(3, \"p\", 61);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 62);\n    i0.ɵɵelementStart(6, \"p\", 61);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 68);\n    i0.ɵɵelementStart(9, \"div\", 70);\n    i0.ɵɵelementStart(10, \"span\", 71);\n    i0.ɵɵtext(11, \"Answer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"textarea\", 86);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestComponent_div_3_div_16_div_3_div_14_div_6_Template_textarea_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext(5);\n      return ctx_r47.questionList[ctx_r47.qIndex].Answer = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r27.questionList[ctx_r27.qIndex].Question, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" [\", ctx_r27.questionList[ctx_r27.qIndex].Mark, \"] \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r27.questionList[ctx_r27.qIndex].Answer);\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_3_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtemplate(1, EvaluationTestComponent_div_3_div_16_div_3_div_14_div_1_Template, 8, 3, \"div\", 57);\n    i0.ɵɵtemplate(2, EvaluationTestComponent_div_3_div_16_div_3_div_14_ul_2_Template, 2, 2, \"ul\", 58);\n    i0.ɵɵtemplate(3, EvaluationTestComponent_div_3_div_16_div_3_div_14_div_3_Template, 14, 5, \"div\", 57);\n    i0.ɵɵtemplate(4, EvaluationTestComponent_div_3_div_16_div_3_div_14_div_4_Template, 13, 4, \"div\", 57);\n    i0.ɵɵtemplate(5, EvaluationTestComponent_div_3_div_16_div_3_div_14_div_5_Template, 14, 3, \"div\", 57);\n    i0.ɵɵtemplate(6, EvaluationTestComponent_div_3_div_16_div_3_div_14_div_6_Template, 13, 4, \"div\", 57);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.questionList[ctx_r21.qIndex].Type === \"MCQ\" && ctx_r21.timer.isRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.questionList[ctx_r21.qIndex].Type === \"MCQ\" && ctx_r21.timer.isRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.questionList[ctx_r21.qIndex].Type === \"TFQ\" && ctx_r21.timer.isRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.questionList[ctx_r21.qIndex].Type === \"FIGQ\" && ctx_r21.timer.isRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.questionList[ctx_r21.qIndex].Type === \"LRMQ\" && ctx_r21.timer.isRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.questionList[ctx_r21.qIndex].Type === \"WQ\" && ctx_r21.timer.isRunning);\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelementStart(1, \"div\", 44);\n    i0.ɵɵelementStart(2, \"div\", 45);\n    i0.ɵɵelementStart(3, \"div\", 1);\n    i0.ɵɵelementStart(4, \"div\", 46);\n    i0.ɵɵelement(5, \"i\", 47);\n    i0.ɵɵelementStart(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 48);\n    i0.ɵɵelement(9, \"i\", 49);\n    i0.ɵɵelementStart(10, \"strong\");\n    i0.ɵɵtext(11, \" Time Left: \");\n    i0.ɵɵelementStart(12, \"b\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, EvaluationTestComponent_div_3_div_16_div_3_div_14_Template, 7, 6, \"div\", 50);\n    i0.ɵɵelementStart(15, \"div\", 51);\n    i0.ɵɵelementStart(16, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function EvaluationTestComponent_div_3_div_16_div_3_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r49 = i0.ɵɵnextContext(3);\n      return ctx_r49.prevQuestion();\n    });\n    i0.ɵɵelement(17, \"i\", 53);\n    i0.ɵɵtext(18, \" Prev \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function EvaluationTestComponent_div_3_div_16_div_3_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r51 = i0.ɵɵnextContext(3);\n\n      const _r3 = i0.ɵɵreference(7);\n\n      return ctx_r51.nextQuestion(_r3);\n    });\n    i0.ɵɵtext(20);\n    i0.ɵɵelement(21, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\" QUESTION \", ctx_r11.qIndex + 1, \" of \", ctx_r11.questionList.length, \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate3(\"\", ctx_r11.timer.hour, \"h : \", ctx_r11.timer.minute, \"m : \", ctx_r11.timer.second, \"s\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.timer.isRunning);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r11.qIndex === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.btnNextText, \" \");\n  }\n}\n\nfunction EvaluationTestComponent_div_3_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, EvaluationTestComponent_div_3_div_16_div_1_Template, 28, 8, \"div\", 20);\n    i0.ɵɵtemplate(2, EvaluationTestComponent_div_3_div_16_div_2_Template, 6, 3, \"div\", 21);\n    i0.ɵɵtemplate(3, EvaluationTestComponent_div_3_div_16_div_3_Template, 22, 8, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.quizRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.quizRunning);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.quizRunning);\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"col-lg-12\": a0\n  };\n};\n\nfunction EvaluationTestComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelementStart(1, \"div\", 6);\n    i0.ɵɵelementStart(2, \"div\", 7);\n    i0.ɵɵelementStart(3, \"div\", 8);\n    i0.ɵɵelementStart(4, \"div\", 9);\n    i0.ɵɵelementStart(5, \"p\", 10);\n    i0.ɵɵtext(6);\n    i0.ɵɵelement(7, \"br\");\n    i0.ɵɵelementStart(8, \"span\", 11);\n    i0.ɵɵtext(9, \"Evaluation Test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function EvaluationTestComponent_div_3_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r53);\n      const ctx_r52 = i0.ɵɵnextContext();\n      return ctx_r52.backClicked();\n    });\n    i0.ɵɵelement(11, \"i\", 13);\n    i0.ɵɵtext(12, \"Go Back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"hr\");\n    i0.ɵɵtemplate(14, EvaluationTestComponent_div_3_div_14_Template, 3, 6, \"div\", 14);\n    i0.ɵɵelementStart(15, \"div\", 15);\n    i0.ɵɵtemplate(16, EvaluationTestComponent_div_3_div_16_Template, 4, 3, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ctx_r0.quizRunning));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.pagedetail.ExamName, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.quizRunning);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.pagedetail);\n  }\n}\n\nconst _c1 = function () {\n  return {\n    standalone: true\n  };\n};\n\nfunction EvaluationTestComponent_ng_template_4_div_7_div_5_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r70 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵelementStart(1, \"input\", 110);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestComponent_ng_template_4_div_7_div_5_div_7_div_2_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r70);\n      const question_r58 = i0.ɵɵnextContext(2).$implicit;\n      return question_r58.Answers = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 67);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const radio_r66 = ctx.$implicit;\n    const r_r67 = ctx.index;\n    const question_r58 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"id\", \"q-ra-\", question_r58.Id, \"-\", r_r67, \"\");\n    i0.ɵɵproperty(\"value\", radio_r66)(\"ngModel\", question_r58.Answers)(\"ngModelOptions\", i0.ɵɵpureFunction0(8, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"for\", \"q-ra-\", question_r58.Id, \"-\", r_r67, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(radio_r66);\n  }\n}\n\nfunction EvaluationTestComponent_ng_template_4_div_7_div_5_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"div\", 107);\n    i0.ɵɵtemplate(2, EvaluationTestComponent_ng_template_4_div_7_div_5_div_7_div_2_Template, 4, 9, \"div\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const question_r58 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", question_r58.Options);\n  }\n}\n\nfunction EvaluationTestComponent_ng_template_4_div_7_div_5_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r78 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 112);\n    i0.ɵɵelementStart(1, \"input\", 113);\n    i0.ɵɵlistener(\"change\", function EvaluationTestComponent_ng_template_4_div_7_div_5_div_8_div_1_Template_input_change_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r78);\n      const ckbox_r74 = restoredCtx.$implicit;\n      const question_r58 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r76 = i0.ɵɵnextContext(3);\n      return ctx_r76.onChangeCheckBox($event, question_r58, ckbox_r74);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 67);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ckbox_r74 = ctx.$implicit;\n    const c_r75 = ctx.index;\n    const question_r58 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"id\", \"q-ck-\", question_r58.Id, \"-\", c_r75, \"\");\n    i0.ɵɵproperty(\"checked\", question_r58.Answers.indexOf(ckbox_r74) !== -1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"for\", \"q-ck-\", question_r58.Id, \"-\", c_r75, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ckbox_r74);\n  }\n}\n\nfunction EvaluationTestComponent_ng_template_4_div_7_div_5_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, EvaluationTestComponent_ng_template_4_div_7_div_5_div_8_div_1_Template, 4, 6, \"div\", 111);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const question_r58 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", question_r58.Options);\n  }\n}\n\nfunction EvaluationTestComponent_ng_template_4_div_7_div_5_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r83 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"ng-select\", 114);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestComponent_ng_template_4_div_7_div_5_div_9_Template_ng_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r83);\n      const question_r58 = i0.ɵɵnextContext().$implicit;\n      return question_r58.Answers = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const question_r58 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"clearable\", false)(\"ngModel\", question_r58.Answers)(\"ngModelOptions\", i0.ɵɵpureFunction0(5, _c1))(\"clearOnBackspace\", false)(\"items\", question_r58.Options);\n  }\n}\n\nfunction EvaluationTestComponent_ng_template_4_div_7_div_5_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r87 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"div\", 107);\n    i0.ɵɵelementStart(2, \"rating\", 115);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestComponent_ng_template_4_div_7_div_5_div_10_Template_rating_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const question_r58 = i0.ɵɵnextContext().$implicit;\n      return question_r58.Answers = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const question_r58 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", question_r58.Answers)(\"ngModelOptions\", i0.ɵɵpureFunction0(4, _c1))(\"max\", 5)(\"readonly\", false);\n  }\n}\n\nfunction EvaluationTestComponent_ng_template_4_div_7_div_5_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r91 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"div\", 107);\n    i0.ɵɵelementStart(2, \"textarea\", 116);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationTestComponent_ng_template_4_div_7_div_5_div_11_Template_textarea_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r91);\n      const question_r58 = i0.ɵɵnextContext().$implicit;\n      return question_r58.Answers = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const question_r58 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", question_r58.Answers)(\"ngModelOptions\", i0.ɵɵpureFunction0(2, _c1));\n  }\n}\n\nfunction EvaluationTestComponent_ng_template_4_div_7_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵelementStart(1, \"div\", 103);\n    i0.ɵɵelementStart(2, \"b\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 104);\n    i0.ɵɵelementStart(6, \"div\", 105);\n    i0.ɵɵtemplate(7, EvaluationTestComponent_ng_template_4_div_7_div_5_div_7_Template, 3, 1, \"div\", 106);\n    i0.ɵɵtemplate(8, EvaluationTestComponent_ng_template_4_div_7_div_5_div_8_Template, 2, 1, \"div\", 106);\n    i0.ɵɵtemplate(9, EvaluationTestComponent_ng_template_4_div_7_div_5_div_9_Template, 2, 6, \"div\", 106);\n    i0.ɵɵtemplate(10, EvaluationTestComponent_ng_template_4_div_7_div_5_div_10_Template, 3, 5, \"div\", 106);\n    i0.ɵɵtemplate(11, EvaluationTestComponent_ng_template_4_div_7_div_5_div_11_Template, 3, 3, \"div\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const question_r58 = ctx.$implicit;\n    const i_r59 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i_r59 + 1, \".\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", question_r58.Question, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", question_r58.QuestionType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Radio\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Checkbox\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Dropdown\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Rating\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Textbox\");\n  }\n}\n\nfunction EvaluationTestComponent_ng_template_4_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵelementStart(1, \"div\", 98);\n    i0.ɵɵelementStart(2, \"h5\", 99);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 100);\n    i0.ɵɵtemplate(5, EvaluationTestComponent_ng_template_4_div_7_div_5_Template, 12, 8, \"div\", 101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r56 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", item_r56.Group, \" - Feedbacks\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r56.Questions);\n  }\n}\n\nfunction EvaluationTestComponent_ng_template_4_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r94 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 117);\n    i0.ɵɵlistener(\"click\", function EvaluationTestComponent_ng_template_4_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r94);\n      const ctx_r93 = i0.ɵɵnextContext(2);\n      return ctx_r93.onSubmitFeedback();\n    });\n    i0.ɵɵelement(1, \"i\", 118);\n    i0.ɵɵtext(2, \" Submit \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EvaluationTestComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r96 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵelementStart(1, \"h4\", 88);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function EvaluationTestComponent_ng_template_4_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r96);\n      const ctx_r95 = i0.ɵɵnextContext();\n      return ctx_r95.modalHideFeedBack();\n    });\n    i0.ɵɵelement(4, \"i\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 91);\n    i0.ɵɵelementStart(6, \"form\", 92);\n    i0.ɵɵtemplate(7, EvaluationTestComponent_ng_template_4_div_7_Template, 6, 2, \"div\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 94);\n    i0.ɵɵelementStart(9, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function EvaluationTestComponent_ng_template_4_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r96);\n      const ctx_r97 = i0.ɵɵnextContext();\n      return ctx_r97.modalHideFeedBack();\n    });\n    i0.ɵɵelement(10, \"i\", 90);\n    i0.ɵɵtext(11, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, EvaluationTestComponent_ng_template_4_button_12_Template, 3, 0, \"button\", 96);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Feedbacks on \\\"\", ctx_r2.pagedetail.ExamName, \"\\\" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.feedBackQuestionList);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.feedBackQuestionList.length > 0);\n  }\n}\n\nconst _c2 = function (a0, a1) {\n  return {\n    \"bg-success\": a0,\n    \"bg-danger\": a1\n  };\n};\n\nfunction EvaluationTestComponent_ng_template_6_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 125);\n    i0.ɵɵelementStart(1, \"table\", 126);\n    i0.ɵɵelementStart(2, \"tbody\");\n    i0.ɵɵelementStart(3, \"tr\");\n    i0.ɵɵelementStart(4, \"th\");\n    i0.ɵɵtext(5, \"Score\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"tr\");\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Grade\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"tr\");\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Result\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵelementStart(17, \"span\", 127);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r98 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r98.result.Score, \" %\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r98.result.Grade);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c2, ctx_r98.result.Result === \"Passed\", ctx_r98.result.Result === \"Failed\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r98.result.Result);\n  }\n}\n\nfunction EvaluationTestComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r100 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 119);\n    i0.ɵɵelementStart(1, \"h4\", 120);\n    i0.ɵɵtext(2, \"Exam Result\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 91);\n    i0.ɵɵtemplate(4, EvaluationTestComponent_ng_template_6_div_4_Template, 19, 7, \"div\", 121);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 122);\n    i0.ɵɵelementStart(6, \"button\", 123);\n    i0.ɵɵlistener(\"click\", function EvaluationTestComponent_ng_template_6_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r100);\n      const ctx_r99 = i0.ɵɵnextContext();\n      return ctx_r99.modalHide();\n    });\n    i0.ɵɵelement(7, \"i\", 124);\n    i0.ɵɵtext(8, \" OK \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.result);\n  }\n}\n\nexport class EvaluationTestComponent {\n  constructor(document, _service, ngxSmartModalService, toastr, confirmService, authService, route, pageService, modalService, _location) {\n    this.document = document;\n    this._service = _service;\n    this.ngxSmartModalService = ngxSmartModalService;\n    this.toastr = toastr;\n    this.confirmService = confirmService;\n    this.authService = authService;\n    this.route = route;\n    this.pageService = pageService;\n    this.modalService = modalService;\n    this._location = _location;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.mqAnswered = false;\n    this.changeScreenCounter = 0;\n    this.quizRunning = false;\n    this.questionList = [];\n    this.qIndex = -1;\n    this.btnNextText = 'Next';\n    this.noOfAnsweredQs = 0;\n    this.mcqList = [];\n    this.timer = new Timer();\n    this.terminated = null;\n    this.resultList = [];\n    this.allowAnswerSubmit = false;\n    this.isConnected = true;\n    this.page = new Page();\n    this.answerStatus = '';\n    this.feedBackQuestionList = [];\n    this.modalFeedBackQuestionList = [];\n    this.modalLgConfig = {\n      class: 'gray modal-lg',\n      backdrop: 'static'\n    };\n    this.modalConfig = {\n      class: 'gray',\n      backdrop: 'static'\n    };\n    this.isSubmitting = false;\n    this.answerSubmitted = false;\n    this.examId = this.route.snapshot.paramMap.get('examId');\n    this.baseUrl = environment.baseUrl;\n    this.window = this.document.defaultView;\n    this.page.startsFrom = 1;\n    this.page.pageNumber = 1;\n    this.page.size = 1;\n  }\n\n  ngOnInit() {\n    this.answerSubmitted = false;\n    this.route.params.subscribe(params => {});\n    this.getEvaluationTestDetails();\n  }\n\n  ngAfterViewInit() {\n    if (!this.isSubmitting) {\n      // window.addEventListener('popstate', (event) => {\n      //   this.quizRunning ? alert(\"Your exam has been terminated!\") : true;\n      //   this.quizRunning ? this.saveAnswer(this.template, true) : true;\n      // });\n      this.popStateHandler = event => {\n        if (this.quizRunning) {\n          alert('Your exam has been terminated!');\n          this.saveAnswer(this.template);\n        }\n      };\n\n      window.addEventListener('popstate', this.popStateHandler);\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.popStateHandler) {\n      window.removeEventListener('popstate', this.popStateHandler);\n    }\n\n    if (this.timerSubscription) this.timerSubscription.unsubscribe();\n    if (this.interval) clearInterval(this.interval);\n  }\n\n  getHourMint(duration) {\n    return Math.floor(duration / 60) + 'h ' + duration % 60 + 'm';\n  }\n\n  getEvaluationTestDetails() {\n    this.blockUI.start('Getting data...');\n\n    this._service.get('evaluation-exam/get-exam-info/' + this.examId).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n\n          this._location.back();\n\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n\n          this._location.back();\n\n          return;\n        }\n\n        this.pagedetail = res.Data;\n        this.notAllowMessage = res.Message;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  startQuiz(template) {\n    this.blockUI.start('Starting exam. Please wait...');\n    this.template = template;\n\n    this._service.get('evaluation-exam/get-questions/' + this.examId).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.questionList = []; // **FIX: Use ISO string for proper datetime serialization**\n\n        this.quiz = {\n          StartDate: new Date().toISOString()\n        };\n        this.traineeEvaluationExamId = res.Data.TraineeEvaluationExamId;\n        res.Data.MCQs.forEach(element => {\n          this.questionList.push({\n            Id: element.Id,\n            Question: element.Question,\n            Options: [{\n              Text: element.Option1,\n              Selected: false\n            }, {\n              Text: element.Option2,\n              Selected: false\n            }, {\n              Text: element.Option3,\n              Selected: false\n            }, {\n              Text: element.Option4,\n              Selected: false\n            }],\n            Mark: element.Mark,\n            Type: 'MCQ'\n          });\n        });\n        res.Data.TrueFalsQs.forEach(x => {\n          this.questionList.push({\n            Id: x.Id,\n            Question: x.Question,\n            Mark: x.Mark,\n            Answer: false,\n            CorrectAnswer: null,\n            Type: 'TFQ'\n          });\n        });\n        res.Data.FIGQs.forEach(x => {\n          this.questionList.push({\n            Id: x.Id,\n            Question: x.Question,\n            Mark: x.Mark,\n            Answer: null,\n            Type: 'FIGQ'\n          });\n        });\n\n        if (res.Data.MatchingQs) {\n          this.questionList.push({\n            LeftSides: res.Data.MatchingQs.LeftSides,\n            RightSides: res.Data.MatchingQs.RightSides,\n            Type: 'LRMQ'\n          });\n        }\n\n        res.Data.WrittenQs.forEach(x => {\n          this.questionList.push({\n            Id: x.Id,\n            Question: x.Question,\n            Mark: x.Mark,\n            Answer: null,\n            Type: 'WQ'\n          });\n        });\n        this.qIndex = 0;\n        this.quizRunning = true;\n        this.allowAnswerSubmit = true; // this.preventWindowScreenChanges();\n\n        if (this.allowAnswerSubmit) {\n          this.examTimeChangeScreen(template);\n        }\n\n        this.timerSubscription = this.timer.start(this.pagedetail.DurationMnt * 60).subscribe(status => {\n          if (status === 'ended') {\n            this.onTimesUp(template);\n            this.timerSubscription.unsubscribe();\n          }\n        });\n      },\n      error: err => {\n        this.toastr.error(err.message || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: true,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  onPageChange(page) {\n    this.setPage(page);\n  }\n\n  drop(event) {\n    this.mqAnswered = true;\n    moveItemInArray(this.questionList[this.qIndex].RightSides, event.previousIndex, event.currentIndex);\n  }\n\n  setPage(pageNumber) {\n    this.page.pageNumber = pageNumber;\n    this.qIndex = pageNumber - 1;\n    this.btnNextText = this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';\n  }\n\n  onTimesUp(template) {\n    if (this.answerSubmitted) return; //  this.saveAnswerSheetIntoLocalStorage(true);\n\n    this.ngxSmartModalService.closeAll();\n    this.confirmService.close();\n    this.confirmService.confirm(\"Time's Up!!\", 'Your answer will get submitted automatically. ' + this.answerStatus, 'OK', null, true).subscribe(() => {\n      this.answerSubmitted = true;\n      this.saveAnswer(template, true).then(() => {\n        console.log('Answer saved on time up');\n      }).catch(error => {\n        console.error('Error saving answer on time up:', error);\n      });\n    });\n  }\n\n  onScreenChange(template) {\n    //  this.saveAnswerSheetIntoLocalStorage(true);\n    if (this.answerSubmitted) return;\n    this.ngxSmartModalService.closeAll();\n    this.confirmService.close();\n\n    if (!this.isSubmitting) {\n      this.confirmService.confirm('Your exam has been terminated!', 'Your answer will get submitted automatically. ' + this.answerStatus, 'OK', null, true).subscribe(() => {\n        this.answerSubmitted = true;\n        this.saveAnswer(template, true).then(() => {\n          console.log('Answer saved on screen change');\n        }).catch(error => {\n          console.error('Error saving answer on screen change:', error);\n        });\n      });\n    }\n  }\n\n  nextQuestion(template) {\n    if (this.qIndex < this.questionList.length - 1) this.qIndex++;else {\n      this.timer.pause();\n      this.submitAnswer(template);\n      return;\n    }\n    this.btnNextText = this.qIndex >= this.questionList.length - 1 ? 'Finish' : 'Next';\n    this.page.pageNumber = this.qIndex + 1;\n    this.pageService.currentPage.next(this.page.pageNumber);\n  }\n\n  prevQuestion() {\n    if (this.qIndex !== 0) this.qIndex--;\n    this.page.pageNumber = this.page.pageNumber - 1;\n    this.pageService.currentPage.next(this.page.pageNumber);\n  }\n\n  submitAnswer(template) {\n    if (this.answerSubmitted) return;\n    this.confirmService.confirm('Are you sure?', 'You are going to submit answer. ' + this.answerStatus, 'Yes, Submit Answer').subscribe(result => {\n      if (result) {\n        this.timer.pause();\n        this.answerSubmitted = true; // **UNIFIED: Activity tracking now handled in backend, single API call**\n\n        this.saveAnswer(template).then(() => {\n          console.log('Exam submitted successfully with unified activity tracking');\n        }).catch(error => {\n          console.error('Error in exam submission:', error);\n        });\n      } else {\n        this.timer.resume();\n      }\n    });\n  }\n\n  saveAnswer(template, autoSubmission = false) {\n    return new Promise((resolve, reject) => {\n      if (!this.isConnected) {\n        this.toastr.error('You have no internet connection', 'ALERT!', {\n          timeOut: 4000\n        });\n        this.timer.resume();\n        reject('No internet connection');\n        return;\n      }\n\n      let submittedMCQList = [],\n          submittedTFQList = [],\n          submittedFIGQList = [],\n          submittedLRMQList = [],\n          submittedWQList = [];\n      this.questionList.filter(x => x.Type === 'MCQ').forEach(element => {\n        submittedMCQList.push({\n          QuestionId: element.Id,\n          Answered: element.Options.map(function (x, i) {\n            if (x.Selected) return i + 1;else return 0;\n          }).filter(x => x > 0).join()\n        });\n      });\n      this.questionList.filter(x => x.Type === 'TFQ').forEach(element => {\n        submittedTFQList.push({\n          QuestionId: element.Id,\n          Answered: element.Answer,\n          CorrectAnswer: !element.Answer ? element.CorrectAnswer ? element.CorrectAnswer.trim() : element.CorrectAnswer : null\n        });\n      });\n      this.questionList.filter(x => x.Type === 'FIGQ').forEach(element => {\n        submittedFIGQList.push({\n          QuestionId: element.Id,\n          Answered: element.Answer ? element.Answer.trim() : element.Answer\n        });\n      });\n      let mQuestions = this.questionList.find(x => x.Type === 'LRMQ');\n      if (mQuestions) for (let i = 0; i < mQuestions.LeftSides.length; i++) {\n        const element = mQuestions.LeftSides[i];\n        submittedLRMQList.push({\n          QuestionId: element.Id,\n          Answered: mQuestions.RightSides[i]\n        });\n      }\n      this.questionList.filter(x => x.Type === 'WQ').forEach(element => {\n        submittedWQList.push({\n          QuestionId: element.Id,\n          Answered: element.Answer ? element.Answer.trim() : element.Answer\n        });\n      });\n      const obj = {\n        TraineeEvaluationExamId: this.traineeEvaluationExamId,\n        ExamId: this.examId,\n        // **FIX: Use ISO strings for proper datetime serialization**\n        StartTime: this.quiz.StartDate,\n        EndTime: new Date().toISOString(),\n        AutoSubmission: autoSubmission,\n        MCQList: submittedMCQList,\n        TFQList: submittedTFQList,\n        FIGQList: submittedFIGQList,\n        MatchingQList: submittedLRMQList,\n        WQList: submittedWQList,\n        Terminated: this.terminated\n      };\n      this.isSubmitting = true;\n      this.blockUI.start('Submitting answer. Please wait...');\n\n      this._service.post('evaluation-exam/save-answers', obj).subscribe({\n        next: res => {\n          this.quizRunning = false;\n          this.isSubmitting = false;\n\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.error(res.Message, 'Warning!', {\n              closeButton: true,\n              disableTimeOut: true,\n              enableHtml: true\n            }); // this.timer.resume();\n\n            this._location.back();\n\n            reject(res.Message);\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              closeButton: true,\n              disableTimeOut: true,\n              enableHtml: true\n            }); // this.timer.resume();\n\n            this._location.back();\n\n            reject(res.Message);\n            return;\n          }\n\n          this.toastr.success(res.Message, 'Answer Submission!', {\n            timeOut: 4000\n          });\n          this.allowAnswerSubmit = false;\n          if (!res.Data) this._location.back();else {\n            this.result = res.Data;\n            this.modalRef = this.modalService.show(template, this.modalConfig);\n          }\n          resolve(res);\n        },\n        error: err => {\n          this.quizRunning = false;\n          this.isSubmitting = false;\n          this.timer.resume();\n          this.toastr.error(err.message || err, 'Error!', {\n            closeButton: true,\n            disableTimeOut: true,\n            enableHtml: true\n          });\n          this.blockUI.stop();\n\n          this._location.back();\n\n          reject(err);\n        },\n        complete: () => {\n          this.isSubmitting = false;\n          this.blockUI.stop();\n        }\n      });\n    });\n  }\n\n  backClicked() {\n    if (this.quizRunning) {\n      this.saveAnswer(this.template, true).then(() => {\n        console.log('Answer saved on back click');\n      }).catch(error => {\n        console.error('Error saving answer on back click:', error);\n      });\n    } else {\n      this._location.back();\n    }\n  }\n\n  selectSpecific(lists) {\n    lists.map(x => x.Selected = false);\n  }\n\n  openFeedBackModal(template) {\n    this.modalFeedBackQuestionList = [];\n\n    if (this.feedBackQuestionList.length === 0) {\n      this.blockUI.start('Getting feedback questions. Pleae wait...');\n\n      this._service.get('evaluation-exam/get-feedback-questions/' + this.pagedetail.Id).subscribe({\n        next: res => {\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              timeOut: 2000\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              timeOut: 2000\n            });\n            return;\n          }\n\n          this.feedBackQuestionList = res.Data;\n          this.feedBackQuestionList.forEach(group => {\n            group.Questions.forEach(element => {\n              if (element.QuestionType !== 'Checkbox') element.Answers = element.Answers.length > 0 ? element.Answers.join() : null;\n              if (element.QuestionType === 'Rating') element.Answers = element.Answers ? Number(element.Answers) : null;\n            });\n          });\n          this.modalFeedBackQuestionList = this.feedBackQuestionList;\n        },\n        error: err => {\n          this.toastr.warning(err.Messaage || err, 'Warning!', {\n            closeButton: true,\n            disableTimeOut: false\n          });\n          this.blockUI.stop();\n        },\n        complete: () => {\n          this.blockUI.stop();\n        }\n      });\n    } else {\n      this.modalFeedBackQuestionList = this.feedBackQuestionList;\n    }\n\n    this.modalRef = this.modalService.show(template, this.modalLgConfig);\n  }\n\n  modalHideFeedBack() {\n    this.modalFeedBackQuestionList = [];\n    this.modalRef.hide();\n  }\n\n  onChangeCheckBox(event, question, option) {\n    if (event.target.checked) {\n      question.Answers.push(option);\n    } else question.Answers.splice(question.Answers.indexOf(option), 1);\n  }\n\n  onSubmitFeedback() {\n    let feedbacks = [];\n    this.modalFeedBackQuestionList.forEach(group => {\n      group.Questions.forEach(element => {\n        if (element.QuestionType !== 'Checkbox') element.Answers = element.Answers ? [element.Answers.toString()] : [];\n        feedbacks.push({\n          QuestionId: element.Id,\n          Answers: element.Answers\n        });\n      });\n    });\n    const obj = {\n      ExamId: this.pagedetail.Id,\n      Feedbacks: feedbacks\n    };\n    this.blockUI.start('Saving feedbacks. Please wait...');\n\n    this._service.post('feedback/learning-hour/save-or-update', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, 'SUCCESS!', {\n          timeOut: 2000\n        });\n        this.feedBackQuestionList = [];\n        this.pagedetail.FeedbackGiven = true;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        this.blockUI.stop();\n      },\n      complete: () => {\n        this.blockUI.stop();\n        this.modalHideFeedBack();\n      }\n    });\n  } // preventWindowScreenChanges()\n  // {\n  //   this.window.addEventListener(\"beforeunload\", function (e) {\n  //     var confirmationMessage = 'It looks like you have not finishes the exam yet! '\n  //                             + 'If you leave before finishing, your exam will be expired.';\n  //     (e || window.event).returnValue = confirmationMessage; //Gecko + IE\n  //     // this.alert();\n  //     return confirmationMessage; //Gecko + Webkit, Safari, Chrome etc.\n  // });\n  // }\n\n\n  examTimeChangeScreen(template) {\n    document.addEventListener('visibilitychange', event => {\n      if (document.visibilityState != 'visible') {\n        if (this.changeScreenCounter === 0 && this.allowAnswerSubmit == true) {\n          alert('You are not allowed to change the screen!!! If you try one more step to change the screen the exam will be terminated!!!');\n          this.changeScreenCounter++;\n        } else if (this.changeScreenCounter === 1 && this.allowAnswerSubmit == true) {\n          this.terminated = true;\n          this.onScreenChange(template);\n          this.timerSubscription.unsubscribe();\n          this.quizRunning = false;\n          this.changeScreenCounter++;\n          this.allowAnswerSubmit;\n        }\n      }\n    });\n  }\n\n  modalHide() {\n    this.modalRef.hide();\n\n    this._location.back();\n  }\n\n}\n\nEvaluationTestComponent.ɵfac = function EvaluationTestComponent_Factory(t) {\n  return new (t || EvaluationTestComponent)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i1.CommonService), i0.ɵɵdirectiveInject(i2.NgxSmartModalService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i4.ConfirmService), i0.ɵɵdirectiveInject(i5.AuthenticationService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i7.PaginationService), i0.ɵɵdirectiveInject(i8.BsModalService), i0.ɵɵdirectiveInject(i9.Location));\n};\n\nEvaluationTestComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: EvaluationTestComponent,\n  selectors: [[\"app-evaluation-test\"]],\n  viewQuery: function EvaluationTestComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tpl = _t.first);\n    }\n  },\n  decls: 8,\n  vars: 1,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"row\"], [\"class\", \"col p-5\", 3, \"ngClass\", 4, \"ngIf\"], [\"templateFeedBackModal\", \"\"], [\"template\", \"\"], [1, \"col\", \"p-5\", 3, \"ngClass\"], [1, \"row\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"col-12\"], [1, \"pt-2\", \"p-md-3\"], [1, \"col-12\", \"d-flex\", \"justify-content-between\"], [1, \"h3\", \"text-break\", \"mb-0\"], [1, \"fw-bold\", \"text-uppercase\", \"fs-6\"], [1, \"btn\", \"btn-link\", \"text-decoration-none\", \"fs-4\", \"fw-bold\", \"btn-sm\", \"d-flex\", \"align-items-center\", \"border-start\", 3, \"click\"], [1, \"fs-4\", \"ai-arrow-left\", \"fs-base\", \"me-2\"], [\"class\", \"col-12 mb-3\", 4, \"ngIf\"], [1, \"col-12\", \"mb-3\"], [\"class\", \"section\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"totalItems\", \"paginationControlToShow\", \"itemsPerPage\", \"definitions\", \"currentPage\", \"mqAnswered\", \"pageChange\"], [1, \"section\"], [\"class\", \"col-12\", 4, \"ngIf\"], [\"class\", \"col-12 text-center\", 4, \"ngIf\"], [1, \"font-weight-bold\"], [1, \"widget\", \"widget_recent_post\"], [1, \"col-lg-6\", \"col-12\"], [1, \"list_none\", \"blog_meta\"], [1, \"fa\", \"fa-arrow-right\", \"me-2\"], [1, \"list_none\", \"blog_meta\", \"mb-4\", \"mt-2\"], [4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-mini\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-tasks\"], [1, \"text-primary\"], [3, \"isAnimated\"], [\"heading\", \"EXAM INSTRUCTIONS (click here to see)\", \"panelClass\", \"custom-accordion\"], [3, \"innerHTML\"], [1, \"col-12\", \"text-center\"], [1, \"bg-success\", \"rounded-3\"], [\"class\", \"p-3 text-white\", 4, \"ngIf\"], [1, \"bg-danger\", \"rounded-3\"], [\"type\", \"button\", \"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"p-3\", \"text-white\"], [1, \"fa\", \"fa-exclamation-triangle\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-check\"], [1, \"card\", \"q-panel\", \"noselect\"], [1, \"card-header\"], [1, \"col-lg-8\", \"col-md-6\", \"col-12\", \"qs-counter\"], [1, \"fa-solid\", \"fa-circle-question\"], [1, \"col-lg-4\", \"col-md-6\", \"col-12\", \"text-end\", \"timer\"], [1, \"fa-solid\", \"fa-clock\"], [\"class\", \"question-body noselect\", 4, \"ngIf\"], [1, \"card-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-info\", \"btn-box\", \"me-1\", 3, \"disabled\", \"click\"], [1, \"fa\", \"fa-arrow-left\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", 3, \"click\"], [1, \"fa\", \"fa-arrow-right\"], [1, \"question-body\", \"noselect\"], [\"class\", \"card-body\", 4, \"ngIf\"], [\"class\", \"list-group\", 4, \"ngIf\"], [1, \"card-body\"], [1, \"col-sm-11\", \"col-10\"], [1, \"text-question\", \"mb-0\"], [1, \"col-sm-1\", \"col-2\"], [1, \"list-group\"], [\"class\", \"list-group-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\"], [\"type\", \"radio\", 1, \"form-check-input\", 3, \"name\", \"ngModel\", \"id\", \"value\", \"click\", \"ngModelChange\"], [1, \"form-check-label\", 3, \"for\"], [1, \"col-sm-12\", \"mt-2\"], [\"uncheckedLabel\", \"Selected False\", \"checkedLabel\", \"Selected True\", \"size\", \"small\", \"defaultBgColor\", \"#E82D4C\", 3, \"ngModel\", \"ngModelChange\"], [1, \"input-group\", \"mb-3\"], [1, \"input-group-text\"], [\"type\", \"text\", \"placeholder\", \"Write answer ..\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"card\"], [1, \"text-bold\"], [1, \"card-body\", \"pt-3\"], [1, \"col-sm-7\", \"col-12\", \"pb-2\"], [\"class\", \"row\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-sm-5\", \"col-12\"], [\"cdkDropList\", \"\", 1, \"example-list\", 3, \"cdkDropListDropped\"], [\"class\", \"example-box\", \"cdkDrag\", \"\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-sm-11\", \"col-xs-11\", \"example-box\"], [1, \"col-sm-1\", \"col-xs-1\", \"example-box\"], [\"cdkDrag\", \"\", 1, \"example-box\"], [\"class\", \"example-custom-placeholder\", 4, \"cdkDragPlaceholder\"], [1, \"example-custom-placeholder\"], [\"rows\", \"5\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"modal-header\"], [1, \"modal-title\", \"float-start\"], [\"type\", \"button \", \"aria-label\", \"Close \", 1, \"close\", \"btn\", \"btn-mini\", \"btn-outline-danger\", \"float-end\", 3, \"click\"], [1, \"fa\", \"fa-close\"], [1, \"modal-body\"], [\"autocomplete\", \"off\", 1, \"col-12\"], [\"class\", \"card card-active mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"modal-footer\"], [1, \"btn\", \"btn-outline-danger\", \"me-2\", 3, \"click\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"card\", \"card-active\", \"mb-3\"], [1, \"card-header\", \"py-2\", \"bg-primary\", \"text-dark\", \"bg-opacity-25\"], [1, \"card-title\", \"mb-0\"], [1, \"card-body\", \"py-2\"], [\"class\", \"row mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"mb-3\"], [1, \"col-lg-7\", \"col-md-6\", \"col-12\"], [1, \"col-lg-5\", \"col-md-4\", \"col-12\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [1, \"input-group\"], [\"class\", \"form-check form-check-inline\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-check\", \"form-check-inline\"], [\"type\", \"radio\", \"name\", \"radio\", 1, \"form-check-input\", 3, \"id\", \"value\", \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [\"class\", \"form-check mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-check\", \"mb-1\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"id\", \"checked\", \"change\"], [\"placeholder\", \"Select\", 1, \"rounded-2\", \"w-100\", 3, \"clearable\", \"ngModel\", \"ngModelOptions\", \"clearOnBackspace\", \"items\", \"ngModelChange\"], [1, \"fs-3\", \"text-gold\", 3, \"ngModel\", \"ngModelOptions\", \"max\", \"readonly\", \"ngModelChange\"], [\"rows\", \"3\", \"placeholder\", \"Write anser here\", 1, \"form-control\", \"px-2\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fa\", \"fa-save\"], [1, \"modal-header\", \"d-flex\", \"justify-content-center\"], [1, \"modal-title\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"modal-footer\", \"d-flex\", \"justify-content-center\", \"py-1\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"fa\", \"fa-check\"], [1, \"table-responsive\"], [1, \"table\", \"table-sm\", \"mb-0\", \"table-hover\", \"table-borderless\"], [1, \"badge\", 3, \"ngClass\"]],\n  template: function EvaluationTestComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵtemplate(3, EvaluationTestComponent_div_3_Template, 17, 6, \"div\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(4, EvaluationTestComponent_ng_template_4_Template, 13, 3, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(6, EvaluationTestComponent_ng_template_6_Template, 9, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.pagedetail);\n    }\n  },\n  directives: [i10.BlockUIComponent, i9.NgIf, i9.NgClass, i11.DefaultClassDirective, i12.PaginationComponent, i13.AccordionComponent, i13.AccordionPanelComponent, i9.NgForOf, i14.RadioControlValueAccessor, i14.DefaultValueAccessor, i14.NgControlStatus, i14.NgModel, i15.UiSwitchComponent, i16.CdkDropList, i16.CdkDrag, i16.CdkDragPlaceholder, i14.ɵNgNoValidate, i14.NgControlStatusGroup, i14.NgForm, i9.NgSwitch, i9.NgSwitchCase, i17.NgSelectComponent, i18.RatingComponent],\n  pipes: [i19.DateFormatPipe, i20.SafePipe],\n  styles: [\".example-list[_ngcontent-%COMP%]{width:100%;max-width:100%;border:solid 1px #ccc;display:block;background:white;border-radius:4px;overflow:hidden}.example-box[_ngcontent-%COMP%]{padding:10px;border-bottom:solid 1px #ccc;color:#000000de;display:flex;flex-direction:row;align-items:center;justify-content:space-between;box-sizing:border-box;cursor:move;background:white;font-size:14px}.cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px #0003,0 8px 10px 1px #00000024,0 3px 14px 2px #0000001f}.cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.example-box[_ngcontent-%COMP%]:last-child{border:none}.example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.example-custom-placeholder[_ngcontent-%COMP%]{background:#ccc;border:dotted 3px #999;min-height:60px;transition:transform .25s cubic-bezier(0,0,.2,1)}.text-question[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#423838}.timer[_ngcontent-%COMP%]{font-size:16px;color:red;text-align:right}.timer[_ngcontent-%COMP%]   b[_ngcontent-%COMP%]{font-weight:900}@media (max-width: 768px){.timer[_ngcontent-%COMP%]{text-align:center;margin-top:5px;border-top:1px solid #8383a1;display:block}}.q-panel[_ngcontent-%COMP%], .q-panel[_ngcontent-%COMP%]   .list-group-item[_ngcontent-%COMP%]{background:#d7f4ff;font-size:19px;font-weight:600;color:#423838}.q-panel[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%]{font-size:19px!important;color:#423838}\"],\n  data: {\n    animation: [trigger('inOutAnimation', [transition(':enter', [style({\n      height: 0,\n      opacity: 0\n    }), animate('0.5s ease-out', style({\n      height: 50,\n      opacity: 1\n    }))]), transition(':leave', [style({\n      height: 50,\n      opacity: 1\n    }), animate('0.5s ease-in', style({\n      height: 0,\n      opacity: 0\n    }))])])]\n  }\n});\n\n__decorate([BlockUI()], EvaluationTestComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}