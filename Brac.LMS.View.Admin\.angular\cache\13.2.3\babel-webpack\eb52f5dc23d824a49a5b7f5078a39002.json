{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { TimeWiseLearningHourStudyReportRoutingModule } from './time-wise-learning-hour-study-report-routing.module';\nimport { PdfJsViewerModule } from 'ng2-pdfjs-viewer';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';\nimport * as i0 from \"@angular/core\";\nexport let TimeWiseLearningHourStudyReportModule = /*#__PURE__*/(() => {\n  class TimeWiseLearningHourStudyReportModule {}\n\n  TimeWiseLearningHourStudyReportModule.ɵfac = function TimeWiseLearningHourStudyReportModule_Factory(t) {\n    return new (t || TimeWiseLearningHourStudyReportModule)();\n  };\n\n  TimeWiseLearningHourStudyReportModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TimeWiseLearningHourStudyReportModule\n  });\n  TimeWiseLearningHourStudyReportModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, TimeWiseLearningHourStudyReportRoutingModule, SharedModule, PdfJsViewerModule, NgxExtendedPdfViewerModule]]\n  });\n  return TimeWiseLearningHourStudyReportModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}