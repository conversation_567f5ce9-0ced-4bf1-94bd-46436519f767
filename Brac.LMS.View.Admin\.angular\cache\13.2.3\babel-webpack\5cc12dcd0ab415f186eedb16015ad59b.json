{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ColumnMode } from '@swimlane/ngx-datatable';\nimport { Validators } from '@angular/forms';\nimport { BlockUI } from 'ng-block-ui';\nimport { environment } from '../../environments/environment';\nimport { UploadDialogComponent } from '../_helpers/upload-dialog/dialog.component';\nimport { ResponseStatus } from '../_models/enum';\nimport { concat, of, Subject } from \"rxjs\";\nimport { debounceTime, distinctUntilChanged, tap, switchMap, catchError } from \"rxjs/operators\";\nimport { Page } from \"../_models/page\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../app.component\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../_services/common.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"ngx-bootstrap/modal\";\nimport * as i7 from \"ng-block-ui\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@ng-select/ng-select\";\nimport * as i10 from \"@angular/router\";\nimport * as i11 from \"@swimlane/ngx-datatable\";\nimport * as i12 from \"ngx-moment\";\n\nfunction TraineeCertificateTestListComponent_ng_select_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 40, 41);\n    i0.ɵɵlistener(\"click\", function TraineeCertificateTestListComponent_ng_select_13_Template_ng_select_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n\n      const _r19 = i0.ɵɵreference(1);\n\n      const ctx_r20 = i0.ɵɵnextContext();\n      return ctx_r20.handleSelectClick(_r19);\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r0.courseList);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_div_14_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 44);\n    i0.ɵɵtext(1, \"Course is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TraineeCertificateTestListComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtemplate(1, TraineeCertificateTestListComponent_div_14_span_1_Template, 2, 0, \"span\", 43);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.courseId.errors.required);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_26_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 53);\n  }\n\n  if (rf & 2) {\n    const item_r23 = i0.ɵɵnextContext().item;\n    const ctx_r25 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r25.baseUrl + item_r23.ImagePath, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_26_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 54);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_26_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 55);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_26_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 56);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_26_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 57);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, TraineeCertificateTestListComponent_ng_template_26_img_1_Template, 1, 1, \"img\", 46);\n    i0.ɵɵtemplate(2, TraineeCertificateTestListComponent_ng_template_26_img_2_Template, 1, 0, \"img\", 47);\n    i0.ɵɵtemplate(3, TraineeCertificateTestListComponent_ng_template_26_img_3_Template, 1, 0, \"img\", 48);\n    i0.ɵɵtemplate(4, TraineeCertificateTestListComponent_ng_template_26_img_4_Template, 1, 0, \"img\", 49);\n    i0.ɵɵtemplate(5, TraineeCertificateTestListComponent_ng_template_26_img_5_Template, 1, 0, \"img\", 50);\n    i0.ɵɵelementStart(6, \"div\", 51);\n    i0.ɵɵelementStart(7, \"h5\", 52);\n    i0.ɵɵelementStart(8, \"b\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"br\");\n    i0.ɵɵelementStart(11, \"b\");\n    i0.ɵɵtext(12, \"PIN:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelement(14, \"br\");\n    i0.ɵɵelementStart(15, \"b\");\n    i0.ɵɵtext(16, \"Division :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r23 = ctx.item;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r23.ImagePath);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r23.ImagePath && !item_r23.Gender);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r23.ImagePath && item_r23.Gender == \"Others\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r23.ImagePath && item_r23.Gender == \"Male\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r23.ImagePath && item_r23.Gender == \"Female\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r23.Name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r23.PIN, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r23.Division, \" \");\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r31 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r31);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r31);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r32 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r32);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r32);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r33 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r33);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r33);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_46_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵpipe(1, \"amDateFormat\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r34 = i0.ɵɵnextContext().value;\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind2(1, 2, value_r34, \"MMM DD, YYYY hh:mmA\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 5, value_r34, \"MMM DD, YYYY hh:mmA\"), \" \");\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TraineeCertificateTestListComponent_ng_template_46_span_0_Template, 4, 8, \"span\", 59);\n  }\n\n  if (rf & 2) {\n    const value_r34 = ctx.value;\n    i0.ɵɵproperty(\"ngIf\", value_r34);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r37 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r37);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r37);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r38 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r38);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r38);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r39 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r39);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r39);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_54_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r40 = i0.ɵɵnextContext().row;\n    i0.ɵɵpropertyInterpolate(\"title\", row_r40.Checker);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(row_r40.Checker);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_54_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 61);\n    i0.ɵɵtext(1, \"AUTO\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TraineeCertificateTestListComponent_ng_template_54_span_0_Template, 2, 2, \"span\", 59);\n    i0.ɵɵtemplate(1, TraineeCertificateTestListComponent_ng_template_54_span_1_Template, 2, 0, \"span\", 60);\n  }\n\n  if (rf & 2) {\n    const row_r40 = ctx.row;\n    i0.ɵɵproperty(\"ngIf\", row_r40.Checker);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (row_r40.Status === \"Examined\" || row_r40.Status === \"Published\") && !row_r40.Checker);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_56_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r44 = i0.ɵɵnextContext().value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r44);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_56_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r44 = i0.ɵɵnextContext().value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r44);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TraineeCertificateTestListComponent_ng_template_56_span_0_Template, 2, 1, \"span\", 62);\n    i0.ɵɵtemplate(1, TraineeCertificateTestListComponent_ng_template_56_span_1_Template, 2, 1, \"span\", 63);\n  }\n\n  if (rf & 2) {\n    const value_r44 = ctx.value;\n    i0.ɵɵproperty(\"ngIf\", value_r44 === \"Passed\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", value_r44 === \"Failed\");\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_58_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵpipe(1, \"amDateFormat\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r49 = i0.ɵɵnextContext().value;\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind2(1, 2, value_r49, \"MMM DD, YYYY\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 5, value_r49, \"MMM DD, YYYY\"), \" \");\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TraineeCertificateTestListComponent_ng_template_58_span_0_Template, 4, 8, \"span\", 59);\n  }\n\n  if (rf & 2) {\n    const value_r49 = ctx.value;\n    i0.ɵɵproperty(\"ngIf\", value_r49);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r52 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r52);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r52);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_62_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function TraineeCertificateTestListComponent_ng_template_62_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const row_r53 = i0.ɵɵnextContext().row;\n      const ctx_r56 = i0.ɵɵnextContext();\n\n      const _r17 = i0.ɵɵreference(64);\n\n      return ctx_r56.ExtendQuota(row_r53, _r17);\n    });\n    i0.ɵɵelement(1, \"i\", 71);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_62_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function TraineeCertificateTestListComponent_ng_template_62_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const row_r53 = i0.ɵɵnextContext().row;\n      const ctx_r59 = i0.ɵɵnextContext();\n      return ctx_r59.downloadAnswersheet(row_r53);\n    });\n    i0.ɵɵelement(1, \"i\", 21);\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function (a1) {\n  return [\"/check-trainee-answersheet\", a1];\n};\n\nfunction TraineeCertificateTestListComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TraineeCertificateTestListComponent_ng_template_62_button_0_Template, 2, 0, \"button\", 66);\n    i0.ɵɵtemplate(1, TraineeCertificateTestListComponent_ng_template_62_button_1_Template, 2, 0, \"button\", 67);\n    i0.ɵɵelementStart(2, \"a\", 68);\n    i0.ɵɵelement(3, \"i\", 69);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r53 = ctx.row;\n    i0.ɵɵproperty(\"ngIf\", row_r53.Result !== \"Passed\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", row_r53.Status !== \"Submitted\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, row_r53.Id));\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_63_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtext(1, \"Quota is required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_63_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵtemplate(1, TraineeCertificateTestListComponent_ng_template_63_div_11_div_1_Template, 2, 0, \"div\", 43);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r62 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r62.f.ExtendQuota.errors[\"required\"]);\n  }\n}\n\nfunction TraineeCertificateTestListComponent_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelementStart(1, \"h4\", 74);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function TraineeCertificateTestListComponent_ng_template_63_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return ctx_r64.modalHideExtendQuota();\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 76);\n    i0.ɵɵelementStart(5, \"form\", 77);\n    i0.ɵɵelementStart(6, \"div\", 78);\n    i0.ɵɵelementStart(7, \"label\", 79);\n    i0.ɵɵtext(8, \"Extended Quota\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 80);\n    i0.ɵɵelement(10, \"input\", 81);\n    i0.ɵɵtemplate(11, TraineeCertificateTestListComponent_ng_template_63_div_11_Template, 2, 1, \"div\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 83);\n    i0.ɵɵelementStart(13, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function TraineeCertificateTestListComponent_ng_template_63_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return ctx_r66.onSaveExtendQuota();\n    });\n    i0.ɵɵtext(14, \"Save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 85);\n    i0.ɵɵelementStart(16, \"a\", 86);\n    i0.ɵɵlistener(\"click\", function TraineeCertificateTestListComponent_ng_template_63_Template_a_click_16_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r67 = i0.ɵɵnextContext();\n      return ctx_r67.modalHideExtendQuota();\n    });\n    i0.ɵɵtext(17, \"Close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Extend quota for \", ctx_r18.ExtendedQuotaUser, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r18.extendQuota);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.submitted && ctx_r18.f.ExtendQuota);\n  }\n}\n\nconst _c1 = function () {\n  return [\"/trainee-certificate-test-publish\"];\n};\n\nexport class TraineeCertificateTestListComponent {\n  constructor(appComponent, formBuilder, _service, toastr, dialog, bsModalService) {\n    this.appComponent = appComponent;\n    this.formBuilder = formBuilder;\n    this._service = _service;\n    this.toastr = toastr;\n    this.dialog = dialog;\n    this.bsModalService = bsModalService;\n    this.baseUrl = environment.baseUrl;\n    this.submitted = false;\n    this.courseList = [];\n    this.examList = [];\n    this.examPublishList = [];\n    this.rows = [];\n    this.rowsPublish = [];\n    this.statusList = [{\n      id: 'Submitted',\n      text: 'Submitted'\n    }, {\n      id: 'Examined',\n      text: 'Examined'\n    }, {\n      id: 'Published',\n      text: 'Published'\n    }];\n    this.loadingIndicator = false;\n    this.ColumnMode = ColumnMode;\n    this.traineeLoading = false;\n    this.traineeInput$ = new Subject();\n    this.page = new Page();\n    this.page.pageNumber = 0;\n    this.page.size = 10;\n  }\n\n  handleSelectClick(selectElement) {\n    this.appComponent.handleSelectClick(selectElement);\n  }\n\n  ngOnInit() {\n    this.filterForm = this.formBuilder.group({\n      courseId: [null, [Validators.required]],\n      status: [null],\n      trainee: [null]\n    });\n    this.extendQuota = this.formBuilder.group({\n      ExtendQuota: ['', [Validators.required]]\n    });\n    this.loadTrainee();\n    this.getCourseList();\n  }\n\n  get f() {\n    return this.filterForm.controls;\n  }\n\n  loadTrainee() {\n    this.trainee$ = concat(of([]), // default items\n    this.traineeInput$.pipe(debounceTime(500), distinctUntilChanged(), tap(() => this.traineeLoading = true), switchMap(term => {\n      if (term && term.length > 2) return this._service.get(\"trainee/query/10/\" + term).pipe(catchError(() => of([])), tap(() => this.traineeLoading = false), switchMap(res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, \"Warning!\", {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, \"Error!\", {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        return of(res.Data);\n      }));else {\n        this.traineeLoading = false;\n        return of([]);\n      }\n    })));\n  }\n\n  setPage(pageInfo) {\n    this.page.pageNumber = pageInfo.offset;\n    this.filterList();\n  }\n\n  getCourseList() {\n    this._service.get('course/dropdown-list').subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.courseList = res.Data;\n    }, () => {});\n  }\n\n  filterList() {\n    this.submitted = true;\n    if (this.filterForm.invalid) return;\n    this.loadingIndicator = true;\n    const obj = {\n      courseId: this.filterForm.value.courseId,\n      examStatus: this.filterForm.value.status,\n      traineeId: this.filterForm.value.trainee ? this.filterForm.value.trainee.Id : null,\n      size: this.page.size,\n      pageNumber: this.page.pageNumber\n    };\n\n    this._service.get('exam/certificate-test/get-trainee-exam-list', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.rows = res.Data.Records;\n        this.page.totalElements = res.Data.Total;\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n        setTimeout(() => {\n          this.loadingIndicator = false;\n        }, 1000);\n        this.submitted = false;\n      },\n      error: err => {\n        this.toastr.error(err.message || err, \"Error!\", {\n          timeOut: 2000\n        });\n        setTimeout(() => {\n          this.loadingIndicator = false;\n        }, 1000);\n        this.submitted = false;\n      },\n      complete: () => setTimeout(() => {\n        this.loadingIndicator = false;\n      }, 1000)\n    });\n  }\n\n  openUploadDialog() {\n    this.dialog.open(UploadDialogComponent, {\n      data: {\n        url: this._service.generateUrl('exam/certificate-test/upload-marking-file'),\n        whiteList: ['xlsx', 'xls'],\n        uploadtext: 'Please upload an Excel file',\n        title: 'Upload Marking File'\n      },\n      width: '50%',\n      height: '50%'\n    }).afterClosed().subscribe(response => {\n      if (response) {\n        this.toastr.success(response, 'Success!', {\n          timeOut: 2000\n        });\n        this.filterList();\n      }\n    });\n  }\n\n  downloadExcel() {\n    this.submitted = true;\n    if (this.filterForm.invalid) return;\n    const obj = {\n      courseId: this.filterForm.value.courseId,\n      examStatus: this.filterForm.value.status,\n      traineeId: this.filterForm.value.trainee ? this.filterForm.value.trainee.Id : null,\n      timeZoneOffset: new Date().getTimezoneOffset()\n    };\n    this.blockUI.start(\"Generating excel file. Please wait ...\");\n    return this._service.downloadFile('exam/certificate-test/download-list-in-excel', obj).subscribe(res => {\n      this.blockUI.stop();\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement(\"a\");\n      link.href = url;\n      link.download = \"Certificate_Tets_Result_List.xlsx\";\n      link.click();\n      link.remove();\n      this.submitted = false;\n    }, error => {\n      this.toastr.error(error.message || error, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n      this.submitted = false;\n    });\n  }\n\n  downloadAnswersheet(item) {\n    this.blockUI.start('Generating pdf file. Please wait ...');\n\n    this._service.downloadFile('exam/certificate-test/get-answer-sheet-in-pdf/' + item.Id).subscribe({\n      next: res => {\n        const url = window.URL.createObjectURL(res);\n        var link = document.createElement('a');\n        link.href = url;\n        link.download = item.PIN + '_Certification_Test_Answersheet.pdf';\n        link.click();\n        link.remove();\n      },\n      error: error => {\n        this.toastr.error(error.message || error, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  ExtendQuota(rowData, template) {\n    this.ExtendedQuotaUser = rowData.Name;\n    this.TraineeId = rowData.TraineeId;\n    this.ExamId = rowData.ExamId;\n    this.modalRef = this.bsModalService.show(template, {\n      class: 'gray',\n      backdrop: 'static'\n    });\n  }\n\n  onSaveExtendQuota() {\n    // alert(this.extendQuota.value.ExtendQuota);\n    var obj = {\n      examId: this.ExamId,\n      traineeId: this.TraineeId,\n      extendedQuota: this.extendQuota.value.ExtendQuota\n    };\n    this.submitted = true;\n\n    this._service.get('exam/certificate-test/extend-trainee-exam-quota', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, 'Success!', {\n          timeOut: 2000\n        });\n        this.submitted = false;\n        this.modalHideExtendQuota();\n      },\n      error: err => {\n        this.toastr.error(err.message || err, \"Error!\", {\n          timeOut: 2000\n        });\n        setTimeout(() => {\n          this.loadingIndicator = false;\n        }, 1000);\n        this.submitted = false;\n      },\n      complete: () => setTimeout(() => {\n        this.loadingIndicator = false;\n      }, 1000)\n    });\n  }\n\n  modalHideExtendQuota() {\n    this.ExtendedQuotaUser = '';\n    this.TraineeId = '';\n    this.ExamId = '';\n    this.extendQuota.reset();\n    this.modalRef.hide();\n  }\n\n}\n\nTraineeCertificateTestListComponent.ɵfac = function TraineeCertificateTestListComponent_Factory(t) {\n  return new (t || TraineeCertificateTestListComponent)(i0.ɵɵdirectiveInject(i1.AppComponent), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.CommonService), i0.ɵɵdirectiveInject(i4.ToastrService), i0.ɵɵdirectiveInject(i5.MatDialog), i0.ɵɵdirectiveInject(i6.BsModalService));\n};\n\nTraineeCertificateTestListComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: TraineeCertificateTestListComponent,\n  selectors: [[\"app-trainee-certificate-test-list\"]],\n  inputs: {\n    ExtendedQuotaUser: \"ExtendedQuotaUser\",\n    TraineeId: \"TraineeId\",\n    ExamId: \"ExamId\"\n  },\n  decls: 65,\n  vars: 57,\n  consts: [[1, \"col-12\"], [1, \"card\", \"card-border-primary\"], [1, \"card-header\"], [1, \"card-block\"], [1, \"row\"], [\"autocomplete\", \"off\", 1, \"col-12\", \"custom-inline-form\", 3, \"formGroup\"], [1, \"mb-3\", \"col-lg-3\", \"col-12\"], [1, \"col-form-label\", \"col-form-label-sm\", \"text-right\", \"required\"], [\"formControlName\", \"courseId\", \"bindLabel\", \"Title\", \"class\", \"form-control form-control-sm\", \"bindValue\", \"Id\", \"placeholder\", \"Select a course\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\", 4, \"ngIf\"], [\"class\", \"error-text\", 4, \"ngIf\"], [1, \"mb-3\", \"col-lg-2\", \"col-12\"], [1, \"col-form-label\", \"col-form-label-sm\"], [\"formControlName\", \"status\", \"bindLabel\", \"text\", \"bindValue\", \"id\", \"placeholder\", \"All\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\"], [\"selectElementS\", \"\"], [1, \"col-form-label\", \"col-form-label-sm\", \"text-right\"], [\"formControlName\", \"trainee\", \"bindLabel\", \"Name\", \"typeToSearchText\", \"Please enter 3 or more characters\", \"placeholder\", \"Type trainee pin/name\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"hideSelected\", \"loading\", \"typeahead\", \"click\"], [\"selectElementT\", \"\"], [\"ng-option-tmp\", \"\"], [1, \"mb-3\", \"col-lg-4\", \"col-12\"], [1, \"btn\", \"btn-theme\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"feather\", \"icon-search\"], [1, \"feather\", \"icon-download\"], [\"target\", \"_blank\", \"rel\", \"noopener\", 1, \"btn\", \"btn-theme\", \"btn-sm\", \"text-white\", \"float-end\", 3, \"routerLink\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"col-lg-12\"], [\"rowHeight\", \"auto\", 1, \"material\", \"table-bordered\", 3, \"rows\", \"loadingIndicator\", \"columnMode\", \"headerHeight\", \"footerHeight\", \"count\", \"offset\", \"limit\", \"page\"], [\"name\", \"PIN\", \"prop\", \"PIN\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"ngx-datatable-cell-template\", \"\"], [\"name\", \"Name\", \"prop\", \"Name\", 3, \"draggable\", \"sortable\"], [\"prop\", \"Division\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"Attend On\", \"prop\", \"StartDate\", \"headerClass\", \"text-center\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"T. Marks\", \"prop\", \"TotalMarks\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"G. Marks\", \"prop\", \"GainedMarks\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"Status\", \"prop\", \"Status\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"Marked By\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"Result\", \"prop\", \"Result\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"Marked On\", \"prop\", \"MarkedOn\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"Termination\", \"prop\", \"Terminated\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"Action\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"templateExtendQuota\", \"\"], [\"formControlName\", \"courseId\", \"bindLabel\", \"Title\", \"bindValue\", \"Id\", \"placeholder\", \"Select a course\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\"], [\"selectElement\", \"\"], [1, \"error-text\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [1, \"media\"], [\"class\", \"rounded-circle me-3\", \"width\", \"40\", 3, \"src\", 4, \"ngIf\"], [\"src\", \"assets/images/user/user-avatar-blank.png\", \"class\", \"rounded-circle me-3\", \"width\", \"40\", 4, \"ngIf\"], [\"src\", \"assets/images/user/other.jpg\", \"alt\", \"user image\", \"class\", \"rounded-circle me-3\", \"width\", \"40\", 4, \"ngIf\"], [\"src\", \"assets/images/user/male.jpg\", \"alt\", \"user image\", \"class\", \"rounded-circle me-3\", \"width\", \"40\", 4, \"ngIf\"], [\"src\", \"assets/images/user/female.jpg\", \"alt\", \"user image\", \"class\", \"rounded-circle me-3\", \"width\", \"40\", 4, \"ngIf\"], [1, \"media-body\"], [1, \"mt-0\"], [\"width\", \"40\", 1, \"rounded-circle\", \"me-3\", 3, \"src\"], [\"src\", \"assets/images/user/user-avatar-blank.png\", \"width\", \"40\", 1, \"rounded-circle\", \"me-3\"], [\"src\", \"assets/images/user/other.jpg\", \"alt\", \"user image\", \"width\", \"40\", 1, \"rounded-circle\", \"me-3\"], [\"src\", \"assets/images/user/male.jpg\", \"alt\", \"user image\", \"width\", \"40\", 1, \"rounded-circle\", \"me-3\"], [\"src\", \"assets/images/user/female.jpg\", \"alt\", \"user image\", \"width\", \"40\", 1, \"rounded-circle\", \"me-3\"], [3, \"title\"], [3, \"title\", 4, \"ngIf\"], [\"title\", \"AUTO\", 4, \"ngIf\"], [\"title\", \"AUTO\"], [\"class\", \"badge bg-success\", 4, \"ngIf\"], [\"class\", \"badge bg-danger\", 4, \"ngIf\"], [1, \"badge\", \"bg-success\"], [1, \"badge\", \"bg-danger\"], [\"class\", \"btn btn-warning btn-mini me-1\", \"title\", \"Extend Quota\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info btn-mini me-1\", \"title\", \"Download PDF\", 3, \"click\", 4, \"ngIf\"], [\"target\", \"blank\", \"rel\", \"noopener\", \"title\", \"Check Answersheet\", 1, \"btn\", \"btn-primary\", \"btn-mini\", 3, \"routerLink\"], [1, \"feather\", \"icon-arrow-right\"], [\"title\", \"Extend Quota\", 1, \"btn\", \"btn-warning\", \"btn-mini\", \"me-1\", 3, \"click\"], [1, \"feather\", \"icon-edit\"], [\"title\", \"Download PDF\", 1, \"btn\", \"btn-info\", \"btn-mini\", \"me-1\", 3, \"click\"], [1, \"modal-header\"], [1, \"modal-title\", \"float-start\"], [\"type\", \"button \", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\"], [\"autocomplete\", \"off\", 3, \"formGroup\"], [1, \"mb-3\", \"row\"], [\"for\", \"pin\", 1, \"col-sm-4\", \"col-12\", \"col-form-label\"], [1, \"col-sm-8\", \"col-12\"], [\"type\", \"number\", \"formControlName\", \"ExtendQuota\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"d-grid\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"text-center\", \"mt-3\", \"fw-bold\"], [\"href\", \"javascript:;\", 1, \"tctl-style-1\", 3, \"click\"], [1, \"invalid-feedback\"]],\n  template: function TraineeCertificateTestListComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r68 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"h5\");\n      i0.ɵɵtext(5, \"Trainee Certificate Test List\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"div\", 3);\n      i0.ɵɵelementStart(7, \"div\", 4);\n      i0.ɵɵelementStart(8, \"form\", 5);\n      i0.ɵɵelementStart(9, \"div\", 4);\n      i0.ɵɵelementStart(10, \"div\", 6);\n      i0.ɵɵelementStart(11, \"label\", 7);\n      i0.ɵɵtext(12, \" Course \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(13, TraineeCertificateTestListComponent_ng_select_13_Template, 2, 3, \"ng-select\", 8);\n      i0.ɵɵtemplate(14, TraineeCertificateTestListComponent_div_14_Template, 2, 1, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"div\", 10);\n      i0.ɵɵelementStart(16, \"label\", 11);\n      i0.ɵɵtext(17, \" Status \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"ng-select\", 12, 13);\n      i0.ɵɵlistener(\"click\", function TraineeCertificateTestListComponent_Template_ng_select_click_18_listener() {\n        i0.ɵɵrestoreView(_r68);\n\n        const _r2 = i0.ɵɵreference(19);\n\n        return ctx.handleSelectClick(_r2);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"div\", 6);\n      i0.ɵɵelementStart(21, \"label\", 14);\n      i0.ɵɵtext(22, \" Trainee \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(23, \"ng-select\", 15, 16);\n      i0.ɵɵlistener(\"click\", function TraineeCertificateTestListComponent_Template_ng_select_click_23_listener() {\n        i0.ɵɵrestoreView(_r68);\n\n        const _r3 = i0.ɵɵreference(24);\n\n        return ctx.handleSelectClick(_r3);\n      });\n      i0.ɵɵpipe(25, \"async\");\n      i0.ɵɵtemplate(26, TraineeCertificateTestListComponent_ng_template_26_Template, 18, 8, \"ng-template\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(27, \"div\", 18);\n      i0.ɵɵelementStart(28, \"button\", 19);\n      i0.ɵɵlistener(\"click\", function TraineeCertificateTestListComponent_Template_button_click_28_listener() {\n        return ctx.filterList();\n      });\n      i0.ɵɵelement(29, \"i\", 20);\n      i0.ɵɵtext(30, \" Search \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(31, \"button\", 19);\n      i0.ɵɵlistener(\"click\", function TraineeCertificateTestListComponent_Template_button_click_31_listener() {\n        return ctx.downloadExcel();\n      });\n      i0.ɵɵelement(32, \"i\", 21);\n      i0.ɵɵtext(33, \" Download \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(34, \"a\", 22);\n      i0.ɵɵtext(35, \" Bulk Result Publish \");\n      i0.ɵɵelement(36, \"i\", 23);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(37, \"div\", 24);\n      i0.ɵɵelementStart(38, \"ngx-datatable\", 25);\n      i0.ɵɵlistener(\"page\", function TraineeCertificateTestListComponent_Template_ngx_datatable_page_38_listener($event) {\n        return ctx.setPage($event);\n      });\n      i0.ɵɵelementStart(39, \"ngx-datatable-column\", 26);\n      i0.ɵɵtemplate(40, TraineeCertificateTestListComponent_ng_template_40_Template, 2, 2, \"ng-template\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(41, \"ngx-datatable-column\", 28);\n      i0.ɵɵtemplate(42, TraineeCertificateTestListComponent_ng_template_42_Template, 2, 2, \"ng-template\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(43, \"ngx-datatable-column\", 29);\n      i0.ɵɵtemplate(44, TraineeCertificateTestListComponent_ng_template_44_Template, 2, 2, \"ng-template\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(45, \"ngx-datatable-column\", 30);\n      i0.ɵɵtemplate(46, TraineeCertificateTestListComponent_ng_template_46_Template, 1, 1, \"ng-template\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(47, \"ngx-datatable-column\", 31);\n      i0.ɵɵtemplate(48, TraineeCertificateTestListComponent_ng_template_48_Template, 2, 2, \"ng-template\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(49, \"ngx-datatable-column\", 32);\n      i0.ɵɵtemplate(50, TraineeCertificateTestListComponent_ng_template_50_Template, 2, 2, \"ng-template\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(51, \"ngx-datatable-column\", 33);\n      i0.ɵɵtemplate(52, TraineeCertificateTestListComponent_ng_template_52_Template, 2, 2, \"ng-template\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(53, \"ngx-datatable-column\", 34);\n      i0.ɵɵtemplate(54, TraineeCertificateTestListComponent_ng_template_54_Template, 2, 2, \"ng-template\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(55, \"ngx-datatable-column\", 35);\n      i0.ɵɵtemplate(56, TraineeCertificateTestListComponent_ng_template_56_Template, 2, 2, \"ng-template\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(57, \"ngx-datatable-column\", 36);\n      i0.ɵɵtemplate(58, TraineeCertificateTestListComponent_ng_template_58_Template, 1, 1, \"ng-template\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(59, \"ngx-datatable-column\", 37);\n      i0.ɵɵtemplate(60, TraineeCertificateTestListComponent_ng_template_60_Template, 2, 2, \"ng-template\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(61, \"ngx-datatable-column\", 38);\n      i0.ɵɵtemplate(62, TraineeCertificateTestListComponent_ng_template_62_Template, 4, 5, \"ng-template\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(63, TraineeCertificateTestListComponent_ng_template_63_Template, 18, 3, \"ng-template\", null, 39, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.courseList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.courseId.errors);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"clearable\", true)(\"clearOnBackspace\", true)(\"items\", ctx.statusList);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(25, 54, ctx.trainee$))(\"hideSelected\", true)(\"loading\", ctx.traineeLoading)(\"typeahead\", ctx.traineeInput$);\n      i0.ɵɵadvance(11);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(56, _c1));\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"rows\", ctx.rows)(\"loadingIndicator\", ctx.loadingIndicator)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"count\", ctx.page.totalElements)(\"offset\", ctx.page.pageNumber)(\"limit\", ctx.page.size);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"maxWidth\", 80)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 180)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 185)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 70)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 70)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 110)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 150)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 90)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 120)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 110)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 120)(\"draggable\", false)(\"sortable\", false);\n    }\n  },\n  directives: [i7.BlockUIComponent, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i8.NgIf, i9.NgSelectComponent, i2.NgControlStatus, i2.FormControlName, i9.NgOptionTemplateDirective, i10.RouterLinkWithHref, i11.DatatableComponent, i11.DataTableColumnDirective, i11.DataTableColumnCellDirective, i2.NumberValueAccessor, i2.DefaultValueAccessor],\n  pipes: [i8.AsyncPipe, i12.DateFormatPipe],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], TraineeCertificateTestListComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}