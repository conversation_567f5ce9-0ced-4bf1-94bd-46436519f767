{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport { NgbDropdownConfig } from '@ng-bootstrap/ng-bootstrap';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { Page } from 'src/app/_models/page';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/_services/authentication.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/_services/common.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/flex-layout/extended\";\nimport * as i8 from \"ngx-moment\";\n\nfunction NavRightComponent_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.unseenTotal);\n  }\n}\n\nfunction NavRightComponent_li_11_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n}\n\nfunction NavRightComponent_li_11_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelementStart(1, \"p\");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 26);\n    i0.ɵɵelement(5, \"i\", 27);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"amTimeAgo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p\", 28);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r3.Title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 3, item_r3.CreatedOn));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHtml\", item_r3.Details, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction NavRightComponent_li_11_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelementStart(1, \"p\");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3, \"No notification found.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"unseen\": a0\n  };\n};\n\nfunction NavRightComponent_li_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 20);\n    i0.ɵɵlistener(\"click\", function NavRightComponent_li_11_Template_li_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const item_r3 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return ctx_r8.onClickNotification(item_r3);\n    });\n    i0.ɵɵtemplate(1, NavRightComponent_li_11_span_1_Template, 1, 0, \"span\", 21);\n    i0.ɵɵelementStart(2, \"div\", 22);\n    i0.ɵɵtemplate(3, NavRightComponent_li_11_div_3_Template, 9, 5, \"div\", 23);\n    i0.ɵɵtemplate(4, NavRightComponent_li_11_div_4_Template, 4, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, !item_r3.Seen));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r3.Seen);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.notifications.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.notifications.length <= 0);\n  }\n}\n\nfunction NavRightComponent_a_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 29);\n    i0.ɵɵlistener(\"click\", function NavRightComponent_a_18_Template_a_click_0_listener($event) {\n      return $event.preventDefault();\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelement(2, \"i\", 30);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.currentUser.FullName, \" \");\n  }\n}\n\nconst _c1 = function () {\n  return [\"/notification-list\"];\n};\n\nexport let NavRightComponent = /*#__PURE__*/(() => {\n  class NavRightComponent {\n    constructor(authService, router, _service, toastr) {\n      this.authService = authService;\n      this.router = router;\n      this._service = _service;\n      this.toastr = toastr;\n      this.loadingIndicator = false;\n      this.notifications = [];\n      this.page = new Page();\n      this.unseenTotal = 0;\n      this.page.size = 10;\n      this.authService.getCurrentUser().subscribe(user => {\n        this.currentUser = user;\n      });\n    }\n\n    ngOnInit() {\n      this.getList();\n    }\n\n    logout() {\n      this.authService.logout(window.location.hostname).subscribe(() => this.router.navigate(['/auth/signin']));\n    }\n\n    showMore() {\n      this.page.pageNumber++;\n      this.getList();\n    }\n\n    getList() {\n      this.loadingIndicator = true;\n      let obj = {\n        size: this.page.size,\n        pageNumber: this.page.pageNumber,\n        unseenOnly: false\n      };\n\n      this._service.get('notification/admin/list', obj).subscribe(res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        res.Data.Records.forEach(element => {\n          this.notifications.push(element);\n        });\n        this.unseenTotal = res.Data.UnseenTotal;\n        this.page.totalElements = res.Data.Total;\n        this.page.totalPages = this.page.totalElements > 0 ? this.page.totalElements / this.page.size : 0;\n        setTimeout(() => {\n          this.loadingIndicator = false;\n        }, 1000);\n      }, err => {\n        this.toastr.warning(err.Message || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        setTimeout(() => {\n          this.loadingIndicator = false;\n        }, 1000);\n      });\n    }\n\n    onClickNotification(item) {\n      return __awaiter(this, void 0, void 0, function* () {\n        try {\n          if (!item.Seen) {\n            yield this.seenNotification(item.Id);\n            item.Seen = true;\n            this.unseenTotal--;\n          }\n\n          switch (item.NavigateTo) {\n            case 'ForumPostDetails':\n              this.router.navigate(['forum-post-details', item.Payload]);\n              break;\n\n            case 'CertificateTestAnswersheet':\n              this.router.navigate(['check-trainee-answersheet', item.Payload]);\n              break;\n\n            case 'EvaluationTestAnswersheet':\n              this.router.navigate(['check-trainee-evaluation-answersheet', item.Payload]);\n              break;\n          }\n        } catch (error) {}\n      });\n    }\n\n    seenNotification(id) {\n      return new Promise((resolve, reject) => {\n        this._service.get('notification/seen/' + id).subscribe(res => {\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              timeOut: 2000\n            });\n            return resolve(null);\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              closeButton: true,\n              disableTimeOut: false,\n              enableHtml: true\n            });\n            return resolve(null);\n          }\n\n          resolve(null);\n        }, err => {\n          this.toastr.error(err.message || err, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false\n          });\n          reject(err.message || err);\n        });\n      });\n    }\n\n  }\n\n  NavRightComponent.ɵfac = function NavRightComponent_Factory(t) {\n    return new (t || NavRightComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.CommonService), i0.ɵɵdirectiveInject(i4.ToastrService));\n  };\n\n  NavRightComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NavRightComponent,\n    selectors: [[\"app-nav-right\"]],\n    features: [i0.ɵɵProvidersFeature([NgbDropdownConfig])],\n    decls: 25,\n    vars: 5,\n    consts: [[1, \"navbar-nav\", \"ml-auto\"], [\"ngbDropdown\", \"\", \"placement\", \"auto\", 1, \"dropdown\"], [\"ngbDropdownToggle\", \"\", \"href\", \"javascript:\"], [1, \"icon\", \"feather\", \"icon-bell\"], [\"class\", \"badge bg-pill bg-danger\", 4, \"ngIf\"], [\"ngbDropdownMenu\", \"\", 1, \"dropdown-menu-right\", \"notification\"], [1, \"noti-head\"], [1, \"d-inline-block\", \"m-b-0\"], [1, \"noti-body\"], [\"ngbDropdownItem\", \"\", \"class\", \"notification text-wrap bg-white notification-footer\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"ngbDropdownItem\", \"\", 1, \"noti-footer\", \"hover-item\"], [\"href\", \"javascript:\", \"routerLinkActive\", \"router-link-active\", 3, \"routerLink\"], [1, \"hover-item-text\"], [\"ngbDropdown\", \"\", \"placement\", \"auto\", 1, \"drp-user\", \"dropdown\"], [\"href\", \"\", \"ngbDropdownToggle\", \"\", 3, \"click\", 4, \"ngIf\"], [\"ngbDropdownMenu\", \"\", 1, \"dropdown-menu-right\", \"profile-notification\"], [1, \"pro-body\"], [\"href\", \"javascript:\", 1, \"dropdown-item\", 3, \"click\"], [1, \"feather\", \"icon-log-out\"], [1, \"badge\", \"bg-pill\", \"bg-danger\"], [\"ngbDropdownItem\", \"\", 1, \"notification\", \"text-wrap\", \"bg-white\", \"notification-footer\", 3, \"ngClass\", \"click\"], [\"class\", \"unseen-badge\", 4, \"ngIf\"], [1, \"media\"], [\"class\", \"media-body\", 4, \"ngIf\"], [1, \"unseen-badge\"], [1, \"media-body\"], [1, \"n-time\", \"text-muted\"], [1, \"icon\", \"feather\", \"icon-clock\", \"mr-10\"], [3, \"innerHtml\"], [\"href\", \"\", \"ngbDropdownToggle\", \"\", 3, \"click\"], [1, \"feather\", \"icon-user\"]],\n    template: function NavRightComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"ul\", 0);\n        i0.ɵɵelementStart(1, \"li\");\n        i0.ɵɵelementStart(2, \"div\", 1);\n        i0.ɵɵelementStart(3, \"a\", 2);\n        i0.ɵɵelement(4, \"i\", 3);\n        i0.ɵɵtemplate(5, NavRightComponent_span_5_Template, 2, 1, \"span\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 5);\n        i0.ɵɵelementStart(7, \"div\", 6);\n        i0.ɵɵelementStart(8, \"h6\", 7);\n        i0.ɵɵtext(9, \"Notifications\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"ul\", 8);\n        i0.ɵɵtemplate(11, NavRightComponent_li_11_Template, 5, 6, \"li\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"div\", 10);\n        i0.ɵɵelementStart(13, \"a\", 11);\n        i0.ɵɵelementStart(14, \"span\", 12);\n        i0.ɵɵtext(15, \" Show More\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"li\");\n        i0.ɵɵelementStart(17, \"div\", 13);\n        i0.ɵɵtemplate(18, NavRightComponent_a_18_Template, 3, 1, \"a\", 14);\n        i0.ɵɵelementStart(19, \"div\", 15);\n        i0.ɵɵelementStart(20, \"ul\", 16);\n        i0.ɵɵelementStart(21, \"li\");\n        i0.ɵɵelementStart(22, \"a\", 17);\n        i0.ɵɵlistener(\"click\", function NavRightComponent_Template_a_click_22_listener() {\n          return ctx.logout();\n        });\n        i0.ɵɵelement(23, \"i\", 18);\n        i0.ɵɵtext(24, \" Logout\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.unseenTotal > 0);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngForOf\", ctx.notifications);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(4, _c1));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n      }\n    },\n    directives: [i5.NgbDropdown, i5.NgbDropdownToggle, i6.NgIf, i5.NgbDropdownMenu, i6.NgForOf, i5.NgbDropdownItem, i6.NgClass, i7.DefaultClassDirective, i2.RouterLinkWithHref, i2.RouterLinkActive],\n    pipes: [i8.TimeAgoPipe],\n    styles: [\".notification.unseen[_ngcontent-%COMP%]{font-weight:500}.notification.unseen[_ngcontent-%COMP%]   span.unseen-badge[_ngcontent-%COMP%]{width:12px;background:#206ad7;display:inline-flex;height:12px;position:absolute;right:25px;border-radius:50%;top:45%}.hover-item[_ngcontent-%COMP%]{background-color:#fff}.hover-item[_ngcontent-%COMP%]:hover   .hover-item-text[_ngcontent-%COMP%]{color:#faa61a}\"]\n  });\n  return NavRightComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}