{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { BlockUI } from 'ng-block-ui';\nimport { ResponseStatus } from '../_models/enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../app.component\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../_services/common.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"ng-block-ui\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@ng-select/ng-select\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"ngx-moment\";\n\nfunction TraineeEvaluationTestPublishComponent_div_21_tr_26_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r5.Result);\n  }\n}\n\nfunction TraineeEvaluationTestPublishComponent_div_21_tr_26_p_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r5.Result);\n  }\n}\n\nconst _c0 = function () {\n  return {\n    standalone: true\n  };\n};\n\nfunction TraineeEvaluationTestPublishComponent_div_21_tr_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵelementStart(3, \"input\", 29);\n    i0.ɵɵlistener(\"ngModelChange\", function TraineeEvaluationTestPublishComponent_div_21_tr_26_Template_input_ngModelChange_3_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const item_r5 = restoredCtx.$implicit;\n      return item_r5.selected = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"label\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtemplate(15, TraineeEvaluationTestPublishComponent_div_21_tr_26_p_15_Template, 2, 1, \"p\", 31);\n    i0.ɵɵtemplate(16, TraineeEvaluationTestPublishComponent_div_21_tr_26_p_16_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"name\", item_r5.Id);\n    i0.ɵɵpropertyInterpolate(\"id\", item_r5.Id);\n    i0.ɵɵpropertyInterpolate(\"value\", item_r5.Id);\n    i0.ɵɵproperty(\"ngModel\", item_r5.selected)(\"ngModelOptions\", i0.ɵɵpureFunction0(16, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"for\", item_r5.Id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r5.Name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r5.Status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(11, 13, item_r5.MarkedOn, \"DD MMM YYYY, h:mm a\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", item_r5.GainedMarks, \" / \", item_r5.TotalMarks, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r5.Result === \"Passed\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r5.Result === \"Failed\");\n  }\n}\n\nfunction TraineeEvaluationTestPublishComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelementStart(1, \"div\", 20);\n    i0.ɵɵelementStart(2, \"h5\", 3);\n    i0.ɵɵtext(3, \"Select To Publish\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 21);\n    i0.ɵɵelementStart(5, \"input\", 22);\n    i0.ɵɵlistener(\"ngModelChange\", function TraineeEvaluationTestPublishComponent_div_21_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return ctx_r12.selectAll = $event;\n    })(\"change\", function TraineeEvaluationTestPublishComponent_div_21_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return ctx_r14.onChangeSelectAll($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"label\", 23);\n    i0.ɵɵtext(7, \" Select All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 24);\n    i0.ɵɵelementStart(9, \"div\", 25);\n    i0.ɵɵelementStart(10, \"table\", 26);\n    i0.ɵɵelementStart(11, \"thead\");\n    i0.ɵɵelementStart(12, \"tr\");\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\");\n    i0.ɵɵtext(18, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\");\n    i0.ɵɵtext(20, \"Marked On\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\");\n    i0.ɵɵtext(22, \"Marks\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\");\n    i0.ɵɵtext(24, \"Result\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"tbody\");\n    i0.ɵɵtemplate(26, TraineeEvaluationTestPublishComponent_div_21_tr_26_Template, 17, 17, \"tr\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.selectAll)(\"ngModelOptions\", i0.ɵɵpureFunction0(3, _c0));\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.rows);\n  }\n}\n\nfunction TraineeEvaluationTestPublishComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"br\");\n    i0.ɵɵelementStart(2, \"h4\", 35);\n    i0.ɵɵtext(3, \"No Item Found!!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TraineeEvaluationTestPublishComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function TraineeEvaluationTestPublishComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return ctx_r15.onPublish();\n    });\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵtext(2, \" Publish\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c1 = function () {\n  return [\"/trainee-evaluation-test-list\"];\n};\n\nexport class TraineeEvaluationTestPublishComponent {\n  constructor(appComponent, formBuilder, _service, toastr) {\n    this.appComponent = appComponent;\n    this.formBuilder = formBuilder;\n    this._service = _service;\n    this.toastr = toastr;\n    this.examList = [];\n    this.rows = [];\n    this.selectAll = false;\n  }\n\n  handleSelectClick(selectElement) {\n    this.appComponent.handleSelectClick(selectElement);\n  }\n\n  ngOnInit() {\n    this.getExamList();\n  }\n\n  getExamList() {\n    this._service.get('evaluation-exam/dropdown-list').subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.examList = res.Data;\n    }, () => {});\n  }\n\n  filterList(exam) {\n    this.selectedExam = exam;\n    this.blockUI.start('Getting data. Please wait...');\n\n    this._service.get('evaluation-exam/get-unpublished-trainee-exam-list/' + exam.Id).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.rows = res.Data;\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.error(err.message || err, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => this.blockUI.stop()\n    });\n  } // Clearing All Selections\n\n\n  clearSelection() {\n    this.rows.forEach(g => {\n      g.selected = false;\n    });\n  }\n\n  onPublish() {\n    let examList = this.rows.filter(x => x.selected).map(x => x.Id);\n\n    if (examList.length === 0) {\n      this.toastr.warning('No exam has been selected to publish.', 'Warning!');\n      return;\n    }\n\n    this.blockUI.start(\"Publishing result. Please wait...\");\n\n    this._service.post('evaluation-exam/publish-trainee-exam', examList).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, 'Success!', {\n        timeOut: 2000\n      });\n      this.filterList(this.selectedExam);\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  onChangeSelectAll(event) {\n    this.rows.forEach(element => {\n      element.selected = event.target.checked;\n    });\n  }\n\n}\n\nTraineeEvaluationTestPublishComponent.ɵfac = function TraineeEvaluationTestPublishComponent_Factory(t) {\n  return new (t || TraineeEvaluationTestPublishComponent)(i0.ɵɵdirectiveInject(i1.AppComponent), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.CommonService), i0.ɵɵdirectiveInject(i4.ToastrService));\n};\n\nTraineeEvaluationTestPublishComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: TraineeEvaluationTestPublishComponent,\n  selectors: [[\"app-trainee-evaluation-test-publish\"]],\n  decls: 25,\n  vars: 8,\n  consts: [[1, \"col-12\"], [1, \"card\", \"card-border-primary\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"btn\", \"btn-info\", \"btn-mini\", \"float-end\", 3, \"routerLink\"], [1, \"feather\", \"icon-arrow-left\"], [1, \"card-block\"], [1, \"row\"], [1, \"col-lg-12\"], [\"autocomplete\", \"off\"], [1, \"mb-3\", \"row\"], [1, \"col-sm-1\", \"col-form-label\", \"col-form-label-sm\", \"text-right\", \"required\"], [1, \"col-sm-4\"], [\"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\", \"change\"], [\"selectElement\", \"\"], [\"class\", \"card mb-0\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"card-footer\"], [\"class\", \"btn btn-theme btn-testz\", 3, \"click\", 4, \"ngIf\"], [1, \"card\", \"mb-0\"], [1, \"card-header\", \"p-2\"], [1, \"custom-control\", \"custom-checkbox\", \"float-end\"], [\"name\", \"selectAll\", \"id\", \"selectAll\", \"type\", \"checkbox\", 1, \"custom-control-input\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\", \"change\"], [\"for\", \"selectAll\", 1, \"custom-control-label\"], [1, \"card-body\", \"p-2\"], [1, \"table-responsive\"], [1, \"table\", \"table-sm\", \"table-hover\", \"table-bordered\", \"table-striped\"], [4, \"ngFor\", \"ngForOf\"], [1, \"custom-control\", \"custom-checkbox\"], [\"type\", \"checkbox\", 1, \"custom-control-input\", 3, \"name\", \"id\", \"ngModel\", \"ngModelOptions\", \"value\", \"ngModelChange\"], [1, \"custom-control-label\", 3, \"for\"], [\"class\", \"badge bg-success\", 4, \"ngIf\"], [\"class\", \"badge bg-danger\", 4, \"ngIf\"], [1, \"badge\", \"bg-success\"], [1, \"badge\", \"bg-danger\"], [1, \"text-danger\"], [1, \"btn\", \"btn-theme\", \"btn-testz\", 3, \"click\"], [1, \"feather\", \"icon-check-circle\"]],\n  template: function TraineeEvaluationTestPublishComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r17 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"h5\", 3);\n      i0.ɵɵtext(5, \" Trainee Evaluation Test Publish\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"button\", 4);\n      i0.ɵɵelement(7, \"i\", 5);\n      i0.ɵɵtext(8, \" Go Back \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"div\", 6);\n      i0.ɵɵelementStart(10, \"div\", 7);\n      i0.ɵɵelementStart(11, \"div\", 8);\n      i0.ɵɵelementStart(12, \"form\", 9);\n      i0.ɵɵelementStart(13, \"div\", 7);\n      i0.ɵɵelementStart(14, \"div\", 8);\n      i0.ɵɵelementStart(15, \"div\", 10);\n      i0.ɵɵelementStart(16, \"label\", 11);\n      i0.ɵɵtext(17, \" Exam \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"div\", 12);\n      i0.ɵɵelementStart(19, \"ng-select\", 13, 14);\n      i0.ɵɵlistener(\"click\", function TraineeEvaluationTestPublishComponent_Template_ng_select_click_19_listener() {\n        i0.ɵɵrestoreView(_r17);\n\n        const _r0 = i0.ɵɵreference(20);\n\n        return ctx.handleSelectClick(_r0);\n      })(\"change\", function TraineeEvaluationTestPublishComponent_Template_ng_select_change_19_listener($event) {\n        return ctx.filterList($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(21, TraineeEvaluationTestPublishComponent_div_21_Template, 27, 4, \"div\", 15);\n      i0.ɵɵtemplate(22, TraineeEvaluationTestPublishComponent_div_22_Template, 4, 0, \"div\", 16);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(23, \"div\", 17);\n      i0.ɵɵtemplate(24, TraineeEvaluationTestPublishComponent_button_24_Template, 3, 0, \"button\", 18);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(7, _c1));\n      i0.ɵɵadvance(13);\n      i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx.examList);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.rows.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedExam && ctx.rows.length === 0);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.rows.length > 0);\n    }\n  },\n  directives: [i5.BlockUIComponent, i6.RouterLink, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.NgForm, i7.NgSelectComponent, i8.NgIf, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgModel, i8.NgForOf],\n  pipes: [i9.DateFormatPipe],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], TraineeEvaluationTestPublishComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}