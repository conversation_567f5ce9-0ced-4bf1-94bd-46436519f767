<block-ui>
  <div class="col-12">
    <div class="card card-border-primary">
      <div class="card-header">
        <h5 class="card-title">Trainee Mock Test List</h5>
      </div>
      <div class="card-body">
        <form [formGroup]="filterForm" class="custom-inline-form" autocomplete="off">
          <div class="row">
            <div class="mb-3 col-lg-3 col-md-4 col-12">
              <label class="col-form-label col-form-label-sm required">
                Course
              </label>
              <ng-select #selectElement (click)="handleSelectClick(selectElement)" class="form-control form-control-sm"
                *ngIf="courseList.length > 0" formControlName="courseId" [clearable]="false" [clearOnBackspace]="false"
                [items]="courseList" (change)="onChangeCourse($event)" bindLabel="Title" bindValue="Id"
                placeholder="Select a course">
              </ng-select>
              <div *ngIf="submitted && f.courseId.errors" class="error-text">
                <span *ngIf="f.courseId.errors.required" class="text-danger">Course is required</span>
              </div>
            </div>

            <div class="mb-3 col-lg-3 col-md-4 col-12">
              <label class="col-form-label col-form-label-sm required">
                Exam
              </label>
              <ng-select #selectElementE (click)="handleSelectClick(selectElementE)"
                class="form-control form-control-sm" formControlName="examId"
                [ngClass]="{ 'is-invalid': submitted && f.examId.errors }" [clearable]="false"
                [clearOnBackspace]="false" [items]="examList" bindLabel="Name" bindValue="Id" placeholder="Select">
              </ng-select>
              <div *ngIf="submitted && f.examId.errors" class="error-text">
                <span *ngIf="f.examId.errors.required" class="text-danger">Exam is required</span>
              </div>
            </div>

            <div class="col-lg-3 col-12 mb-3">
              <label class="col-form-label col-form-label-sm text-right">
                Trainee
              </label>
              <ng-select #selectElementT (click)="handleSelectClick(selectElementT)"
                class="form-control form-control-sm" [items]="trainee$ | async" formControlName="trainee"
                bindLabel="Name" [hideSelected]="true" [loading]="traineeLoading"
                typeToSearchText="Please enter 3 or more characters" [typeahead]="traineeInput$"
                placeholder="Type trainee pin/name">
                <ng-template ng-option-tmp let-item="item" let-index="index">
                  <div class="media">
                    <img *ngIf="item.ImagePath" [src]="baseUrl + item.ImagePath" class="rounded-circle me-3"
                      width="40" />
                    <img *ngIf="!item.ImagePath && !item.Gender" src="assets/images/user/user-avatar-blank.png"
                      class="rounded-circle me-3" width="40" />
                    <img *ngIf="!item.ImagePath && item.Gender == 'Others'" src="assets/images/user/other.jpg"
                      alt="user image" class="rounded-circle me-3" width="40" />
                    <img *ngIf="!item.ImagePath && item.Gender == 'Male'" src="assets/images/user/male.jpg"
                      alt="user image" class="rounded-circle me-3" width="40" />
                    <img *ngIf="!item.ImagePath && item.Gender == 'Female'" src="assets/images/user/female.jpg"
                      alt="user image" class="rounded-circle me-3" width="40" />

                    <div class="media-body">
                      <h5 class="mt-0">
                        <b>{{ item.Name }}</b> <br /><b>PIN:</b> {{ item.PIN }}
                        <br /><b>Division :</b>
                        {{ item.Division }}
                      </h5>
                    </div>
                  </div>
                </ng-template>
              </ng-select>
            </div>

            <div class="mb-3 col-lg-3 col-12">
              <button class="btn btn-theme btn-sm me-2" (click)="filterList()">
                <i class="feather icon-search"></i>
                Search
              </button>
              <button class="btn btn-theme btn-sm me-2" (click)="downloadExcel()">
                <i class="feather icon-download"></i> Download
              </button>
            </div>
          </div>
        </form>

        <ngx-datatable class="material table-bordered" [rows]="rows" [loadingIndicator]="loadingIndicator"
          [externalPaging]="true" [columnMode]="ColumnMode.force" [headerHeight]="40" [footerHeight]="50"
          rowHeight="auto" [count]="page.totalElements" [offset]="page.pageNumber" [limit]="page.size"
          (page)="setPage($event)">
          <ngx-datatable-column [maxWidth]="100" name="PIN" prop="PIN" [draggable]="false" [sortable]="false">
            <ng-template let-value="value" ngx-datatable-cell-template>
              <span title="{{ value }}">{{ value }}</span>
            </ng-template>
          </ngx-datatable-column>

          <ngx-datatable-column name="Name" prop="Name" [draggable]="false" [sortable]="false">
            <ng-template let-value="value" ngx-datatable-cell-template>
              <span title="{{ value }}">{{ value }}</span>
            </ng-template>
          </ngx-datatable-column>

          <ngx-datatable-column prop="Division" [draggable]="false" [sortable]="false">
            <ng-template let-value="value" ngx-datatable-cell-template>
              <span title="{{ value }}">{{ value }}</span>
            </ng-template>
          </ngx-datatable-column>

          <ngx-datatable-column [maxWidth]="185" name="Attend/Entry On" prop="StartDate" [draggable]="false"
            [sortable]="false" headerClass="text-center" cellClass="text-center">
            <ng-template let-value="value" ngx-datatable-cell-template>
              <span *ngIf="value" title="{{ value | amDateFormat : 'MMM DD, YYYY hh:mmA' }}">
                {{ value | amDateFormat : 'MMM DD, YYYY hh:mmA' }}
              </span>
            </ng-template>
          </ngx-datatable-column>

          <ngx-datatable-column [maxWidth]="150" name="Total Marks" prop="TotalMarks" [draggable]="false"
            [sortable]="false" headerClass="text-center" cellClass="text-center">
            <ng-template let-value="value" ngx-datatable-cell-template>
              <span title="{{ value }}">{{ value }}</span>
            </ng-template>
          </ngx-datatable-column>

          <ngx-datatable-column [maxWidth]="150" name="Gained Marks" prop="GainedMarks" [draggable]="false"
            [sortable]="false" headerClass="text-center" cellClass="text-center">
            <ng-template let-value="value" ngx-datatable-cell-template>
              <b title="{{ value }}">{{ value }}</b>
            </ng-template>
          </ngx-datatable-column>
        </ngx-datatable>
      </div>
    </div>
  </div>
</block-ui>