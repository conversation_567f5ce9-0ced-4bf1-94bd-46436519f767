﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using DocumentFormat.OpenXml.Wordprocessing;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.Services
{
    public class ForumService : IForumService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        public ForumService()
        {
            _context = new ApplicationDbContext();
        }
        public ForumService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }
        public async Task<APIResponse> TopicCreateOrUpdate(ForumTopicModel model, ApplicationUser user)
        {
            ForumTopic item = null;
            Notification notification = null;
            bool isEdit = true;
            try
            {
                if (await _context.ForumTopics.AnyAsync(x => x.Id != model.Id && x.Title == model.Title))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Already exists: " + model.Title
                    };

                if (model.Id.HasValue && model.Id != Guid.Empty)
                {
                    item = await _context.ForumTopics.FindAsync(model.Id);
                    if (item == null)
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = "Post not found"
                        };

                    if (item.Status != ForumTopicStatus.Pending)
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = $"Only pending post can be modified. Your post is in {item.Status} status"
                        };
                    item.ModifiedDate = DateTime.UtcNow.ToKindLocal();
                }
                else
                {
                    item = new ForumTopic
                    {
                        CreatorId = user.Id,
                        CreatedDate = DateTime.UtcNow.ToKindLocal()
                    };
                    isEdit = false;
                }

                item.Title = model.Title;
                item.Description = model.Description;
                item.CategoryId = model.CategoryId;
                item.Status = ForumTopicStatus.Pending;

                if (item.Tags == null) item.Tags = new List<ForumTag>();
                item.Tags.Clear();

                if (model.Tags.Any())
                    item.Tags = await _context.ForumTags.Where(x => model.Tags.Contains(x.Id)).ToListAsync();

                if (!isEdit)
                {
                    item.Id = Guid.NewGuid();
                    _context.ForumTopics.Add(item);

                    notification = new Notification
                    {
                        Id = Guid.NewGuid(),
                        CreatedOn = DateTime.UtcNow.ToKindLocal(),
                        NotificationType = NotificationType.ForumTopicCreated,
                        Title = "New Post on Forum",
                        SourceId = user.Trainee.Id.ToString(),
                        TargetUserType = UserType.Admin,
                        Details = $"{user.FirstName} {user.LastName} added a post \"{item.Title}\" on forum. You may approve the post",
                        Payload = item.Id.ToString(),
                        NavigateTo = Navigation.ForumPostDetails
                    };
                    _context.Notifications.Add(notification);
                }
                else
                {
                    item.ModifiedDate = DateTime.UtcNow.ToKindLocal();
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();
                //await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                //if (notification != null) NotificationHub.NewNotification(UserType.Admin.ToString(), item.CreatorId, notification.Id);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved. Wait for system admin's approval. It will not be shown to anyone until approved.")
                };
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                //await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };
            }
            catch (Exception ex)
            {
    

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetTopics(string text, int size, int pageNumber, ApplicationUser user)
        {
            try
            {
                var favCatIds = await _context.Trainees.Where(t => t.UserId == user.Id)
                    .Select(t => t.ForumCategories.Select(a => a.Id).ToList()).FirstOrDefaultAsync();
                if (!favCatIds.Any()) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "No favorit category found!",
                    Data = "NOCATEGORY"
                };

                var query = _context.ForumTopics.Where(x => x.Status != ForumTopicStatus.Pending && favCatIds.Contains(x.CategoryId)).AsQueryable();

                if (!string.IsNullOrEmpty(text)) query = query.Where(x => x.Title.Contains(text));
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.
               OrderByDescending(x => x.CreatedDate)
                    .Skip(pageNumber * size).Take(size);
                var list = await filteredQuery.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.Description,
                    x.CreatedDate,
                    Category = x.Category.Name,
                    Status = x.Status.ToString(),
                    Creator = x.Creator.FirstName + " " + x.Creator.LastName,
                    CreatorImage = x.Creator.ImagePath,
                    CreatorGender = x.Creator.Gender.ToString(),
                    NoOfReplies = x.Posts.Count,
                    NoOfLikes = x.Posts.Any() ? x.Posts.Sum(y => y.Likes.Any() ? y.Likes.Count : 0) : 0,
                    Tags = x.Tags.Select(y => y.Name)
                }).ToListAsync();

                var data = list.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.Description,
                    CreatedDate = x.CreatedDate.ToKindUtc(),
                    x.Category,
                    x.Status,
                    x.Creator,
                    x.CreatorImage,
                    x.CreatorGender,
                    x.NoOfReplies,
                    x.NoOfLikes,
                    x.Tags
                }).ToList();
                var count = await (!string.IsNullOrEmpty(text) ? filteredQuery.CountAsync() : _context.ForumTopics.Where(x => x.Status != ForumTopicStatus.Pending && favCatIds.Contains(x.CategoryId)).CountAsync());


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetMyTopicTitles(int limit, ApplicationUser user, Guid? skipTopicId = null)
        {
            try
            {
                var favCatIds = await _context.Trainees.Where(t => t.UserId == user.Id)
                    .Select(t => t.ForumCategories.Select(a => a.Id).ToList()).FirstOrDefaultAsync();
                if (!favCatIds.Any()) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "No favorit category found!",
                    Data = "NOCATEGORY"
                };

                var query = _context.ForumTopics.Where(x => x.CreatorId == user.Id && favCatIds.Contains(x.CategoryId)).AsQueryable();

                if (skipTopicId.HasValue) query = query.Where(x => x.Id != skipTopicId);

                var list = await query.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.CreatedDate,
                    Status = x.Status.ToString()
                }).OrderByDescending(x => x.CreatedDate).Take(limit).ToListAsync();

                var data = list.Select(x => new
                {
                    x.Id,
                    x.Title,
                    CreatedDate = x.CreatedDate.ToKindUtc(),
                    x.Status
                }).ToList();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetMyTopics(int size, int pageNumber, ApplicationUser user)
        {
            try
            {
                var favCatIds = await _context.Trainees.Where(t => t.UserId == user.Id)
                    .Select(t => t.ForumCategories.Select(a => a.Id).ToList()).FirstOrDefaultAsync();
                if (!favCatIds.Any()) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "No favorit category found!",
                    Data = "NOCATEGORY"
                };

                var query = _context.ForumTopics.Where(x => x.CreatorId == user.Id && favCatIds.Contains(x.CategoryId)).AsQueryable();

                var list = await query.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.Description,
                    x.CreatedDate,
                    Category = x.Category.Name,
                    Status = x.Status.ToString(),
                    Creator = x.Creator.FirstName + " " + x.Creator.LastName,
                    CreatorImage = x.Creator.ImagePath,
                    CreatorGender = x.Creator.Gender.ToString(),
                    NoOfReplies = x.Posts.Count,
                    NoOfLikes = x.Posts.Any() ? x.Posts.Sum(y => y.Likes.Any() ? y.Likes.Count : 0) : 0
                }).OrderByDescending(x => x.CreatedDate)
                .Skip(pageNumber * size).Take(size).ToListAsync();

                var count = await query.CountAsync();

                var data = list.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.Description,
                    CreatedDate = x.CreatedDate.ToKindUtc(),
                    x.Category,
                    x.Status,
                    x.Creator,
                    x.CreatorImage,
                    x.CreatorGender,
                    x.NoOfReplies,
                    x.NoOfLikes
                }).ToList();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetPopularTopicTitles(int limit, ApplicationUser user, Guid? skipTopicId = null)
        {
            try
            {
                var favCatIds = await _context.Trainees.Where(t => t.UserId == user.Id)
                    .Select(t => t.ForumCategories.Select(a => a.Id).ToList()).FirstOrDefaultAsync();
                if (!favCatIds.Any()) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "No favorit category found!",
                    Data = "NOCATEGORY"
                };

                var query = _context.ForumTopics.Where(x => x.Status != ForumTopicStatus.Pending && favCatIds.Contains(x.CategoryId)).AsQueryable();
                if (skipTopicId.HasValue) query = query.Where(x => x.Id != skipTopicId);

                var list = await query.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.CreatedDate,
                    NoOfReplies = x.Posts.Count,
                    NoOfLikes = x.Posts.Any() ? x.Posts.Sum(y => y.Likes.Any() ? y.Likes.Count : 0) : 0
                })
                .OrderByDescending(x => x.NoOfReplies)
                .ThenByDescending(x => x.NoOfLikes)
                .ThenByDescending(x => x.CreatedDate)
                .Take(limit).ToListAsync();

                var data = list.Select(x => new
                {
                    x.Id,
                    x.Title,
                    CreatedDate = x.CreatedDate.ToKindUtc().ToLocalTime(),
                    x.NoOfReplies,
                    x.NoOfLikes
                }).ToList();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetMyPopularTopicTitles(int limit, ApplicationUser user, Guid? skipTopicId = null)
        {
            try
            {
                var favCatIds = await _context.Trainees.Where(t => t.UserId == user.Id)
                    .Select(t => t.ForumCategories.Select(a => a.Id).ToList()).FirstOrDefaultAsync();
                if (!favCatIds.Any()) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "No favorit category found!",
                    Data = "NOCATEGORY"
                };

                var query = _context.ForumTopics.Where(x => x.Status != ForumTopicStatus.Pending && x.CreatorId == user.Id && favCatIds.Contains(x.CategoryId)).AsQueryable();
                if (skipTopicId.HasValue) query = query.Where(x => x.Id != skipTopicId);

                var list = await query.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.CreatedDate,
                    NoOfReplies = x.Posts.Count,
                    NoOfLikes = x.Posts.Any() ? x.Posts.Sum(y => y.Likes.Any() ? y.Likes.Count : 0) : 0
                })
                .OrderByDescending(x => x.NoOfReplies)
                .ThenByDescending(x => x.NoOfLikes)
                .ThenByDescending(x => x.CreatedDate)
                .Take(limit).ToListAsync();

                var data = list.Select(x => new
                {
                    x.Id,
                    x.Title,
                    CreatedDate = x.CreatedDate.ToKindUtc(),
                    x.NoOfReplies,
                    x.NoOfLikes
                }).ToList();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetPopularTopics(int size, int pageNumber, ApplicationUser user)
        {
            try
            {
                var favCatIds = await _context.Trainees.Where(t => t.UserId == user.Id)
                    .Select(t => t.ForumCategories.Select(a => a.Id).ToList()).FirstOrDefaultAsync();
                if (!favCatIds.Any()) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "No favorit category found!",
                    Data = "NOCATEGORY"
                };

                var query = _context.ForumTopics.Where(x => x.Status != ForumTopicStatus.Pending && favCatIds.Contains(x.CategoryId)).AsQueryable();

                var list = await query.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.CreatedDate,
                    x.Description,
                    Category = x.Category.Name,
                    Status = x.Status.ToString(),
                    Creator = x.Creator.FirstName + " " + x.Creator.LastName,
                    CreatorImage = x.Creator.ImagePath,
                    CreatorGender = x.Creator.Gender.ToString(),
                    NoOfReplies = x.Posts.Count,
                    NoOfLikes = x.Posts.Any() ? x.Posts.Sum(y => y.Likes.Any() ? y.Likes.Count : 0) : 0
                })
                .OrderByDescending(x => x.NoOfReplies)
                .ThenByDescending(x => x.NoOfLikes)
                .ThenByDescending(x => x.CreatedDate)
                .Skip(pageNumber * size).Take(size).ToListAsync();

                var data = list.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.Description,
                    CreatedDate = x.CreatedDate.ToKindUtc(),
                    x.Category,
                    x.Status,
                    x.Creator,
                    x.CreatorImage,
                    x.CreatorGender,
                    x.NoOfReplies,
                    x.NoOfLikes
                }).ToList();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetMyPopularTopics(int size, int pageNumber, ApplicationUser user)
        {
            try
            {
                var favCatIds = await _context.Trainees.Where(t => t.UserId == user.Id)
                    .Select(t => t.ForumCategories.Select(a => a.Id).ToList()).FirstOrDefaultAsync();
                if (!favCatIds.Any()) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "No favorit category found!",
                    Data = "NOCATEGORY"
                };

                var query = _context.ForumTopics.Where(x => x.Status != ForumTopicStatus.Pending && x.CreatorId == user.Id && favCatIds.Contains(x.CategoryId)).AsQueryable();

                var list = await query.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.CreatedDate,
                    x.Description,
                    Category = x.Category.Name,
                    Status = x.Status.ToString(),
                    Creator = x.Creator.FirstName + " " + x.Creator.LastName,
                    CreatorImage = x.Creator.ImagePath,
                    CreatorGender = x.Creator.Gender.ToString(),
                    NoOfReplies = x.Posts.Count,
                    NoOfLikes = x.Posts.Any() ? x.Posts.Sum(y => y.Likes.Any() ? y.Likes.Count : 0) : 0
                })
                .OrderByDescending(x => x.NoOfReplies)
                .ThenByDescending(x => x.NoOfLikes)
                .ThenByDescending(x => x.CreatedDate)
                .Skip(pageNumber * size).Take(size).ToListAsync();

                var data = list.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.Description,
                    CreatedDate = x.CreatedDate.ToKindUtc(),
                    x.Category,
                    x.Status,
                    x.Creator,
                    x.CreatorImage,
                    x.CreatorGender,
                    x.NoOfReplies,
                    x.NoOfLikes
                }).ToList();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetTopicById(Guid id)
        {
            try
            {
                var item = await _context.ForumTopics.Where(x => x.Id == id)
                    .Select(x => new { x.Id, x.Title, x.Description, Status = x.Status.ToString() })
                    .FirstOrDefaultAsync();

                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Post not found"
                };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = item
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetTopicDetails(Guid id)
        {
            try
            {
                var item = await _context.ForumTopics.Where(x => x.Id == id).Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.Description,
                    x.CreatedDate,
                    Category = x.Category.Name,
                    Status = x.Status.ToString(),
                    x.CreatorId,
                    Creator = x.Creator.FirstName + " " + x.Creator.LastName,
                    CreatorImage = x.Creator.ImagePath,
                    CreatorGender = x.Creator.Gender.ToString(),
                    NoOfReplies = x.Posts.Count,
                    NoOfLikes = x.Posts.Any() ? x.Posts.Sum(y => y.Likes.Any() ? y.Likes.Count : 0) : 0,
                    Tags = x.Tags.Select(y => y.Name).ToList()
                }).FirstOrDefaultAsync();

                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Post not found"
                };

                var data = new
                {
                    item.Id,
                    item.Title,
                    item.Description,
                    CreatedDate = item.CreatedDate.ToKindUtc(),
                    item.Category,
                    item.Status,
                    item.CreatorId,
                    item.Creator,
                    item.CreatorImage,
                    item.CreatorGender,
                    item.NoOfReplies,
                    item.NoOfLikes,
                    item.Tags
                };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetTopicPosts(Guid id, int size, int pageNumber, ApplicationUser user)
        {
            try
            {
                var list = await _context.ForumPosts.Where(x => x.TopicId == id && x.ParentPostId == null)
                    .Select(x => new
                    {
                        x.Id,
                        x.Comment,
                        x.CreatedDate,
                        Creator = x.Creator.FirstName + " " + x.Creator.LastName,
                        x.CreatorId,
                        CreatorImage = x.Creator.ImagePath,
                        Replies = x.Replies.OrderBy(y => y.CreatedDate).Select(y => new
                        {
                            y.Id,
                            y.Comment,
                            y.CreatedDate,
                            Creator = y.Creator.FirstName + " " + y.Creator.LastName,
                            y.CreatorId
                        }).ToList(),
                        NoOfLikes = x.Likes.Count,
                        Liked = x.Likes.Any(y => y.UserId == user.Id)
                    })
                .OrderByDescending(x => x.CreatedDate)
                .Skip(pageNumber * size).Take(size).ToListAsync();

                var data = list.Select(x => new
                {
                    x.Id,
                    x.Comment,
                    CreatedDate = x.CreatedDate.ToKindUtc(),
                    x.Creator,
                    x.CreatorId,
                    x.CreatorImage,
                    Replies = x.Replies.Select(y => new
                    {
                        y.Id,
                        y.Comment,
                        CreatedDate = y.CreatedDate.ToKindUtc(),
                        y.Creator,
                        y.CreatorId
                    }).ToList(),
                    x.NoOfLikes,
                    x.Liked
                }).ToList();

                var count = await _context.ForumPosts.CountAsync(x => x.TopicId == id && x.ParentPostId == null);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> PostSaveOrUpdate(ForumPostModel model, ApplicationUser user)
        {
            DbContextTransaction transaction = null;
            ForumPost item = null;
            Notification notification = null;
            List<string> tokens = new List<string>();
            bool isEdit = true;
            int noOfPost = 0;
            try
            {
                transaction = _context.Database.BeginTransaction(System.Data.IsolationLevel.ReadUncommitted);
                var topic = await _context.ForumTopics.Where(x => x.Id == model.TopicId).Select(x => new { x.Id, x.Status, x.CreatorId, x.Title }).FirstOrDefaultAsync();

                if (topic == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Post not found"
                };

                if (topic.Status == ForumTopicStatus.Closed) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "This post has been already closed. You can't comment now."
                };

                if (model.Id.HasValue)
                {
                    item = await _context.ForumPosts.FindAsync(model.Id);
                    if (item == null) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Post not found"
                    };
                }
                else
                {
                    item = new ForumPost { TopicId = model.TopicId, CreatorId = user.Id, CreatedDate = DateTime.UtcNow.ToKindLocal() };
                    isEdit = false;
                }

                item.Comment = model.Comment;

                if (!isEdit)
                {
                    _context.ForumPosts.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();

                if (!isEdit)
                {
                    _context.PendingNotifications.Add(new PendingNotification
                    {
                        Id = Guid.NewGuid(),
                        CreatedOn = DateTime.UtcNow.ToKindLocal(),
                        NotificationType = NotificationType.ForumComment,
                        Title = "New comment on a post",
                        Details = $"{user.FirstName + " " + user.LastName} commented in a post \"{topic.Title}\" that you followed",
                        Payload = item.Id.ToString(),
                        NavigateTo = Navigation.ForumComment,
                        SourceId = user.Id
                    });

                    if (topic.CreatorId != item.CreatorId)
                    {
                        notification = new Notification
                        {
                            Id = Guid.NewGuid(),
                            CreatedOn = DateTime.UtcNow.ToKindLocal(),
                            NotificationType = NotificationType.ForumComment,
                            TargetUserType = UserType.Trainee,
                            Title = "New comment on your post",
                            Details = $"{user.FirstName + " " + user.LastName} commented in your post \"{topic.Title}\".",
                            Payload = JsonConvert.SerializeObject(new
                            {
                                TopicId = topic.Id,
                                PostId = item.Id
                            }),
                            NavigateTo = Navigation.ForumComment,
                            SourceId = user.Id,
                            TargetTraineeId = await _context.Trainees.Where(x => x.UserId == topic.CreatorId).Select(x => x.Id).FirstOrDefaultAsync(),
                        };
                        _context.Notifications.Add(notification);

                        tokens = await _context.TraineeDevices.Where(x => x.TraineeId == notification.TargetTraineeId).Select(x => x.Token).ToListAsync();
                    }
                    await _context.SaveChangesAsync();

                }

                noOfPost = await _context.ForumPosts.CountAsync(x => x.TopicId == item.TopicId);

                transaction.Commit();

                if (tokens.Any() && notification != null)
                    do
                    {
                        await new FirebaseMessagingClient().SendNotifications(tokens.Take(Math.Min(tokens.Count, 20)).ToArray(), notification.Title, notification.Details, new
                        {
                            NavigateTo = notification.NavigateTo.ToString(),
                            notification.Payload,
                            notification.Id,
                            NotificationType = notification.NotificationType.ToString()
                        });
                        tokens.RemoveRange(0, Math.Min(tokens.Count, 20));
                    } while (tokens.Any());
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved"),
                    Data = noOfPost
                };
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                transaction?.Rollback();
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                transaction?.Rollback();
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
            finally
            {
                transaction?.Dispose();
            }
        }

        public async Task<APIResponse> ReplySaveOrUpdate(ForumReplyModel model, ApplicationUser user)
        {
            DbContextTransaction transaction = null;
            ForumPost item = null;
            List<string> tokens = new List<string>();
            Notification notification = null;
            bool isEdit = true;
            int noOfPost = 0;
            try
            {
                transaction = _context.Database.BeginTransaction(System.Data.IsolationLevel.ReadUncommitted);
                var post = await _context.ForumPosts.FindAsync(model.PostId);

                if (post == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Post not found"
                };


                if (post.Topic.Status == ForumTopicStatus.Closed) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "This post has already been closed. You can't reply now."
                };

                if (model.Id.HasValue)
                {
                    item = post.Replies.FirstOrDefault(x => x.Id == model.Id);
                    if (item == null) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Reply not found"
                    };
                }
                else
                {
                    item = new ForumPost { TopicId = post.TopicId, CreatorId = user.Id, CreatedDate = DateTime.UtcNow.ToKindLocal() };
                    isEdit = false;
                }

                item.Comment = model.Reply;

                post.Replies.Add(item);
                await _context.SaveChangesAsync();

                _context.PendingNotifications.Add(new PendingNotification
                {
                    Id = Guid.NewGuid(),
                    CreatedOn = DateTime.UtcNow.ToKindLocal(),
                    NotificationType = NotificationType.ForumReply,
                    Title = "New reply to a comment that you followed",
                    Details = $"{user.FirstName + " " + user.LastName} replied to a comment that you followed",
                    Payload = item.Id.ToString(),
                    NavigateTo = Navigation.ForumReply,
                    SourceId = user.Id
                });

                if (post.CreatorId != item.CreatorId)
                {
                    var trainee = await _context.Trainees.Where(x => x.UserId == post.CreatorId).Select(x => new { x.Id, Tokens = x.Devices.Select(y => y.Token).ToList() }).FirstOrDefaultAsync();
                    notification = new Notification
                    {
                        Id = Guid.NewGuid(),
                        CreatedOn = DateTime.UtcNow.ToKindLocal(),
                        NotificationType = NotificationType.ForumReply,
                        TargetUserType = UserType.Trainee,
                        Title = "New reply to your comment",
                        Details = $"{user.FirstName + " " + user.LastName} replied to your comment.",
                        Payload = JsonConvert.SerializeObject(new
                        {
                            TopicId = post.TopicId,
                            PostId = post.Id,
                            ReplyId = item.Id
                        }),
                        NavigateTo = Navigation.ForumReply,
                        SourceId = user.Id,
                        TargetTraineeId = trainee.Id,
                    };
                    _context.Notifications.Add(notification);
                    tokens = trainee.Tokens;
                }

                await _context.SaveChangesAsync();
                noOfPost = await _context.ForumPosts.CountAsync(x => x.TopicId == item.TopicId);

                transaction.Commit();

                if (tokens.Any() && notification != null)
                    do
                    {
                        await new FirebaseMessagingClient().SendNotifications(tokens.Take(Math.Min(tokens.Count, 20)).ToArray(), notification.Title, notification.Details, new
                        {
                            NavigateTo = notification.NavigateTo.ToString(),
                            notification.Payload,
                            notification.Id,
                            NotificationType = notification.NotificationType.ToString()
                        });
                        tokens.RemoveRange(0, Math.Min(tokens.Count, 20));
                    } while (tokens.Any());
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved"),
                    Data = new
                    {
                        Reply = new
                        {
                            item.Id,
                            item.Comment,
                            item.CreatedDate,
                            Creator = user.FirstName + " " + user.LastName
                        },
                        NoOfReplies = noOfPost
                    }
                };
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                transaction?.Rollback();
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                transaction?.Rollback();
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
            finally
            {
                transaction?.Dispose();
            }
        }

        public async Task<APIResponse> PostLikeOrUnlike(long id, ApplicationUser user)
        {
            ForumPost item = null;
            //Notification notificationForTopic = null;
            //Notification notificationForPost = null;
            bool like = false;
            int totalLikes = 0;
            try
            {
                item = await _context.ForumPosts.FindAsync(id);
                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Post not found"
                };

                if (item.Likes == null) item.Likes = new List<ForumPostLike>();

                var postLike = item.Likes.FirstOrDefault(x => x.UserId == user.Id);

                if (postLike == null)
                {
                    postLike = new ForumPostLike { PostId = item.Id, UserId = user.Id, LikedOn = DateTime.UtcNow.ToKindLocal() };
                    item.Likes.Add(postLike);
                    like = true;
                }
                else item.Likes.Remove(postLike);

                _context.Entry(item).State = EntityState.Modified;
                await _context.SaveChangesAsync();

                totalLikes = await _context.ForumPosts.Where(x => x.TopicId == item.TopicId).SumAsync(x => x.Likes.Count); ;

                //if (like)
                //{
                //    notificationForTopic = new Notification
                //    {
                //        Id = Guid.NewGuid(),
                //        CreatedOn = DateTime.UtcNow,
                //        NotificationType = NotificationType.Like,
                //        Title = "New Like on conversation " + item.Topic.Title,
                //        TargetUserId = item.Topic.CreatorId,
                //        TargetUserType = UserType.Trainee,
                //        RefId = item.TopicId.ToString(),
                //        // ParentSourceId = item.TopicId.ToString(),
                //        SourceId = user.Id,
                //        Details = $"{user.FirstName} {user.LastName} liked a post - \"" + item.Comment + "\""
                //    };
                //    notificationForPost = new Notification
                //    {
                //        Id = Guid.NewGuid(),
                //        CreatedOn = DateTime.UtcNow,
                //        NotificationType = NotificationType.Like,
                //        Title = "New Like",
                //        TargetUserId = item.CreatorId,
                //        TargetUserType = UserType.Trainee,
                //        RefId = item.TopicId.ToString(),
                //        // ParentSourceId = item.TopicId.ToString(),
                //        SourceId = user.Id,
                //        Details = $"{user.FirstName} {user.LastName} liked your post - \"" + item.Comment + "\""
                //    };



                //    if (item.Topic.CreatorId != item.CreatorId) _context.Notifications.Add(notificationForTopic);

                //    _context.Notifications.Add(notificationForPost);

                //    await _context.SaveChangesAsync();

                //    //if (notificationForPost.TargetUserId != notificationForTopic.TargetUserId)
                //    //{
                //    //    if (notificationForTopic != null) NotificationHub.NewNotification(notificationForTopic.TargetUserType.ToString(), notificationForTopic.TargetUserId, notificationForTopic.Id);
                //    //    if (notificationForPost != null) NotificationHub.NewNotification(notificationForPost.TargetUserType.ToString(), notificationForPost.TargetUserId, notificationForPost.Id);
                //    //}
                //    //else
                //    //{
                //    //    if (notificationForPost != null) NotificationHub.NewNotification(notificationForPost.TargetUserType.ToString(), notificationForPost.TargetUserId, notificationForPost.Id);
                //    //}
                //}

                await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (like ? "Liked" : "Unliked"),
                    Data = new { NoOfLikes = item.Likes.Count, Liked = like, TopicLikes = totalLikes }
                };
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> AddOrModifyMyFavoriteCategories(List<long> categoryIds, ApplicationUser user)
        {
            try
            {
                var trainee = await _context.Trainees.FirstOrDefaultAsync(x => x.UserId == user.Id);

                bool isEdit = trainee.ForumCategories != null;

                if (trainee.ForumCategories == null) trainee.ForumCategories = new List<ForumCategory>();
                trainee.ForumCategories.Clear();

                if (categoryIds.Any())
                    trainee.ForumCategories = await _context.ForumCategories.Where(x => categoryIds.Contains(x.Id)).ToListAsync();


                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())
                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetMyFavoriteCategories(ApplicationUser user)
        {
            try
            {
                var favCatIds = await _context.Trainees.Where(t => t.UserId == user.Id)
                    .Select(t => t.ForumCategories.Select(a => a.Id).ToList()).FirstOrDefaultAsync();

                var data = await _context.ForumCategories.Where(x => x.Active)
                    .Select(x => new { x.Id, x.Name, Selected = favCatIds.Contains(x.Id) }).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        #region Admin Panel APIs
        public async Task<APIResponse> GetTopicList(string text, string categoryIds, ForumTopicStatus? status, int size, int pageNumber)
        {
            try
            {
                var query = _context.ForumTopics.AsQueryable();

                if (status.HasValue) query = query.Where(x => x.Status == status);
                if (!string.IsNullOrEmpty(text)) query = query.Where(x => x.Title.Contains(text));
                if (!string.IsNullOrEmpty(categoryIds))
                {
                    var caregoryIdList = categoryIds.Split(',').Select(long.Parse).ToList();
                    query = query.Where(x => caregoryIdList.Contains(x.CategoryId));
                }
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderByDescending(x => x.CreatedDate).Skip(pageNumber * size).Take(size);
                var data = await filteredQuery.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.CreatedDate,
                    Category = x.Category.Name,
                    Status = x.Status.ToString(),
                    Creator = x.Creator.FirstName + " " + x.Creator.LastName,
                    CreatorImage = x.Creator.ImagePath,
                    NoOfReplies = x.Posts.Count,
                    NoOfLikes = x.Posts.Any() ? x.Posts.Sum(y => y.Likes.Any() ? y.Likes.Count : 0) : 0,
                    Tags = x.Tags.Select(y => y.Name).ToList()
                }).ToListAsync();
                var count = await ((((!string.IsNullOrEmpty(text)) || (!string.IsNullOrEmpty(categoryIds)) || ((!string.IsNullOrEmpty(text)) && (!string.IsNullOrEmpty(categoryIds)))) ? query.CountAsync() : _context.ForumTopics.CountAsync()));
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> ChangeTopicStatus(Guid id, ForumTopicStatus status, ApplicationUser user)
        {
            Notification notification = null;
            try
            {
                var item = await _context.ForumTopics.FindAsync(id);
                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Post not found"
                };

                item.Status = status;

                switch (status)
                {
                    case ForumTopicStatus.Open:
                        item.ApprovedById = user.Id;
                        item.ApprovedDate = DateTime.UtcNow.ToKindLocal();
                        break;
                    case ForumTopicStatus.Closed:
                        item.ClosedById = user.Id;
                        item.ClosedDate = DateTime.UtcNow.ToKindLocal();
                        break;
                }
                _context.Entry(item).State = EntityState.Modified;

                var trainee = await _context.Trainees.Where(x => x.UserId == item.CreatorId).Select(x => new { x.Id, Tokens = x.Devices.Select(y => y.Token).ToList() }).FirstOrDefaultAsync();

                notification = new Notification
                {
                    Id = Guid.NewGuid(),
                    CreatedOn = DateTime.UtcNow.ToKindLocal(),
                    NotificationType = NotificationType.ForumApproval,
                    TargetUserType = UserType.Trainee,
                    TargetTraineeId = trainee.Id,
                    Title = $"Your Post {(item.Status == ForumTopicStatus.Open ? "Approved" : "Closed")}",
                    Details = $"An Admin has {(item.Status == ForumTopicStatus.Open ? "approved" : "closed")} your post \"{item.Title}\".",
                    Payload = item.Id.ToString(),
                    NavigateTo = Navigation.ForumPostDetails
                };
                _context.Notifications.Add(notification);
                await _context.SaveChangesAsync();

                if (trainee.Tokens.Any() && notification != null)
                    do
                    {
                        await new FirebaseMessagingClient().SendNotifications(trainee.Tokens.Take(Math.Min(trainee.Tokens.Count, 20)).ToArray(), notification.Title, notification.Details, new
                        {
                            NavigateTo = notification.NavigateTo.ToString(),
                            notification.Payload,
                            notification.Id,
                            NotificationType = notification.NotificationType.ToString()
                        });
                        trainee.Tokens.RemoveRange(0, Math.Min(trainee.Tokens.Count, 20));
                    } while (trainee.Tokens.Any());

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Status Successfully " + item.Status
                };
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> DeleteTopic(Guid id)
        {

            try
            {
                var item = await _context.ForumTopics.FindAsync(id);
                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Post not found"
                };

                if (item.Status != ForumTopicStatus.Pending) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = $"Only pending post can be deleted. This post is in {item.Status} status."
                };

                item.Tags.Clear();

                _context.Entry(item).State = EntityState.Deleted;

                await _context.SaveChangesAsync();
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Deleted"
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> DeleteCommentOrReply(long id)
        {

            try
            {
                var item = await _context.ForumPosts.FindAsync(id);
                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Post not found"
                };

                _context.Entry(item).State = EntityState.Deleted;

                await _context.SaveChangesAsync();
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Deleted"
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
        #endregion
    }

    public interface IForumService
    {
        Task<APIResponse> TopicCreateOrUpdate(ForumTopicModel model, ApplicationUser user);
        Task<APIResponse> GetTopics(string text, int size, int pageNumber, ApplicationUser user);
        Task<APIResponse> GetMyTopicTitles(int limit, ApplicationUser user, Guid? skipTopicId = null);
        Task<APIResponse> GetMyTopics(int size, int pageNumber, ApplicationUser user);
        Task<APIResponse> GetPopularTopicTitles(int limit, ApplicationUser user, Guid? skipTopicId = null);
        Task<APIResponse> GetMyPopularTopicTitles(int limit, ApplicationUser user, Guid? skipTopicId = null);
        Task<APIResponse> GetPopularTopics(int size, int pageNumber, ApplicationUser user);
        Task<APIResponse> GetMyPopularTopics(int size, int pageNumber, ApplicationUser user);
        Task<APIResponse> GetTopicById(Guid id);
        Task<APIResponse> GetTopicDetails(Guid id);
        Task<APIResponse> GetTopicPosts(Guid id, int size, int pageNumber, ApplicationUser user);
        Task<APIResponse> PostSaveOrUpdate(ForumPostModel model, ApplicationUser user);
        Task<APIResponse> ReplySaveOrUpdate(ForumReplyModel model, ApplicationUser user);
        Task<APIResponse> PostLikeOrUnlike(long id, ApplicationUser user);
        Task<APIResponse> AddOrModifyMyFavoriteCategories(List<long> categoryIds, ApplicationUser user);
        Task<APIResponse> GetMyFavoriteCategories(ApplicationUser user);

        #region Admin Panel APIs
        Task<APIResponse> GetTopicList(string text, string categoryIds, ForumTopicStatus? status, int size, int pageNumber);
        Task<APIResponse> ChangeTopicStatus(Guid id, ForumTopicStatus status, ApplicationUser user);
        Task<APIResponse> DeleteTopic(Guid id);
        Task<APIResponse> DeleteCommentOrReply(long id);
        #endregion
    }
}
