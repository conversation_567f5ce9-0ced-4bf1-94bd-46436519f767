<block-ui>
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-border-primary">
                <div class="card-header">
                    <h5>Certification Test List </h5>
                </div>
                <div class="card-block ">
                    <div class="row ">
                        <form [formGroup]="filterForm" class="col-12" autocomplete="off">
                            <div class="mb-3 row">
                                <label
                                    class="col-lg-1 col-md-3 col-4 col-form-label col-form-label-sm text-right required">Course
                                </label>
                                <div class="col-lg-6 col-md-6 col-8 mb-2">
                                    <ng-select #selectElement (click)="handleSelectClick(selectElement)"
                                        *ngIf="courseList.length > 0" (change)="filterList()"
                                        class="form-control form-control-sm" formControlName="courseId"
                                        [clearable]="false" [clearOnBackspace]="false" [items]="courseList"
                                        bindLabel="Title" bindValue="Id" placeholder="Select a course">
                                    </ng-select>
                                    <div *ngIf="submitted && f.courseId.errors" class="error-text">
                                        <span *ngIf="f.courseId.errors.required" class="text-danger">Course is
                                            required</span>
                                    </div>
                                </div>

                                <!-- <div class="col-lg-4 col-12 mb-2">
                                    <button class="btn btn-theme btn-sm" (click)="filterList()"><i
                                            class=" feather icon-search"></i> Search
                                    </button>
                                </div> -->

                                <div class="col-lg-5 col-12 mb-2">

                                    <button class="btn btn-theme btn-sm float-end"
                                        [routerLink]="['/certification-test-entry']"><i class="feather icon-plus"></i>
                                        Create Certification Test
                                    </button>

                                </div>
                            </div>
                        </form>



                        <div class="col-12">

                            <ngx-datatable class="material table-bordered" [rows]="rows"
                                [loadingIndicator]="loadingIndicator" [columnMode]="ColumnMode.force"
                                [headerHeight]="40" [footerHeight]="50" rowHeight="auto" [limit]="10"
                                [scrollbarH]="scrollBarHorizontal">

                                <ngx-datatable-column [width]="100" name="Course" prop="Course" [draggable]="false"
                                    [sortable]="false">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <span title="{{ value }}">{{ value }}</span>
                                    </ng-template>
                                </ngx-datatable-column>

                                <ngx-datatable-column [width]="15" name="Marks" prop="Marks" [draggable]="false"
                                    [sortable]="false">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <b>{{ value }}</b>
                                    </ng-template>
                                </ngx-datatable-column>

                                <ngx-datatable-column [width]="15" name="Quota" prop="Quota" [draggable]="false"
                                    cellClass="text-center" headerClass="text-center" [sortable]="false">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <b>{{ value }}</b>
                                    </ng-template>
                                </ngx-datatable-column>

                                <!-- <ngx-datatable-column [maxWidth]="70" name="MCQ No" prop="ExamMCQNo" [draggable]="false"
                                    [sortable]="false">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <b>{{ value }}</b>
                                    </ng-template>
                                </ngx-datatable-column>

                                <ngx-datatable-column [width]="60" name="T/F No" prop="ExamTrueFalseNo"
                                    [draggable]="false" [sortable]="false">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <b>{{ value }}</b>
                                    </ng-template>
                                </ngx-datatable-column>

                                <ngx-datatable-column [width]="60" name="FIG No" prop="ExamFIGNo" [draggable]="false"
                                    [sortable]="false">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <b>{{ value }}</b>
                                    </ng-template>
                                </ngx-datatable-column>

                                <ngx-datatable-column [maxWidth]="90" name="Matching No" prop="ExamMatchingNo"
                                    [draggable]="false" [sortable]="false">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <b>{{ value }}</b>
                                    </ng-template>
                                </ngx-datatable-column>

                                <ngx-datatable-column [width]="60" name="Writing No" prop="ExamWritingNo"
                                    [draggable]="false" [sortable]="false">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <b>{{ value }}</b>
                                    </ng-template>
                                </ngx-datatable-column> -->

                                <ngx-datatable-column [maxWidth]="90" name="Type" prop="MCQOnly" [draggable]="false"
                                    cellClass="text-center" headerClass="text-center" [sortable]="false">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <span title="{{value === true? 'Yes':'No'}}">
                                            {{value === true? 'MCQ':'Mixed'}} </span>
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column [maxWidth]="100" name="Starts From" prop="StartDate"
                                    [draggable]="false" [sortable]="false" cellClass="text-center"
                                    headerClass="text-center">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <b *ngIf="value">{{ value | amDateFormat: 'DD MMM, YYYY hh:mm A' }}</b>
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column [maxWidth]="100" name="Ends At" prop="EndDate" [draggable]="false"
                                    cellClass="text-center" headerClass="text-center" [sortable]="false">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <b *ngIf="value">{{ value | amDateFormat: 'DD MMM, YYYY hh:mm A' }}</b>
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column [maxWidth]="100" name="Duration" prop="DurationMnt"
                                    [draggable]="false" [sortable]="false">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <b>{{ (value-(value % 60))/60 + 'h ' + value % 60 + 'm' }}</b>
                                    </ng-template>
                                </ngx-datatable-column>

                                <ngx-datatable-column [maxWidth]="100" name="Random Q." prop="Random"
                                    [draggable]="false" cellClass="text-center" headerClass="text-center"
                                    [sortable]="false">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <span title="{{value === true? 'Yes':'No'}}">
                                            {{value === true? 'Yes':'No'}} </span>
                                    </ng-template>
                                </ngx-datatable-column>

                                <ngx-datatable-column [maxWidth]="100" name="Publish" prop="Publish" [draggable]="false"
                                    cellClass="text-center" headerClass="text-center" [sortable]="false">
                                    <ng-template let-value="value" ngx-datatable-cell-template>
                                        <span title="{{value === true? 'Yes':'No'}}">
                                            {{value === true? 'Yes':'No'}} </span>
                                    </ng-template>
                                </ngx-datatable-column>

                                <ngx-datatable-column [maxWidth]="70" name="Action" prop="Id" [draggable]="false"
                                    [sortable]="false" cellClass="text-center" headerClass="text-center">
                                    <ng-template let-row="row" ngx-datatable-cell-template>
                                        <button class="btn btn-outline-primary btn-mini"
                                            [routerLink]="['/certification-test-entry']" [queryParams]="{ id: row.Id }"
                                            queryParamsHandling="merge"><i class="feather icon-edit"></i>
                                        </button>

                                    </ng-template>
                                </ngx-datatable-column>
                            </ngx-datatable>
                        </div>

                    </div>
                </div>
                <div class="card-footer">

                </div>
                <!-- end of card-footer -->
            </div>
        </div>
    </div>


</block-ui>