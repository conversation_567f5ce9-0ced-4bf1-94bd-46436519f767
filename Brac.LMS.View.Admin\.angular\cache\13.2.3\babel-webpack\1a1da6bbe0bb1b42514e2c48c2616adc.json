{"ast": null, "code": "import { __awaiter, __decorate } from \"tslib\";\nimport { BlockUI } from 'ng-block-ui';\nimport { Page } from '../_models/page';\nimport { ResponseStatus } from '../_models/enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"ng-block-ui\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/flex-layout/extended\";\nimport * as i8 from \"ngx-pagination\";\nimport * as i9 from \"ngx-moment\";\n\nconst _c0 = function (a0, a1) {\n  return {\n    \"left-div-active\": a0,\n    \"left-div-inactive\": a1\n  };\n};\n\nfunction NotificationListComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelementStart(1, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_17_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const item_r2 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return ctx_r4.onClickNotification(item_r2);\n    });\n    i0.ɵɵelement(2, \"div\", 17);\n    i0.ɵɵelementStart(3, \"div\", 18);\n    i0.ɵɵelementStart(4, \"h3\", 19);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 20);\n    i0.ɵɵelementStart(7, \"div\", 21);\n    i0.ɵɵelementStart(8, \"div\", 22);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 23);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c0, !item_r2.Seen, item_r2.Seen));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r2.Title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", item_r2.Details, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(12, 4, item_r2.CreatedOn, \"DD-MMM-YYYY hh:mm:ss A\"), \"\");\n  }\n}\n\nfunction NotificationListComponent_div_19_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.page.showingResult());\n  }\n}\n\nfunction NotificationListComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵelementStart(1, \"div\", 25);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_19_p_2_Template, 2, 1, \"p\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 27);\n    i0.ɵɵelementStart(4, \"nav\", 28);\n    i0.ɵɵelementStart(5, \"pagination-controls\", 29);\n    i0.ɵɵlistener(\"pageChange\", function NotificationListComponent_div_19_Template_pagination_controls_pageChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return ctx_r7.setPage($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.page);\n  }\n}\n\nconst _c1 = function (a0, a1, a2) {\n  return {\n    itemsPerPage: a0,\n    currentPage: a1,\n    totalItems: a2\n  };\n}; // import * as moment from 'moment';\n\n\nexport class NotificationListComponent {\n  constructor(router, _service, toastr, route) {\n    this.router = router;\n    this._service = _service;\n    this.toastr = toastr;\n    this.route = route;\n    this.rows = [];\n    this.page = new Page();\n    this.loading = false;\n    this.unseenNotification = 0;\n    this.unseenOnly = '0';\n    this.subscriptios = [];\n    this.clickedAll = 'active';\n    this.clickedUnseen = '';\n    this.activeItemId = '';\n    this.page.startsFrom = 1;\n    this.page.pageNumber = 1;\n    this.page.size = 10;\n  }\n\n  ngOnInit() {\n    this.getList();\n  }\n\n  ngOnDestroy() {\n    this.subscriptios.forEach(subscription => {\n      subscription.unsubscribe();\n    });\n  }\n\n  setPage(pageNumber) {\n    this.page.pageNumber = pageNumber;\n    this.getList();\n  }\n\n  loadList() {\n    let tabName = this.unseenOnly == '0' ? 'all' : 'unseen';\n\n    if (tabName == 'all') {\n      localStorage.setItem('filter-tab', 'all');\n    } else if (tabName == 'unseen') {\n      localStorage.setItem('filter-tab', 'unseen');\n    }\n\n    this.page.pageNumber = 1;\n    this.getList();\n  }\n\n  getList(activeNotificationId = null) {\n    this.blockUI.start('Getting data. Please wait...');\n    let obj = {\n      size: this.page.size,\n      pageNumber: this.page.pageNumber - 1,\n      unseenOnly: this.unseenOnly === '1' || localStorage.getItem('filter-tab') === 'unseen',\n      activeNotificationId: activeNotificationId\n    };\n\n    this._service.get('notification/admin/list', obj).subscribe({\n      next: res => {\n        this.loading = false;\n\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.rows = res.Data.Records;\n        this.unseenOnly = res.Data.Unseen;\n        this.page.pageTotalElements = res.Data.Records.length;\n        this.page.totalElements = res.Data.Total;\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n        this.unseenNotification = res.Data.UnseenTotal;\n\n        this._service.notificationTracker.next(this.unseenNotification);\n\n        this.setFilterTab(this.unseenOnly ? 'unseen' : 'all');\n        this.filterTab();\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => {\n        this.blockUI.stop();\n        localStorage.setItem('notification-payload', undefined);\n      }\n    });\n  }\n\n  filterTab() {\n    if (this.unseenOnly || localStorage.getItem('filter-tab') == 'unseen') {\n      // localStorage.setItem('filter-tab','unseen');\n      this.clickedUnseen = 'active';\n      this.clickedAll = '';\n    } else {\n      // localStorage.setItem('filter-tab','all');\n      this.clickedUnseen = '';\n      this.clickedAll = 'active';\n    }\n  }\n\n  setFilterTab(set) {\n    localStorage.setItem('filter-tab', set);\n  }\n\n  resetFilterTab() {\n    localStorage.setItem('filter-tab', '');\n  }\n\n  onClickNotification(item) {\n    return __awaiter(this, void 0, void 0, function* () {\n      localStorage.setItem('notification-payload', JSON.stringify(item));\n      localStorage.setItem('active-notification-id', item.Id);\n\n      try {\n        if (!item.Seen) {\n          yield this.seenNotification(item.Id);\n          item.Seen = true;\n          this.unseenNotification--;\n\n          this._service.notificationTracker.next(this.unseenNotification);\n        }\n\n        switch (item.NavigateTo) {\n          case 'ForumPostDetails':\n            this.router.navigate(['forum-post-details', item.Payload]);\n            break;\n\n          case 'CertificateTestAnswersheet':\n            this.router.navigate(['check-trainee-answersheet', item.Payload]);\n            break;\n\n          case 'EvaluationTestAnswersheet':\n            this.router.navigate(['check-trainee-evaluation-answersheet', item.Payload]);\n            break;\n        }\n      } catch (err) {\n        this.toastr.warning(err.message || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n      }\n    });\n  }\n\n  deleteNotification(item) {\n    this._service.post('notification/delete/' + item.Id).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      }\n\n      this.page.pageNumber = 1;\n      this.getList();\n    }, err => {\n      this.toastr.warning(err.message || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  seenNotification(id) {\n    return new Promise((resolve, reject) => {\n      this._service.get('notification/seen/' + id).subscribe(res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return resolve(null);\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return resolve(null);\n        }\n\n        resolve(null);\n      }, err => {\n        this.toastr.warning(err.message || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        reject(err.message || err);\n      });\n    });\n  }\n\n}\n\nNotificationListComponent.ɵfac = function NotificationListComponent_Factory(t) {\n  return new (t || NotificationListComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n};\n\nNotificationListComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: NotificationListComponent,\n  selectors: [[\"app-notification-list\"]],\n  decls: 20,\n  vars: 17,\n  consts: [[1, \"row\"], [1, \"col-lg-10\", \"col-12\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-light\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [1, \"mt-1\", \"mb-4\"], [\"role\", \"group\", \"aria-label\", \"Basic radio toggle button group\", 1, \"btn-group\", \"btn-group-sm\", \"mb-3\"], [\"type\", \"radio\", \"name\", \"unseenOnly\", \"id\", \"btnAll\", \"autocomplete\", \"off\", \"value\", \"0\", 1, \"btn-check\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"for\", \"btnAll\"], [\"type\", \"radio\", \"name\", \"unseenOnly\", \"id\", \"btnUnseenOnly\", \"autocomplete\", \"off\", \"value\", \"1\", 1, \"btn-check\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"for\", \"btnUnseenOnly\"], [\"id\", \"notification-settings\"], [\"class\", \"row border-bottom py-3\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"row text-center\", 4, \"ngIf\"], [1, \"row\", \"border-bottom\", \"py-3\"], [1, \"col-12\", \"cursor-pointer\", 3, \"click\"], [1, \"left-div-active\", \"me-2\", 3, \"ngClass\"], [1, \"fs-sm\", \"ps-sm-3\"], [1, \"mb-1\", \"fw-500\"], [1, \"d-sm-flex\", \"text-heading\", \"align-items-center\"], [1, \"d-flex\", \"align-items-center\"], [1, \"\"], [1, \"ms-sm-auto\", \"text-muted\", \"fs-xs\", \"pt-2\"], [1, \"row\", \"text-center\"], [1, \"col-md-3\", \"col-xs-12\"], [4, \"ngIf\"], [1, \"col-md-9\", \"col-xs-12\"], [1, \"align-items-center\"], [3, \"pageChange\"]],\n  template: function NotificationListComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"div\", 4);\n      i0.ɵɵelementStart(6, \"h3\", 5);\n      i0.ɵɵtext(7, \"My Notifications\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(8, \"hr\", 6);\n      i0.ɵɵelementStart(9, \"div\", 7);\n      i0.ɵɵelementStart(10, \"input\", 8);\n      i0.ɵɵlistener(\"ngModelChange\", function NotificationListComponent_Template_input_ngModelChange_10_listener($event) {\n        return ctx.unseenOnly = $event;\n      })(\"change\", function NotificationListComponent_Template_input_change_10_listener() {\n        return ctx.loadList();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(11, \"label\", 9);\n      i0.ɵɵtext(12, \"All\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"input\", 10);\n      i0.ɵɵlistener(\"ngModelChange\", function NotificationListComponent_Template_input_ngModelChange_13_listener($event) {\n        return ctx.unseenOnly = $event;\n      })(\"change\", function NotificationListComponent_Template_input_change_13_listener() {\n        return ctx.loadList();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"label\", 11);\n      i0.ɵɵtext(15, \"Unread\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(16, \"div\", 12);\n      i0.ɵɵtemplate(17, NotificationListComponent_div_17_Template, 13, 10, \"div\", 13);\n      i0.ɵɵpipe(18, \"paginate\");\n      i0.ɵɵtemplate(19, NotificationListComponent_div_19_Template, 6, 1, \"div\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"ngModel\", ctx.unseenOnly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassMapInterpolate1(\"btn btn-outline-primary \", ctx.clickedAll, \"\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngModel\", ctx.unseenOnly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassMapInterpolate1(\"btn btn-outline-primary \", ctx.clickedUnseen, \"\");\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(18, 10, ctx.rows, i0.ɵɵpureFunction3(13, _c1, ctx.page.size, ctx.page.pageNumber, ctx.page.totalElements)));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.rows.length > 0);\n    }\n  },\n  directives: [i4.BlockUIComponent, i5.RadioControlValueAccessor, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.NgForOf, i6.NgClass, i7.DefaultClassDirective, i6.NgIf, i8.PaginationControlsComponent],\n  pipes: [i8.PaginatePipe, i9.DateFormatPipe],\n  styles: [\".notification-list.unseen{font-weight:500}.notification-list.unseen span.unseen-badge{width:12px;background:#206ad7;display:inline-flex;height:12px;left:25px;border-radius:50%;top:45%;position:absolute}.notification-list{width:100%;font-size:14px;line-height:1.4;overflow:hidden;background-color:#e6e6e6;margin:0;color:#373a3c}.notification-list .noti-body{padding:0}.notification-list ul{padding-left:0;margin-bottom:0;list-style:none}.notification-list .noti-body li{transition:all .3s ease-in-out;cursor:pointer;position:relative;padding:10px 20px 10px 50px;border-bottom:2px solid #eae2e2}.notification-list .noti-body li:hover,.notification-list .noti-body li:focus{background-color:#b5d1e3!important}.notification-list ul li{padding:20px 15px;line-height:1.2}.notification-list li .n-time{margin-left:10px}.notification-list .noti-body li .media .media-body p.noti-title{font-size:17px}.notification-list .noti-body li .media .media-body p.noti-title .n-time{font-size:14px}.notification-list .noti-body li .media .media-body p.noti-content{font-size:16px;font-weight:400;margin-bottom:0}\\n\"],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], NotificationListComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}