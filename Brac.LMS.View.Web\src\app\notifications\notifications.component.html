<block-ui>
  <!-- Page content-->
  <div class="container-fluid position-relative zindex-5 pb-0 mb-md-1 p-3 margin-top-neg-15">
    <div class="d-flex flex-column h-100  bg-tranparent-black-glass rounded-1 shadow-lg ">
      <div class="pt-2 p-md-3">
        <div class="d-sm-flex align-items-center justify-content-between pb-1 text-center text-sm-start">
          <h1 class="h3 mb-2 text-nowrap">My Notifications</h1>
        </div>
        <hr class="mt-1 mb-4">
        <!-- Content-->
        <div class="btn-group btn-group-sm mb-3" role="group" aria-label="Basic radio toggle button group">
          <input type="radio" [(ngModel)]="unseenOnly" class="btn-check" name="unseenOnly" id="btnAll"
            autocomplete="off" value="0" (change)="loadList()">
          <label class="btn btn-outline-primary {{clickedAll}}" for="btnAll">All</label>

          <input type="radio" [(ngModel)]="unseenOnly" class="btn-check" name="unseenOnly" id="btnUnseenOnly"
            autocomplete="off" value="1" (change)="loadList()">
          <label class="btn btn-outline-primary {{clickedUnseen}}" for="btnUnseenOnly">Unread</label>
        </div>
        <div id="notification-settings">

          <div class="row border-bottom py-3" *ngFor="let item of rows | paginate
            : {
                itemsPerPage: page.size,
                currentPage: page.pageNumber,
                totalItems: page.totalElements
              };
        let i = index">

            <div class="col-12 cursor-pointer" (click)="onClickNotification(item)">
              <div class="left-div-active me-2"
                [ngClass]="{'left-div-active': !item.Seen, 'left-div-inactive': item.Seen}"></div>

              <div class="fs-sm ps-sm-3 item-background {{item.Id == activeItemId?'item-active':''}}">
                <h6 class="mb-1 item-background-text">{{item.Title}}</h6>
                <div class="d-sm-flex text-heading align-items-center">
                  <div class="d-flex align-items-center item-background-text">
                    <div class="item-background-text">
                      {{item.Details}}
                    </div>
                  </div>
                </div>
                <div class="ms-sm-auto  fs-xs pt-2 font-weight-bold item-background-text">
                  {{ item.CreatedOn| amDateFormat: "DD-MMM-YYYY hh:mm:ss A" }}
                </div>
              </div>
            </div>
            <!-- <div class="col-2">
            <a class="btn btn-link text-danger fw-medium btn-sm mb-2" (click)="deleteNotification(item)"><i
                class="ai-trash-2 fs-base me-2"></i> Delete</a>
          </div> -->
          </div>
          <div class="row text-center" *ngIf="rows.length > 0">
            <div class="col-md-3 col-xs-12">
              <p *ngIf="page">{{ page.showingResult() }}</p>
            </div>
            <div class="col-md-9 col-xs-12">
              <nav class="align-items-center">
                <pagination-controls (pageChange)="setPage($event)"></pagination-controls>
              </nav>
            </div>
            <!-- end col -->
          </div>
        </div>

      </div>
    </div>
  </div>
</block-ui>