﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Presentation;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.Services
{
    public class DashboardService : IDashboardService
    {
        private readonly ApplicationDbContext _context;
        public DashboardService()
        {
            _context = new ApplicationDbContext();
        }

        public async Task<APIResponse> GetCountForAdmin()
        {
            try
            {
                var trainees = await _context.Trainees.Where(x=>x.Active).CountAsync(t => t.Active);
                var courses = await _context.Courses.CountAsync(t => t.Active && t.Published);
                var libraryItems = await _context.Libraries.CountAsync();
                var openMaterials = await _context.OpenMaterials.CountAsync();
                var evaluationTests = await _context.EvaluationExams.CountAsync();
                var forumPosts = await _context.ForumTopics.CountAsync(t => t.Status == ForumTopicStatus.Open);


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Trainees = trainees, Courses = courses, LibraryItems = libraryItems, LearningHourItems = openMaterials, EvaluationTests = evaluationTests, ForumPosts = forumPosts, }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetTraineeProgress(int size, int pageNumber, ApplicationUser user)
        {
            try
            {
                var query = _context.CourseEnrollments.AsQueryable();

                //if (user.UserType == UserType.Trainer) query = query.Where(x => x.Course.TrainerId == user.Trainer.Id);

                var data = await query
                    .GroupJoin(_context.TraineeCourseActivities, x => x.Course.Id, y => y.CourseId, (x, y) => new
                    {
                        Enrollment = x,
                        Activities = y
                    }).SelectMany(x => x.Activities.DefaultIfEmpty(), (y, z) => new { y.Enrollment, Activity = z })
                    .GroupBy(x => new { x.Enrollment.TraineeId, x.Enrollment.Trainee.User.ImagePath, x.Enrollment.Trainee.Name })
                    .Select(x => new { x.Key.TraineeId, x.Key.Name, x.Key.ImagePath, Enrolled = x.Count(), Completed = x.Count(y => y.Activity != null && y.Activity.Progress == 100) })
                    .OrderByDescending(x => x.Enrolled).Skip(pageNumber * size).Take(size).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetTopEnrollmentCourses(int limit, DateTime? startDate, DateTime? endDate, bool? status)

        {
            try
            {
                //var query = _context.TraineeCourseActivities.Where(x => (bool)(x.Course.Published == (status == null) ? x.Course.Published : status)).AsQueryable();

                var query = status==null? _context.TraineeCourseActivities.AsQueryable(): _context.TraineeCourseActivities.Where(x =>x.Course.Published ==  status).AsQueryable();
                query = (startDate == null && endDate == null) ? query : query.Where(s => s.CreatedDate >= startDate && endDate >= s.CreatedDate).AsQueryable();
                var data = await query.GroupBy(x => new { x.CourseId, x.Course.Title, x.Course.ShortTitle })
                   .Select(x => new
                   {
                       NoOfEnrollments = x.Count(),
                       x.Key.CourseId,
                       CourseTitle = x.Key.Title,
                       CourseShortTitle = x.Key.ShortTitle,
                       NoOfTraineesCompleted = x.Count(y => y.Progress == 100),
                       CountMutiPercant = x.Count() * 100,
                       Progress = x.Sum(y => y.Progress),
                   })
                     .OrderByDescending(x => x.NoOfEnrollments)
                     .Take(limit)
                     .ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GeBelatedTraineeList(int size, int pageNumber)
        {
            try
            {
                DateTime now = DateTime.UtcNow.ToKindLocal();
                var query = _context.CourseEnrollments.AsQueryable();
                var cc = query.Count();
                var data = await query
                    .GroupJoin(_context.CourseExams, x => x.CourseId, y => y.CourseId, (x, y) => new
                    {
                        Enrollment = x,
                        Exams = y
                    }).SelectMany(x => x.Exams.DefaultIfEmpty(), (y, z) => new { y.Enrollment, Exam = z })
                    .Where(x => x.Exam != null && x.Exam.EndDate < now)
                    .GroupJoin(_context.TraineeExams, x => new { x.Enrollment.TraineeId, ExamId = x.Exam.Id },
                    y => new { y.TraineeId, y.ExamId }, (x, y) => new
                    {
                        x.Enrollment,
                        x.Exam,
                        TraineeExams = y
                    }).SelectMany(x => x.TraineeExams.DefaultIfEmpty(), (y, z) => new { y.Enrollment, y.Exam, TraineeExam = z })
                     .Select(x => new
                     {
                         x.Enrollment.CourseId,
                         Name = x.Enrollment.Trainee.Name,
                         Email = x.Enrollment.Trainee.Email,
                         Phone = x.Enrollment.Trainee.PhoneNo,
                         Department = x.Enrollment.Trainee.Department.Name,
                         x.Enrollment.Trainee.User.ImagePath,
                         CourseTitle = x.Enrollment.Course.Title,
                         ExamId = x.Exam != null ? x.Exam.Id : default(Guid?),
                         EndDate = x.Exam != null ? x.Exam.EndDate : default(DateTime?)
                     })
                    .OrderBy(x => x.EndDate).Skip(pageNumber * size).Take(size).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetMonthWiseCertificateGains(int monthCount, DateTime startDate, DateTime endDate, bool? status, int courseLimit, ApplicationUser user)
        {
            try
            {
                endDate = endDate == Convert.ToDateTime("01/01/0001") ? DateTime.Today : endDate;
                startDate = startDate == Convert.ToDateTime("01/01/0001") ? new DateTime(endDate.Year, endDate.Month, 1).AddMonths(-monthCount) : startDate;
                endDate = endDate == DateTime.Today ? endDate.AddDays(1).AddTicks(-1) : endDate;

                var query = _context.TraineeExams.Where(x => x.CertificateAchieved && x.MarkedOn >= startDate && x.MarkedOn <= endDate && (x.Exam.Course.Published == ((bool)((status == null) ? x.Exam.Course.Published : status)))).AsQueryable();
                //var query = _context.TraineeExams.Where(x => x.CertificateAchieved && x.MarkedOn >= startDate && x.MarkedOn<=endDate).AsQueryable();

                //if (user.UserType == UserType.Trainer) query = query.Where(x => x.Exam.Course.TrainerId == user.Trainer.Id);
                //if (user.UserType != UserType.Admin) query = query.Where(x => x.TraineeId == user.Trainee.Id);

                var list = await query
                    .GroupBy(x => new { x.Exam.CourseId, CourseTitle = x.Exam.Course.Title , CourseShortTitle = x.Exam.Course.ShortTitle })
                    .Select(x => new
                    {
                        x.Key.CourseId,
                        x.Key.CourseTitle,
                        x.Key.CourseShortTitle,
                        Total = x.Count(),
                        Months = x.GroupBy(y => new { y.MarkedOn.Value.Month, y.MarkedOn.Value.Year })
                        .Select(y => new { y.Key.Year, y.Key.Month, Certificates = y.Count() }).ToList()
                    })
                    .OrderByDescending(x => x.Total)
                    //.Take(courseLimit)
                    .ToListAsync();

                List<dynamic> data = new List<dynamic>(), months, certificates, course;
                course = new List<dynamic>();
                foreach (var item in list)
                {
                    course.Add(new
                    {
                        Id = item.CourseId,
                        Name = item.CourseTitle,
                        ShortName=item.CourseShortTitle
                    });
                }
                months = new List<dynamic>();
                if (list.Count != 0)
                {
                    for (DateTime i = startDate; i <= endDate; i = i.AddMonths(1))
                    {
                        certificates = new List<dynamic>();

                        foreach (var itemlist in list)
                        {
                            certificates.Add(itemlist.Months.FirstOrDefault(x => x.Month == i.Month && x.Year == i.Year)?.Certificates ?? 0);
                        }

                        data.Add(new
                        {
                            Month = i.ToString("MMM-yy"),
                            certificates
                        });
                    }


                }


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Course = course
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetMonthWiseEnrollments(int monthCount, DateTime startDate, DateTime endDate, bool? status, ApplicationUser user)
        {
            try
            {
                endDate = endDate == Convert.ToDateTime("01/01/0001") ? DateTime.Today : endDate;
                startDate = startDate == Convert.ToDateTime("01/01/0001") ? new DateTime(endDate.Year, endDate.Month, 1).AddMonths(-monthCount) : startDate;
                endDate = endDate == DateTime.Today ? endDate.AddDays(1).AddTicks(-1) : endDate;

                var query = _context.CourseEnrollments
                    .Where(x => x.EnrollmentDate >= startDate && x.EnrollmentDate <= endDate && (x.Course.Published == ((bool)((status == null) ? x.Course.Published : status)))).AsQueryable();


                var lists = await query
                    .GroupBy(x => new { x.EnrollmentDate.Month, x.EnrollmentDate.Year })
                    .Select(x => new
                    {
                        Total = x.Count(),
                        Month = x.Key.Month,
                        Year = x.Key.Year

                    })
                    .OrderBy(x => new {x.Month,x.Year})
                    .ToListAsync();
                var progressQuery = _context.TraineeCourseActivities.Where(x=>x.CreatedDate>=startDate && x.CreatedDate <= endDate &&  x.NoOfContentsStudied < (x.HasCertification ? x.NoOfContents+1 : x.NoOfContents) && (x.Course.Published == ((bool)((status == null) ? x.Course.Published : status)))).AsQueryable();
                var progressLists = await progressQuery.OrderByDescending(x=>x.CreatedDate)
                   .GroupBy(x => new { x.CreatedDate.Month, x.CreatedDate.Year })
                   .Select(x => new
                   {
                       ProgressTotal = x.Count(),
                       Month = x.Key.Month,
                       Year = x.Key.Year

                   })
                   //.OrderByDescending(x => new { x.Month ,x.Year} )
                   .ToListAsync();
                List<dynamic> data = new List<dynamic>(), Year,Months, Total;
                List<dynamic> dataProgress = new List<dynamic>(),YearP, MonthsP, Progress;
                foreach (var list in lists)
                {
                    
                        data.Add(new
                        {
                            Year = list.Year,
                            Months = new DateTime(list.Year, list.Month, 1).ToString("MMM-yy"),
                            Total = list.Total
                        });
                }
                foreach (var list in progressLists)
                {

                    dataProgress.Add(new
                    {
                        YearP = list.Year,
                        MonthsP = new DateTime(list.Year, list.Month, 1).ToString("MMM-yy"),
                        Progress = list.ProgressTotal
                    });
                }
                var datas = data.GroupJoin(dataProgress, d => d.Months, dp => dp.MonthsP, (d, dp) => new {Year = d.Year, Data = d, DataP = dp.FirstOrDefault() });
                var datasets =  datas.OrderBy(p=>p.Year).ToList();
                List<dynamic> records = new List<dynamic>();
                var total = 0;
                foreach (var item in datasets)
                {
                    total += item.Data.Total;
                    records.Add(new {
                    Month=item.Data.Months,
                        New = item.Data.Total,
                        Total = total,
                    Progress =item.DataP?.Progress
                    });
                }
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = records
                    }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }

        }

        public async Task<APIResponse> GetCountForTrainer(ApplicationUser user)
        {
            try
            {
                var trainees = await _context.CourseEnrollments.Where(t => t.Trainee.Active)
                    .GroupBy(x => x.TraineeId).CountAsync();
                var courses = await _context.Courses.CountAsync(t => t.Active);
                var courseMaterials = await _context.CourseMaterials
                    .GroupBy(x => x.MaterialType).Select(x => new { Type = x.Key, Total = x.Count() }).ToListAsync();
                var mockTests = await _context.CourseMockTests.CountAsync();
                var certificationTests = await _context.CourseExams.CountAsync(x => x.Publish);


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Record = new { Trainees = trainees, Courses = courses, Documents = courseMaterials.FirstOrDefault(x => x.Type == MaterialType.Document)?.Total ?? 0, Videos = courseMaterials.FirstOrDefault(x => x.Type == MaterialType.Video)?.Total ?? 0, MockTests = mockTests, CertificationTests = certificationTests }
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetCountForTrainee(ApplicationUser user)
        {
            try
            {
                var enrollments = await _context.CourseEnrollments.CountAsync(t => t.TraineeId == user.Trainee.Id && t.Course.Published == true && t.Course.Active == true);
                var bookmarks = await _context.CourseBookmarks.CountAsync(t => t.TraineeId == user.Trainee.Id && t.Course.Published);
                var certificaates = _context.TraineeExams.Where(x => x.TraineeId == user.Trainee.Id && x.CertificateAchieved).GroupBy(x => x.ExamId).CountAsync().Result;
                //var exams = await _context.TraineeExams.CountAsync(x => x.TraineeId == user.Trainee.Id);
                var exams = _context.TraineeExams.Where(x => x.TraineeId == user.Trainee.Id).GroupBy(x => x.ExamId).CountAsync().Result;
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Enrollments = enrollments, Bookmarks = bookmarks, Certificates = certificaates, ExamTaken = exams }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }


    }

    public interface IDashboardService
    {
        Task<APIResponse> GetCountForAdmin();
        Task<APIResponse> GetTraineeProgress(int size, int pageNumber, ApplicationUser user);
        Task<APIResponse> GetTopEnrollmentCourses(int limit, DateTime? startDate, DateTime? endDate, bool? status);
        Task<APIResponse> GeBelatedTraineeList(int size, int pageNumber);
        Task<APIResponse> GetMonthWiseCertificateGains(int monthCount, DateTime startDate, DateTime endDate, bool? status, int courseLimit, ApplicationUser user);
        Task<APIResponse> GetMonthWiseEnrollments(int monthCount, DateTime startDate, DateTime endDate, bool? status, ApplicationUser user);
        Task<APIResponse> GetCountForTrainer(ApplicationUser user);
        Task<APIResponse> GetCountForTrainee(ApplicationUser user);
    }
}
