<block-ui>
  <!-- Page content-->
  <div class="container-fluid position-relative zindex-5 pb-0 mb-md-1 p-3 margin-top-neg-15">
    <div class="row">
      <!-- Sidebar-->

      <!-- Content-->
      <div class="col p-5" *ngIf="pagedetail" [ngClass]="{'col-lg-12': quizRunning}">
        <div class="row h-100 bg-tranparent-black-glass rounded-1 shadow-lg p-4">
          <div class="pt-2 p-md-3">
            <div class="col-12 d-flex justify-content-between">
              <h3 class="h3 text-break d-flex align-items-center">
                {{ pagedetail.CourseTitle }}
              </h3>
              <a class="btn btn-link fs-4 fw-bold btn-sm d-flex align-items-center border-start"
                (click)="backClicked()"><i class="fs-4 ai-arrow-left fs-base me-2"></i>Go Back</a>
            </div>

            <div class="col-12 mb-3">
              <tabset>
                <tab (selectTab)="changeTab('Exam', $event)">
                  <ng-template tabHeading> Mock Test </ng-template>

                  <div class="section" *ngIf="pagedetail">
                    <div class="col-12">
                      <div class="row">
                        <div class="col-sm-12" *ngIf="!quizRunning">
                          <div class="sidebar">
                            <div class="widget widget_recent_post">
                              <div class="row">
                                <div class="col-md-6">
                                  <ul class="list_none blog_meta">
                                    <li class="text-break">
                                      <i class="fa fa-arrow-right me-2"></i>
                                      Test Name : <b>{{ pagedetail.ExamName }}</b>
                                    </li>
                                    <li>
                                      <i class="fa fa-arrow-right me-2"></i>
                                      Total Marks : {{ pagedetail.Marks }}
                                    </li>
                                    <li>
                                      <i class="fa fa-arrow-right me-2"></i>
                                      Question Type : MCQ
                                    </li>
                                  </ul>
                                </div>
                                <div class="col-md-6">
                                  <ul class="list_none blog_meta">
                                    <li>
                                      <i class="fa fa-arrow-right me-2"></i>
                                      Total Duration :
                                      {{ getHourMint(pagedetail.DurationMnt) }}
                                    </li>
                                    <li>
                                      <i class="fa fa-arrow-right me-2"></i>
                                      Pending Quota :
                                      {{
                                      pagedetail.PendingQuota
                                      }}
                                    </li>
                                  </ul>
                                </div>

                                <div class="col-12 my-3" *ngIf="pagedetail.ExamInstructions">
                                  <accordion [isAnimated]="true">
                                    <accordion-group heading="EXAM INSTRUCTIONS (click here to see)"
                                      panelClass="custom-accordion">
                                      <div [innerHTML]="pagedetail.ExamInstructions| safe: 'html'"></div>
                                    </accordion-group>
                                  </accordion>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="col-sm-12 text-center" *ngIf="!quizRunning">
                          <h3 class="p-3 text-danger" *ngIf="!pagedetail.Allow">
                            {{notAllowMessage}}
                          </h3>

                          <button (click)="backClicked()" type="button" class="btn btn-danger btn-lg me-3">
                            <i class="fa fa-arrow-left"></i> Go Back
                          </button>
                          <button *ngIf="pagedetail.Allow" type="button" class="btn btn-primary btn-lg"
                            (click)="startQuiz()">
                            Start Exam <i class="fa fa-arrow-right"></i>
                          </button>
                        </div>
                        <div class="row">
                          <!--start: quiz trace controls-->
                          <div class="col-12 mb-3" *ngIf="quizRunning">
                            <div class="d-flex justify-content-center">
                              <!-- pagination.component.html -->
                              <app-pagination [totalItems]="questionList.length" [paginationControlToShow]="10"
                                [itemsPerPage]="page.size" [definitions]="questionList"
                                [currentPage]="this.page.pageNumber"
                                (pageChange)="onPageChange($event)"></app-pagination>
                              <!-- <pagination-controls id="p"
        previousLabel="Prev"
        nextLabel="Next"                    
        (pageChange)="setPage($event)"
      >
      </pagination-controls>
      <ul class="d-none">
        <li                     
          *ngFor="
            let q of questionList | paginate : {
                    id:'p',
                    itemsPerPage: page.size,
                    currentPage: page.pageNumber,
                    totalItems: questionList.length
                  };
            let i = index
          ">
          {{ q.Id }}
        </li>
      </ul> -->
                            </div>
                          </div>
                          <!--end: quiz trace controls-->
                        </div>
                        <div class="col-12" *ngIf="quizRunning">
                          <div class="card q-panel noselect">
                            <div class="card-header">
                              <div class="row">
                                <div class="col-lg-8 col-md-5 col-12 qs-counter">
                                  <i class="fa fa-question-circle"></i>
                                  <strong>
                                    QUESTION {{ qIndex + 1 }} of
                                    {{ questionList.length }}</strong>
                                </div>
                                <div class="col-lg-4 col-md-5 col-12 timer">
                                  <i class="fa fa-clock-o"></i>
                                  <strong>
                                    Time Left:
                                    <b>{{ timer.hour }}h : {{ timer.minute }}m :
                                      {{ timer.second }}s</b></strong>
                                </div>
                                <!-- <div class="col-lg-2 col-md-2 col-12 ans-summary">
                                                <i class="fa fa-check"></i> ANS {{noOfAnsweredQs}} of {{questionList.length}}
                                            </div> -->
                              </div>
                            </div>
                            <div class="question-body noselect" *ngIf="timer.isRunning">
                              <!------------ MCQ Question ------------>
                              <div class="card-body" [@inOutAnimation] *ngIf="timer.isRunning">
                                <div class="row">
                                  <div class="col-sm-11 col-10">
                                    <p class="text-question mb-0">
                                      {{ questionList[qIndex].Question }}
                                    </p>
                                  </div>
                                  <div class="col-sm-1 col-2">
                                    <p class="text-question mb-0">
                                      [{{ questionList[qIndex].Mark }}]
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <ul class="list-group" [@inOutAnimation] *ngIf="timer.isRunning">
                                <li class="list-group-item"
                                  *ngFor="let option of questionList[qIndex].Options; let oi = index;">
                                  <input class="form-check-input" (click)="selectSpecific(questionList[qIndex].Options)"
                                    type="radio" [name]="'radioGroup' + qIndex" [(ngModel)]="option.Selected"
                                    [id]="'option' + oi + '-' + qIndex" [value]="option.Text" />

                                  <label class="form-check-label" [for]="'option' + oi + '-' + qIndex">
                                    {{ option.Text }}
                                  </label>
                                </li>
                              </ul>
                              <!------------ End MCQ Question ------------>
                            </div>
                            <div class="card-footer d-flex justify-content-center">
                              <button type="button" class="btn btn-info btn-box me-1" (click)="prevQuestion()"
                                [disabled]="qIndex === 0">
                                <i class="fa fa-arrow-left"></i> Prev
                              </button>
                              <button type="button" class="btn btn-success me-1" (click)="nextQuestion()">
                                {{ btnNextText }}
                                <i class="fa fa-arrow-right"></i>
                              </button>
                              <!-- <button type="button" class="btn btn-danger float-right" (click)="pauseExam()">
                                Take a Break <i class="fa fa-pause-circle"></i>
                              </button> -->
                            </div>
                          </div>
                        </div>

                        <div class="col-sm-10 col-sm-12" *ngIf="timer && timer.isRunning">
                          <div class="card" *ngIf="mcqList.length > 0">
                            <div class="card-header">
                              <h5 class="card-title mb-0">MCQ</h5>
                            </div>
                            <div class="card-body pt-3">
                              <div class="col-sm-12" *ngFor="let item of mcqList; let i = index">
                                <div class="row">
                                  <div class="col-sm-11 col-10">
                                    <b>{{ i + 1 }}. {{ item.Question }}</b>
                                  </div>
                                  <div class="col-sm-1 col-2">
                                    <b>[{{ item.Mark }}]</b>
                                  </div>
                                </div>
                                <div class="row">
                                  <div class="col-sm-12">
                                    <div class="row">
                                      <div class="col-lg-6 col-sm-6 col-12">
                                        <input type="checkbox" id="option0{{ i }}"
                                          [(ngModel)]="item.Options[0].Selected" />
                                        a.
                                        <label class="ms-2 option-text" for="option0{{ i }}">{{ item.Options[0].Text
                                          }}</label>
                                      </div>
                                      <div class="col-lg-6 col-sm-6 col-12">
                                        <input type="checkbox" id="option1{{ i }}"
                                          [(ngModel)]="item.Options[1].Selected" />
                                        b.
                                        <label class="ms-2 option-text" for="option1{{ i }}">{{ item.Options[1].Text
                                          }}</label>
                                      </div>
                                      <div class="col-lg-6 col-sm-6 col-12">
                                        <input type="checkbox" id="option2{{ i }}"
                                          [(ngModel)]="item.Options[2].Selected" />
                                        c.
                                        <label class="ms-2 option-text" for="option2{{ i }}">{{ item.Options[2].Text
                                          }}</label>
                                      </div>
                                      <div class="col-lg-6 col-sm-6 col-12">
                                        <input type="checkbox" id="option3{{ i }}"
                                          [(ngModel)]="item.Options[3].Selected" />
                                        d.
                                        <label class="ms-2 option-text" for="option3{{ i }}">{{ item.Options[3].Text
                                          }}</label>
                                      </div>
                                    </div>
                                    <hr class="mb-3 mt-3" />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- <div class="col-sm-10 col-sm-12" *ngIf="allowAnswerSubmit">
                                <div class="col-sm-12 text-center mt-3">
                                    <button type="button" (click)="stopQuiz()" class="btn btn-primary mr-3"> <i
                                            class="fa fa-times"></i> Cancel</button>

                                    <button type="button" class="btn btn-default" (click)="submitAnswer()">
                                        <i class="fa fa-check"></i> Submit</button>
                                </div>
                            </div> -->
                      </div>
                    </div>
                    <!-- end container -->
                  </div>
                </tab>

                <tab (selectTab)="changeTab('Results', $event)">
                  <ng-template tabHeading> Results </ng-template>

                  <div class="row" *ngIf="pagedetail">
                    <div class="col-lg-12 cmt-style-1">
                      <ngx-datatable class="material" [scrollbarH]="true" [rows]="resultList" columnMode="force"
                        [headerHeight]="40" [footerHeight]="50" rowHeight="auto" [limit]="10">
                        <ngx-datatable-column [maxWidth]="80" name="SL#" [draggable]="false" [sortable]="false">
                          <ng-template let-row="row" let-rowIndex="rowIndex" ngx-datatable-cell-template>
                            <span> {{ rowIndex + 1 }} </span>
                          </ng-template>
                        </ngx-datatable-column>

                        <ngx-datatable-column prop="StartDate" name="Participation Date" [draggable]="false"
                          [sortable]="false" cellClass="text-center" headerClass="text-center">
                          <ng-template let-value="value" ngx-datatable-cell-template>
                            <span>
                              {{ value | amDateFormat: "DD-MMM-YYYY hh:mm:ss A" }}</span>
                          </ng-template>
                        </ngx-datatable-column>

                        <ngx-datatable-column prop="TotalMarks" name="Total Marks" [draggable]="false"
                          [sortable]="false" cellClass="text-center" headerClass="text-center">
                          <ng-template let-value="value" ngx-datatable-cell-template>
                            <span>{{ value }}</span>
                          </ng-template>
                        </ngx-datatable-column>

                        <ngx-datatable-column prop="GainedMarks" name="Gained Marks" [draggable]="false"
                          [sortable]="false" cellClass="text-center" headerClass="text-center">
                          <ng-template let-value="value" ngx-datatable-cell-template>
                            <span>{{ value }}</span>
                          </ng-template>
                        </ngx-datatable-column>
                      </ngx-datatable>
                    </div>
                  </div>
                </tab>
              </tabset>
            </div>
            <hr class="mt-1 mb-4" />
          </div>
        </div>
      </div>
    </div>
  </div>

  <ngx-smart-modal #resultModal identifier="resultModal" [escapable]="false" [dismissable]="false"
    customClass="nsm-dialog-animation-btt" (onAnyCloseEventFinished)="resultModalClose()">
    <div class="card" *ngIf="result">
      <div class="card-body">
        <h3 class="card-title">Result Summary</h3>
        <!-- <highcharts-chart *ngIf="chartOptions" [Highcharts]="highcharts" [options]="chartOptions" class="cmt-style-2">
          </highcharts-chart>
          <h5 class="text-success text-center" *ngIf="result.Scored >= 80">
              Excellent performance!
          </h5>
    -->
        <ul class="list-group list-group-flush">
          <li class="list-group-item bold">
            Correct Answer
            <span class="label label-success float-right">{{ result.NoOfCorrectAnsweredQs }} out of
              {{ result.Questions.length }}</span>
          </li>
          <li class="list-group-item bold">
            Obtained Percentage
            <span class="label label-success float-right">{{ result.Scored }}%</span>
          </li>
        </ul>
      </div>
      <div class="card-footer text-center">
        <button type="button" class="btn btn-success btn-sm me-2" (click)="resultDetailsModal.open()">
          Show Result
        </button>
        <button type="button" (click)="resultModalClose()" class="btn btn-danger btn-sm ml-1">
          Close
        </button>
      </div>
    </div>
  </ngx-smart-modal>

  <ngx-smart-modal #resultDetailsModal identifier="resultDetailsModal" [escapable]="false" [dismissable]="false"
    customClass="nsm-dialog-animation-btt custom-modal">
    <div class="card" *ngIf="result">
      <div class="card-body">
        <h3 class="card-title">Mock Test Result</h3>
        <div class="card">
          <div class="card-body">
            <div class="col-xs-12">
              <div class="row mb-3" *ngFor="let item of result.Questions; let i = index">
                <div class="col-xs-12">
                  <b [ngClass]="{ 'text-danger': !item.Answered }">{{ i + 1 }}. {{ item.Question }}</b>
                  <label *ngIf="!item.Answered" class="badge bg-danger ms-2">not answered</label>
                </div>
                <div class="col-xs-12">
                  <div class="row">
                    <div class="col-lg-6" *ngFor="let option of item.Options">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" [checked]="option.answered" disabled />
                        <label class="form-check-label" [ngClass]="{
                          'text-success': option.correct,
                          'text-danger': !option.correct && option.answered
                        }">
                          {{ option.value }}
                          <span *ngIf="option.correctAns && !option.answered" class="badge bg-info">Correct
                            Answer</span>
                          <span *ngIf="option.answered" class="badge" [ngClass]="{
                            'bg-success': option.correct,
                            'bg-danger': !option.correct
                          }">You Answered</span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="card-footer text-center">
        <button type="button" (click)="resultDetailsModal.close() && resultModalClose()"
          class="btn btn-info btn-sm ml-1">
          Close
        </button>
      </div>
    </div>
  </ngx-smart-modal>
</block-ui>