import {
  Component,
  ElementRef,
  OnInit,
  Renderer2,
  ViewEncapsulation,
} from "@angular/core";
import * as Highcharts from "highcharts";
import { CommonService } from "../_services/common.service";
import { Router } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { AuthenticationService } from "../_services/authentication.service";
import { Page } from "../_models/page";
import { environment } from "src/environments/environment";
import { ignoreElements } from "rxjs/operators";
import { ResponseStatus } from "../_models/enum";
import { NgbTooltipConfig } from "@ng-bootstrap/ng-bootstrap";
import { BlockUI, NgBlockUI } from "ng-block-ui";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import * as moment from "moment";
import { BsDaterangepickerConfig } from "ngx-bootstrap/datepicker";
import { AppComponent } from "../app.component";
@Component({
  selector: "app-dashboard-admin",
  templateUrl: "./dashboard-admin.component.html",
  styleUrls: ["./dashboard-admin.component.scss"],
  providers: [NgbTooltipConfig],
  encapsulation: ViewEncapsulation.None,
})
export class AdminViewComponent implements OnInit {
  @BlockUI() blockUI: NgBlockUI;
  counter: any;
  limit: Number = 5;
  page = new Page();
  baseUrl = environment.baseUrl;
  filterForm: FormGroup;
  traineeProgressList = { list: [], total: 0 };
  traineePerformance = { list: [], total: 0 };
  belatedTraineeList = { list: [], total: 0 };
  certificateList = { list: [], total: 0 };
  courseProgressList = { list: [], total: 0 };
  enrollmentList = {
    list: [],
    month: null,
    new: null,
    total: null,
    progrss: null,
  };
  statusList: Array<any> = [
    { Id: true, Name: "Published" },
    { Id: false, Name: "UnPublished" },
  ];
  bsConfig: Partial<BsDaterangepickerConfig>;
  Highcharts: typeof Highcharts = Highcharts;
  traineePerformanceChartOptions: any;
  enrollChartOptions: any;
  certificateChartOptions: any;
  courseChartOptions: any;
  chart1Options = {
    chart: {
      type: "bar",
    },
    title: {
      text: "Obtained Certificates In Last 6 Months",
    },

    legend: {
      layout: "vertical",
      align: "left",
      verticalAlign: "top",
      x: 250,
      y: 100,
      floating: true,
      borderWidth: 1,
    },
    //  backgroundColor: (
    //     (Highcharts.theme && Highcharts.theme.legendBackgroundColor) ||
    //       '#FFFFFF'), shadow: true
    //  },
    xAxis: {
      categories: ["Bangla", "English", "Word", "Powerpoint", "Excel"],
      title: {
        text: null,
      },
    },
    yAxis: {
      min: 0,
      title: {
        text: "Certificate Count",
        align: "high",
      },
      labels: {
        overflow: "justify",
      },
    },

    plotOptions: {
      bar: {
        dataLabels: {
          enabled: true,
        },
      },
    },
    credits: {
      enabled: false,
    },
    series: [
      {
        name: "Month Feb",
        data: [17, 31, 65, 23, 2],
      },
      {
        name: "Month March",
        data: [145, 16, 97, 40, 63],
      },
      {
        name: "Month April",
        data: [9, 4, 5, 2, 15],
      },
    ],
  };

  constructor(
    private _service: CommonService,
    private appComponent: AppComponent,
    private renderer: Renderer2,
    private el: ElementRef,
    config: NgbTooltipConfig,
    private router: Router,
    public formBuilder: FormBuilder,
    private toastr: ToastrService,
    private authService: AuthenticationService
  ) {
    this.page.pageNumber = 0;
    this.page.size = 3;
    //    config.placement = 'right';
    //  config.triggers = 'click';
  }

  ngOnInit() {
    this.filterForm = this.formBuilder.group({
      dates: [[]],
      statusId: [null],
      isForCertificate: [true],
      isForEnrolledCourse: [true],
      isForEnrolledProgress: [true],
    });
    this.getCounts();
    // this.getBelatedTraineeList();
    if (this.filterForm.value.isForCertificate) this.getCertificateList();
    if (this.filterForm.value.isForEnrolledCourse) this.getCourseProgressList();
    if (this.filterForm.value.isForEnrolledProgress) this.getEnrollmentList();
  }
  ngAfterViewInit() {
    const elements = this.el.nativeElement.querySelectorAll(".point-series");
    elements.forEach((element) => {
      const color = element.getAttribute("data-color");
      this.renderer.setStyle(element, "color", color);
    });
  }
  goToPage(url) {
    this.router.navigate([url]);
  }
  handleSelectClick(selectElement) {
    this.appComponent.handleSelectClick(selectElement);
  }
  getCounts() {
    this._service.get("dashboard/get-count-for-admin").subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.counter = res.Data;
      },
      (err) => {
        this.toastr.warning(err.Messaage || err, "Warning!", {
          closeButton: true,
          disableTimeOut: false,
        });
      }
    );
  }

  getTraineeProgressList() {
    const obj = {
      name: this.filterForm.value.name
        ? this.filterForm.value.name.trim()
        : this.filterForm.value.name,
      categoryId: this.filterForm.value.categoryId,
      size: this.page.size,
      pageNumber: this.page.pageNumber,
    };
    this._service.get("dashboard/get-trainee-progress", this.page).subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.traineeProgressList.list = res.Data;
      },
      (err) => {
        this.toastr.warning(err.Messaage || err, "Warning!", {
          closeButton: true,
          disableTimeOut: false,
        });
      }
    );
  }

  getTraineePerformanceList() {
    this._service
      .get("dashboard/get-top-trainee-performance/" + this.limit)
      .subscribe(
        (res) => {
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.traineePerformance.list = [];

          res.Data.forEach((element) => {
            this.traineePerformance.list.push({
              name: element.Course,
              y: Number(element.EnrolledCount),
              exam: Number(element.Examcount),
            });
          });

          this.traineePerformanceChartOptions = {
            chart: {
              plotBorderWidth: null,
              plotShadow: false,
            },
            title: {
              text: "Top " + this.limit + " Course Progress",
            },
            tooltip: {
              pointFormat: "{series.name}: <b>{point.y:f}</b>",
            },
            credits: {
              enabled: false,
            },
            plotOptions: {
              pie: {
                allowPointSelect: true,
                cursor: "pointer",
                shadow: false,
                dataLabels: {
                  enabled: false,
                },

                showInLegend: true,
              },
            },
            series: [
              {
                type: "pie",
                name: "Enrollments",
                data: this.traineePerformance.list,
                // data: [
                //    {
                //       name: this.traineePerformance.list.map(x => x.Name),
                //       y: this.traineePerformance.list.map(x=>x.Certificates)
                //    },
                //    {
                //       name: this.traineePerformance.list.map(x => x.Name),
                //       y: this.traineePerformance.list.map(x=>x.Certificates)
                //    },
                //    {
                //       name: this.traineePerformance.list.map(x => x.Name),
                //       y: this.traineePerformance.list.map(x=>x.Certificates)
                //    },
                //    {
                //       name: this.traineePerformance.list.map(x => x.Name),
                //       y: this.traineePerformance.list.map(x=>x.Certificates)
                //    },
                //    {
                //       name: this.traineePerformance.list.map(x => x.Name),
                //       y: this.traineePerformance.list.map(x=>x.Certificates)
                //    },

                //    // {
                //    //    name: 'Mr John Doe',
                //    //    y: 10,
                //    //    sliced: true,
                //    //    selected: true
                //    // },
                //    // ['Mr Saiful Alam', 10],

                // ]
              },
            ],
          };
        },
        (err) => {
          this.toastr.warning(err.Messaage || err, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
          });
        }
      );
  }

  getBelatedTraineeList() {
    this._service
      .get("dashboard/get-belated-trainee-list", this.page)
      .subscribe(
        (res) => {
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.belatedTraineeList.list = res.Data.Records;
          this.belatedTraineeList.total = res.Data.Total;
        },
        (err) => {
          this.toastr.warning(err.Messaage || err, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
          });
        }
      );
  }

  getCertificateList() {
    const obj = {
      size: 5,
      startDate:
        this.filterForm.value.isForCertificate != null
          ? this.filterForm.value.dates.length === 2
            ? moment(this.filterForm.value.dates[0]).toISOString()
            : null
          : null,
      endDate:
        this.filterForm.value.isForCertificate != null
          ? this.filterForm.value.dates.length === 2
            ? moment(this.filterForm.value.dates[1]).toISOString()
            : null
          : null,
      status: this.filterForm.value.statusId,
      pageNumber: 6,
    };
    this.certificateList.list = [];
    let titles = [];
    this._service
      .get("dashboard/get-month-wise-certificate-gains/", obj)
      .subscribe(
        (res) => {
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          res.Data.Records.forEach((element) => {
            this.certificateList.list.push({
              name: element.Month,
              data: element.certificates,
            });
          });
          res.Data.Course.forEach((element) => {
            // titles.push(element.Name)
            titles.push({ Name: element.Name, ShortName: element.ShortName });
          });
          this.certificateList.total = res.Total;
          // console.log(moment(this.filterForm.value.dates[0]).format('DD-MMM-YYYY'))
          this.certificateChartOptions = {
            chart: {
              type: "bar",
            },
            title: {
              text:
                this.filterForm.value.dates.length !== 2
                  ? "Obtained Certificates In Last 6 Months "
                  : "Obtained Certificates from " +
                    moment(this.filterForm.value.dates[0]).format(
                      "DD-MMM-YYYY"
                    ) +
                    " - " +
                    moment(this.filterForm.value.dates[1]).format(
                      "DD-MMM-YYYY"
                    ),
            },
            xAxis: {
              categories: titles.map((x) => ({
                shortTitle: x.ShortName != null ? x.ShortName : x.Name,
                title: x.Name,
              })),
              labels: {
                formatter: function () {
                  // Display short name on x-axis labels
                  return this.value.shortTitle;
                },
              },
              crosshair: true,
            },
            yAxis: {
              min: 0,
              title: {
                text: "Number of certificates ",
                align: "middle",
              },
            },
            credits: {
              enabled: false,
            },
            legend: {
              reversed: true,
            },
            plotOptions: {
              series: {
                stacking: "normal",
              },
            },
            tooltip: {
              formatter: function () {
                // return this.points.reduce(function (s, point) {
                //    return s + '<br/><b style="color: ' + point.series.color + '">' + point.series.name + ': </b>' + point.y
                // }, '<b>' + this.x.title + '</b>');

                return this.points.reduce(function (s, point) {
                  return (
                    s +
                    '<br/><b class="point-series" data-color="' +
                    point.series.color +
                    '">' +
                    point.series.name +
                    ": </b>" +
                    point.y
                  );
                }, "<b>" + this.x.title + "</b>");
              },
              shared: true,
              useHTML: true,
            },
            series: this.certificateList.list,
          };
        },
        (err) => {
          this.toastr.warning(err.Messaage || err, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
          });
        }
      );
  }

  getEnrollmentList() {
    const obj = {
      size: 5,
      startDate:
        this.filterForm.value.dates.length === 2
          ? moment(this.filterForm.value.dates[0]).toISOString()
          : null,
      endDate:
        this.filterForm.value.dates.length === 2
          ? moment(this.filterForm.value.dates[1]).toISOString()
          : null,
      status: this.filterForm.value.statusId,
      pageNumber: 6,
    };
    this.enrollmentList.list = [];
    let titles = [];
    this._service.get("dashboard/get-month-wise-enrollment/", obj).subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        res.Data.Records.forEach((element) => {
          this.enrollmentList.list.push({
            month: element.Month,
            total: element.Total,
            new: element.New,
            progress: element.Progress,
          });
        });
        // res.Data.Course.forEach(element => {
        //    titles.push(element.month)
        // });
        this.enrollmentList.total = res.Total;
        // this.enrollmentList.list = [];
        this.enrollChartOptions = {
          chart: {
            type: "spline",
          },
          title: {
            text: "Enrollment Progress",
          },
          xAxis: {
            categories: res.Data.Records.map((x) => x.Month),
            crosshair: true,
          },
          yAxis: {
            title: {
              text: "Count",
            },
            labels: {
              formatter: function () {
                return Highcharts.numberFormat(this.value, 0, "", ",");
              },
            },
          },
          tooltip: {
            crosshairs: true,
            shared: true,
            formatter: function () {
              return (
                this.x +
                "</br> Total: " +
                Highcharts.numberFormat(this.points[0].y, 0, "", ",") +
                "</br> New: " +
                (this.points.length > 1
                  ? Highcharts.numberFormat(this.points[1].y, 0, "", ",")
                  : 0) +
                "</br> Progress: " +
                (this.points.length > 2
                  ? Highcharts.numberFormat(this.points[2].y, 0, "", ",")
                  : 0)
              );
            },
            // pointFormat:'{point.y:,.0f}'
          },
          plotOptions: {
            spline: {
              marker: {
                enabled: true,
                radius: 4,
                lineColor: "#1111",
                lineWidth: 1,
              },
            },
          },

          // series: this.enrollmentList.list,
          series: [
            {
              name: "All",
              color: "#FFA500",
              data: this.enrollmentList.list.map(function (item) {
                return { y: item.total };
              }),
              pointPadding: 0.3,
              connectNulls: true,
            },
            {
              name: "New",
              color: "#0000FF",
              data: this.enrollmentList.list.map(function (item) {
                return { y: item.new };
              }),
              pointPadding: 0.4,
              connectNulls: true,
            },
            {
              name: "Progress",
              color: "#808080",
              data: this.enrollmentList.list.map(function (item) {
                return { y: item.progress };
              }),
              pointPadding: 0.4,
              connectNulls: true,
            },
          ],
        };
      },
      (err) => {
        this.toastr.warning(err.Messaage || err, "Warning!", {
          closeButton: true,
          disableTimeOut: false,
        });
      }
    );
  }
  onChangeFilter() {
    if (this.filterForm.value.isForCertificate) this.getCertificateList();
    if (this.filterForm.value.isForEnrolledCourse) this.getCourseProgressList();
    if (this.filterForm.value.isForEnrolledProgress) this.getEnrollmentList();
  }

  getCourseProgressList() {
    const obj = {
      limit: this.limit,
      startDate:
        this.filterForm.value.isForEnrolledCourse != null
          ? this.filterForm.value.dates.length === 2
            ? moment(this.filterForm.value.dates[0]).toISOString()
            : null
          : null,
      endDate:
        this.filterForm.value.isForEnrolledCourse != null
          ? this.filterForm.value.dates.length === 2
            ? moment(this.filterForm.value.dates[1]).toISOString()
            : null
          : null,
      status: this.filterForm.value.statusId,
    };
    this._service.get("dashboard/get-top-enrollment-courses/", obj).subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.courseChartOptions = {
          chart: {
            type: "column",
          },
          title: {
            text: "Top " + this.limit + " Enrolled Courses",
          },
          xAxis: {
            categories: res.Data.map((x) => ({
              shortTitle:
                x.CourseShortTitle != null ? x.CourseShortTitle : x.CourseTitle,
              title: x.CourseTitle,
            })),
            labels: {
              formatter: function () {
                // Display short name on x-axis labels
                return this.value.shortTitle;
              },
            },
            // categories: res.Data.map(x =>x.CourseShortTitle),
            title: { text: "Courses" },
          },
          yAxis: [
            {
              min: 0,
              title: {
                text: "Enrollments",
              },
            },
          ],
          legend: {
            shadow: false,
          },
          credits: {
            enabled: false,
          },

          tooltip: {
            // headerFormat: '<strong style="font-size:10px">{point.key}</strong><table>',
            // pointFormat: '<tr><th style="color:{series.color};padding:0">{series.name}: </th>' +
            //    '<td style="padding:0"><b>{point.y} </b></td></tr>' +
            //    '<tr><th style="padding:0">Avg. Progress: </th>' +
            //    '<td style="padding:0"><b>{point.avg} </b></td></tr>',
            // footerFormat: '</table>',
            formatter: function () {
              return this.points.reduce(function (s, point) {
                return (
                  s +
                  '<br/><b style="color: ' +
                  point.series.color +
                  '">' +
                  point.series.name +
                  ": </b>" +
                  point.y
                );
              }, "<b>" + this.x.title + "</b>");
            },
            shared: true,
            useHTML: true,
          },
          plotOptions: {
            column: {
              grouping: false,
              shadow: false,
              borderWidth: 0,
            },
          },
          series: [
            {
              name: "Enrollments",
              color: "#056cb5",
              // data: res.Data.map(function (item) { return { y: item.NoOfEnrollments, avg: item.AvgProgress } }),
              data: res.Data.map(function (item) {
                return {
                  y: item.NoOfEnrollments,
                  avg: (item.Progress / item.CountMutiPercant) * 100,
                };
              }),
              pointPadding: 0.3,
            },
            {
              name: "Completed",
              color: "#04245c",
              data: res.Data.map(function (item) {
                return {
                  y: item.NoOfTraineesCompleted,
                  avg: (item.Progress / item.CountMutiPercant) * 100,
                };
              }),
              pointPadding: 0.4,
            },
          ],
        };
      },
      (err) => {
        this.toastr.warning(err.Messaage || err, "Warning!", {
          closeButton: true,
          disableTimeOut: false,
        });
      }
    );
  }
  // getDashBoardByFilter(){
  //    const obj = {
  //       name: this.filterForm.value.name ? this.filterForm.value.name.trim() : this.filterForm.value.name,
  //       categoryId: this.filterForm.value.categoryId,
  //       size: this.page.size,
  //       pageNumber: this.page.pageNumber,
  //     };

  //    this._service.get('dashboard/get-count-for-admin',obj).subscribe(res => {
  //       if (res.Status === ResponseStatus.Warning) {
  //          this.toastr.warning(res.Message, 'Warning!', { timeOut: 2000 });
  //          return;
  //       }
  //       else if (res.Status === ResponseStatus.Error) {
  //          this.toastr.error(res.Message, 'Error!', { closeButton: true, disableTimeOut: false, enableHtml: true });
  //          return;
  //       }

  //       this.counter = res.Data
  //    }, err => {
  //       this.toastr.warning(err.Messaage || err, 'Warning!', { closeButton: true, disableTimeOut: false });
  //    }
  //    );
  // }
}
