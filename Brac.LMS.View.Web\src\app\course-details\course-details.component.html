<block-ui>
  <!-- Page content-->
  <div class="container-fluid position-relative zindex-5 pb-0 mb-md-1 p-3 margin-top-neg-15">
    <div class="d-flex flex-column h-100 bg-tranparent-black-glass rounded-1 shadow-lg">
      <div class="pt-2 p-md-3">
        <div class="col-12 d-flex justify-content-between">
          <p class="h3 text-break mb-0" *ngIf="courseData">
            {{ courseData.Title }} <br />
            <span class="fw-bold text-uppercase fs-6">Course</span>
          </p>
          <a class="btn btn-link text-decoration-none fs-4 fw-bold btn-sm d-flex align-items-center border-start"
            (click)="backClicked()"><i class="fs-4 ai-arrow-left fs-base me-2"></i>Go Back</a>
        </div>
        <hr class="mt-1 mb-4" />
        <!-- Content-->

        <div class="row" *ngIf="selectedContent">
          <div class="col-md-12">

          </div>
        </div>

        <div class="row">
          <div class="col-lg-8 border-end" *ngIf="courseData">
            <div class="d-flex" *ngIf="selectedContent">
              <h2 class="h4 nav-heading mb-2 flex-grow-1">{{ selectedContent.Title }}</h2>
              <div class="btn-group btn-group-mini mb-2 d-flex align-items-end" role="group">
                <button type="button" class="btn btn-outline-primary rounded-0" [disabled]="selectedIndex === 0"
                  (click)="onSelectContent(selectedIndex-1)">
                  <i class="fa-solid fa-arrow-left"></i> Previous
                </button>
                <button type="button" class="btn btn-outline-primary rounded-0"
                  [disabled]="selectedIndex === courseData.Contents.length-1 || courseData.Contents[selectedIndex+1].Restricted"
                  (click)="onSelectContent(selectedIndex+1)">
                  Next <i class="fa-solid fa-arrow-right"></i>
                </button>
              </div>
            </div>

            <ng-container *ngIf="selectedContent; else elseNotSelectedContent">
              <video class="w-100" (loadedmetadata)="onLoadedMetadata($event)" (timeupdate)="onTimeUpdate($event)"
                (seeking)="onSeeking($event)" (pause)="onVideoPause($event)" controlslist="nodownload" controls
                *ngIf="vidObj && !vidObj.YoutubeID">
                <source src="{{ mediaBaseUrl }}{{ vidObj.FilePath }}" type="video/mp4" />
              </video>

              <iframe #youtubeFrame *ngIf="vidObj && vidObj.YoutubeID" class="w-100"
                [style.height.px]="youtubeFrame.offsetWidth * 0.564"
                [src]="'https://www.youtube.com/embed/' + vidObj.YoutubeID + '?rel=0&modestbranding=1&iv_load_policy=3&cc_load_policy=0&autoplay=1&showsearch=0&showinfo=0&autohide=2' | safe: 'resourceUrl'"
                frameborder="0" allow="accelerometer; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen></iframe>

              <div *ngIf="docObj">
                <div class="d-flex justify-content-center mb-2" *ngIf="timer && timer.timeLeft > 0">
                  <div class="hours parent_time">
                    <div class="hours_0 child_time child_time0">{{ timer.hourString().split('')[0] }}</div>
                    <div class="hours_1 child_time child_time1">{{ timer.hourString().split('')[1] }}</div>
                  </div>
                  <div class="minutes parent_time">
                    <div class="minutes_0 child_time child_time0">{{ timer.minuteString().split('')[0] }}</div>
                    <div class="minutes_1 child_time child_time1">{{ timer.minuteString().split('')[1] }}</div>
                  </div>
                  <div class="seconds parent_time">
                    <div class="seconds_0 child_time child_time0">{{ timer.secondString().split('')[0] }}</div>
                    <div class="seconds_1 child_time child_time1">{{ timer.secondString().split('')[1] }}</div>
                  </div>
                </div>
                <div class="w-100 cdc-style-1" *ngIf="selectedContent.Type == 'Document' && docObj.PDF">
                  <ngx-extended-pdf-viewer [(src)]="pdfSrc" [zoom]="'page-width'" [showPrintButton]="false"
                    [showRotateButton]="false" [showOpenFileButton]="false"
                    [showDownloadButton]="selectedContent.CanDownload" [showBookmarkButton]="true"
                    [showSecondaryToolbarButton]="false" [useBrowserLocale]="true">
                  </ngx-extended-pdf-viewer>
                </div>

                <div class="card py-3 mb-2 cdc-style-2" *ngIf="!docObj.PDF">
                  <div class="card-body">
                    <p class="font-size-20">
                      This document can't preview here. You may download the
                      document. Please click below button to download the
                      document.
                    </p>
                    <button type="button" class="btn btn-primary btn-sm mt-2" (click)="downloadDoc()">
                      <i class="fa fa-download"> </i> Download
                    </button>
                  </div>

                </div>
              </div>
            </ng-container>

            <ng-template #elseNotSelectedContent>
              <div class="card-img-top card-img-bottom w-100">
                <img src="{{ mediaBaseUrl }}{{ courseData.ImagePath }}" />
              </div>
            </ng-template>

            <div class="mt-3">
              <tabset [justified]="true" #courseTabs>
                <tab (selectTab)="changeTab('Overview', $event)">
                  <ng-template tabHeading> Overview </ng-template>

                  <div class="row">
                    <div class="col-md-10">
                      <div class="d-sm-flex align-items-center justify-content-between pb-2 text-center text-sm-start">
                        <h1 class="h3 text-wrap">
                          {{ courseData.Title }}
                        </h1>
                      </div>
                      <div class="row">
                        <div class="col-8 pe-0">
                          <rating class="fs-3 text-blue" [(ngModel)]="courseData.Rating"
                            [titles]="['Not satisfied at all','It was okay','Satisfactory','Very Good','Great']"
                            [readonly]="true"></rating>
                          <span class="h4 pt-1 text-blue me-2">
                            {{courseData.Rating}} <span>
                              ({{
                              courseData.NoOfRating
                              }})
                            </span>


                            <!-- <div role="button"
                          class="mt--2 cursor d-inline-block fs-xs fw-bold bg-faded-blue text-primary px-2 py-1 rounded-1 float-end ng-star-inserted">
                          <i class="fab fa-quora"></i> FaceBack </div> -->

                          </span>
                          <button *ngIf="courseData.CertificateAchieved" type="button"
                            (click)="openFeedBackModal(templateFeedBackModal)" [disabled]="courseData.FeedbackGiven"
                            class="btn btn-mini btn-primary">
                            <i class="fas fa-tasks"></i>
                            {{courseData.FeedbackGiven? 'Feedback Given' : 'Give Your Feedback'}}
                          </button>

                          <!-- <span>
                          <div role="button"
                            class="mt--2 cursor d-inline-block fs-xs fw-bold bg-faded-blue text-primary px-2 py-1 rounded-1 float-end ng-star-inserted">
                            <i class="fa-duotone fa-hexagon-check"></i> FaceBack </div>
                        </span> -->




                        </div>
                        <div class="col-4">
                          <span class="text-blue">
                            <span><small>Total Lectures:</small> {{courseData.TotalLecture}}</span><br />
                            <span><small>Total Durations:</small> {{courseData.TotalDuration}}</span>
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-2">
                      <div class="d-inline-block fw-normal px-2 py-1 rounded-1 float-end">
                        <i *ngIf="courseData.Bookmarked" (click)="createBookmarkOrUnbookmark()"
                          class="fa fa-bookmark text-gold fs-3 div-pointer"></i>
                        <i *ngIf="!courseData.Bookmarked" (click)="createBookmarkOrUnbookmark()"
                          class="fa-regular fa-bookmark text-muted fs-3 div-pointer"></i>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-12 py-3">
                      <p class="text-justify" [innerHTML]="courseData.Description| safe: 'html'">
                        <!-- {{ courseData.Description }} -->
                      </p>
                    </div>
                  </div>
                </tab>
                <tab (selectTab)="changeTab('Discussion', $event)">
                  <ng-template tabHeading> Discussion </ng-template>

                  <div class="row">
                    <div class="col-md-8">
                      <div class="d-sm-flex align-items-center justify-content-between pb-2 text-center text-sm-start">
                        <h1 class="h3 mb-3 text-nowrap">
                          {{ courseDiscussionList.length }} Comments
                        </h1>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="d-flex align-items-center justify-content-center">
                        <a #commentButton class="btn btn-primary d-block w-100"
                          (click)="openNewFeedbackForm(commentForm)">Write A Comment</a>
                      </div>
                    </div>
                    <ng-template #commentForm>
                      <div class="col-md-12 py-3 pe-md-3">
                        <div>
                          <form [formGroup]="discussionForm" autocomplete="off"
                            class="needs-validation bg-light rounded-3 shadow p-4" novalidate>
                            <input type="hidden" formControlName="CourseId" [value]="courseId" />
                            <input type="hidden" formControlName="Id" />
                            <!-- <div class="row pull-right" >
    <div>
      <button  #commentButton type="Button"  href="#comment-form" class="btn btn-mini btn-danger"  data-bs-toggle="collapse" area-label="Close">&times;</button>
    </div>
  </div> -->
                            <div class="row">
                              <div class="col-md-6 mb-3">
                                <label class="form-label" for="com-name">
                                  Comment On<sup class="text-danger ms-1">*</sup>
                                </label>
                                <ng-select formControlName="DiscussionType" (change)="onChangeCourse($event)"
                                  placeholder="Select a type" bindLabel="Name" bindValue="Id" [clearable]="true"
                                  [clearOnBackspace]="false" [items]="DiscussionTypeList">
                                </ng-select>
                                <div *ngIf="
                                      submitted && f['DiscussionType'].errors
                                    " class="error-text">
                                  <span *ngIf="
                                        f['DiscussionType'].errors['required']
                                      " class="text-danger">
                                    Type is required
                                  </span>
                                </div>
                              </div>

                              <div class="col-md-6 mb-3" *ngIf="
                                    f['CourseId'] &&
                                    f['DiscussionType'].value == 0
                                  ">
                                <label class="form-label" for="com-name">Video</label>
                                <ng-select formControlName="MaterialId" [clearable]="false" [clearOnBackspace]="false"
                                  [items]="MaterialList" bindLabel="Title" bindValue="Id" placeholder="Select">
                                </ng-select>
                                <div *ngIf="submitted && f['MaterialId'].errors" class="error-text">
                                  <span *ngIf="f['MaterialId'].errors['required']" class="text-danger">
                                    Video is required
                                  </span>
                                </div>
                              </div>
                              <div class="col-md-6 mb-3" *ngIf="
                                    f['CourseId'] &&
                                    f['DiscussionType'].value == 1
                                  ">
                                <label class="form-label"> Document </label>
                                <ng-select formControlName="MaterialId" [clearable]="false" [clearOnBackspace]="false"
                                  [items]="MaterialList" bindLabel="Title" bindValue="Id" placeholder="Select">
                                </ng-select>
                                <div *ngIf="submitted && f['MaterialId'].errors" class="error-text">
                                  <span *ngIf="f['MaterialId'].errors['required']" class="text-danger">
                                    Document is required
                                  </span>
                                </div>
                              </div>

                              <div class="mb-3 col-md-6" *ngIf="
                                    f['CourseId'] &&
                                    f['DiscussionType'].value == 2
                                  ">
                                <label class="form-label required">
                                  Mock Test Test
                                </label>
                                <ng-select formControlName="MockTestId" [clearable]="false" [clearOnBackspace]="false"
                                  [items]="MaterialList" bindLabel="Title" bindValue="Id" placeholder="Select">
                                </ng-select>
                                <div *ngIf="submitted && f['MockTestId'].errors" class="error-text">
                                  <span *ngIf="f['MockTestId'].errors['required']" class="text-danger">
                                    Mock test is required
                                  </span>
                                </div>
                              </div>

                              <div class="mb-3 col-md-6" *ngIf="
                                    f['CourseId'] &&
                                    f['DiscussionType'].value == 3
                                  ">
                                <label class="form-label required">
                                  Certification Test
                                </label>
                                <ng-select formControlName="ExamId" [clearable]="false" [clearOnBackspace]="false"
                                  [items]="MaterialList" bindLabel="Title" bindValue="Id" placeholder="Select">
                                </ng-select>
                                <div *ngIf="submitted && f['ExamId'].errors" class="error-text">
                                  <span *ngIf="f['ExamId'].errors['required']" class="text-danger">
                                    Certificate test is required
                                  </span>
                                </div>
                              </div>
                            </div>

                            <div class="mb-3">
                              <label class="form-label" for="com-text">
                                Your Comment
                                <sup class="text-danger ms-1">*</sup>
                              </label>
                              <textarea formControlName="Comment" class="form-control" id="com-text" rows="4"
                                placeholder="Write your comment  here" required></textarea>

                              <div *ngIf="submitted && f['Comment'].errors" class="error-text">
                                <span *ngIf="f['Comment'].errors['required']" class="text-danger">
                                  Comment is required
                                </span>
                              </div>
                            </div>
                            <div class="">
                              <div class="d-flex justify-content-end">
                                <button #commentButton type="Button" (click)="closeFeedbackForm()"
                                  class="btn  btn-danger mx-2" area-label="Close">
                                  Close
                                </button>

                                <button class="btn btn-primary" (click)="onFormSubmit()">
                                  Post
                                </button>
                              </div>
                            </div>
                          </form>
                        </div>
                      </div>
                    </ng-template>
                    <div class="row">
                      <div class="col-md-12" *ngFor="let item of courseDiscussionList">
                        <div class="comment mb-3 pb-1 cdc-style-3">
                          <div class="d-flex align-items-center mb-3">
                            <img *ngIf="item.ImagePath" class="rounded-circle"
                              src="{{ mediaBaseUrl }}{{ item.ImagePath }}" alt="{{ item.Commentator }}" width="80" />
                            <img *ngIf="!item.ImagePath" class="rounded-circle" src="assets/img/demo/profile.jpg"
                              alt="{{ item.Commentator }}" width="80" />
                            <div class="ps-2 ms-1">
                              <h4 class="fs-4 mb-0">
                                {{ item.Commentator }}
                              </h4>
                              <span class="fs-xs text-muted">
                                <div *ngIf="item.DisscussionType == 'Video'" class="meta-link fs-xs">
                                  <i class="fa fa-play me-1 align-vertical"></i>
                                  {{ item.Material }}
                                </div>
                                <div *ngIf="item.DisscussionType == 'MockTest'" class="meta-link fs-xs">
                                  <i class="fa fa-clock me-1 align-vertical"></i>
                                  {{ item.MockTest }}
                                </div>
                                <div *ngIf="item.DisscussionType == 'Document'" class="meta-link fs-xs">
                                  <i class="fa fa-file me-1 align-vertical"></i>
                                  {{ item.Material }}
                                </div>
                                <div *ngIf="
                                        item.DisscussionType ==
                                        'CertificationTest'
                                      " class="meta-link fs-xs">
                                  <i class="ai-award me-1 align-vertical"></i>
                                  {{ item.Exam }}
                                </div>
                                <div *ngIf="
                                        item.DisscussionType ==
                                        'Course'
                                      " class="meta-link fs-xs">
                                  <i class="ai-award me-1 align-vertical"></i>
                                  {{ courseData.Title }}
                                </div>
                              </span>
                              <br />
                              <span class="fs-xs text-muted">
                                <div class="meta-link fs-xs">
                                  <i class="ai-calendar me-1 align-vertical"></i>
                                  {{ item.CommentTime | amTimeAgo }}
                                </div>
                              </span>
                            </div>
                          </div>
                          <p class="comment-text">{{ item.Comment }}</p>
                          <span *ngIf="item.IsSelfComment" class="cdc-style-4">
                            <button class="btn btn-sm btn-outline-primary"
                              (click)="editDiscussion(commentForm,item.Id)"><i class="fas fa-edit"></i></button>
                          </span>
                        </div>
                      </div>

                      <!-- <div class="col-md-12">
                      <div class="comment mb-3 pb-1">
                        <div class="d-flex align-items-center"><img class="rounded-circle"
                          src="assets/img/blog/comments/03.jpg" alt="Daniel Adams" width="80">
                        <div class="ps-2 ms-1">
                          <h4 class="fs-4 mb-0">Daniel Adams</h4>
                          <span class="fs-xs text-muted">2 days ago</span>
                        </div>
                      </div>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt
                          ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation
                          ullamco laboris nisi ut aliquip ex ea commodo consequat cumque nihil impedit quo minus.
                          Duis aute irure dolor in reprehenderit in voluptate.</p>

                      </div>
                    </div> -->
                    </div>
                  </div>
                </tab>
                <tab (selectTab)="changeTab('FAQ', $event)">
                  <ng-template tabHeading> FAQ </ng-template>

                  <div class="row">
                    <div class="row justify-content-center mt-3">
                      <div class="col-lg-12">
                        <accordion [isAnimated]="true" [closeOthers]="true">
                          <accordion-group [isOpen]="i == 0" *ngFor="
                                  let item of courseFAQList;
                                  let i = index
                                ">
                            <button
                              class="btn fs-5 fw-bold text-primary btn-block justify-content-between d-flex w-100 shadow-none"
                              accordion-heading type="button">
                              <div class="pull-left float-left">
                                Question: {{ item.Question }}
                              </div>
                            </button>
                            <div class="text-justify">
                              {{ item.Answer }}
                            </div>
                          </accordion-group>
                        </accordion>
                      </div>
                    </div>
                  </div>
                </tab>
              </tabset>
            </div>
          </div>
          <div class="col-lg-4" *ngIf="courseData" id="scrollable-content">
            <h1 class="h3 mb-2 text-nowrap">Contents</h1>

            <div class="border-bottom" (click)="onSelectContent(i)"
              *ngFor="let item of courseData.Contents; let i = index">
              <div class="row py-3 content-div" [ngClass]="{ 'content-div-active': item.Selected }">
                <div class="col-10 div-pointer">
                  <div class="left-icon-div me-2">
                    <span *ngIf="item.Type == 'Video'" class="card-floating-icon-custom">
                      <i class="fa fa-play"></i>
                    </span>
                    <span *ngIf="item.Type == 'MockTest'" class="card-floating-icon-custom">
                      <i class="fa fa-clock"></i>
                    </span>
                    <span *ngIf="item.Type == 'Document'" class="card-floating-icon-custom">
                      <i class="fa fa-file"></i>
                    </span>
                    <span *ngIf="item.Type == 'CertificateTest'" class="card-floating-icon-custom">
                      <i class="ai-award"></i>
                    </span>
                  </div>
                  <div class="fs-sm ps-sm-3">
                    <div class="d-flex flex-column text-heading">
                      <div class="fw-bold fs-6">
                        {{ item.Title }}
                        <p *ngIf="item.Type == 'Video'" class="text-primary fw-bold fs-xs pt-2 mb-1">
                          {{
                          item.VideoDurationSecond * 1000 | date: "mm:ss"
                          }}
                        </p>
                        <p *ngIf="item.Type == 'MockTest'" class="text-primary fw-bold fs-xs pt-2 mb-1">
                          Mock Test
                        </p>
                        <p *ngIf="item.Type == 'Document'" class="text-primary fw-bold fs-xs pt-2 mb-1">
                          Document
                        </p>
                        <p *ngIf="item.Type == 'CertificateTest'" class="text-primary fw-bold fs-lg pt-2 mb-1">
                          Final Certification Exam
                        </p>
                        <div role="button" *ngIf="item.Resources.length > 0" (click)="
                                openModalResource(templateResourceModal, item)"
                          class="mt--2 cursor d-inline-block fs-xs fw-bold bg-faded-blue text-primary px-2 py-1 rounded-1 float-end">
                          <i class="fa fa-folder"></i> Resources
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="col-2 align-self-center">
                  <i *ngIf="item.Restricted" class="ai-lock fw-bold fs-3"></i>
                  <i *ngIf="item.Studied" class="ai-check fw-bold fs-3 text-primary"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #templateResourceModal>
    <div class="modal-header">
      <h4 class="modal-title float-start">
        {{ resourceParent.Title }} || Resources
      </h4>
      <button type="button " class="close float-end" aria-label="Close " (click)="modalHideResource()">
        <i class="fa fa-close"></i>
      </button>
    </div>
    <div class="modal-body">
      <div class="col-sm-12">
        <div class="card">
          <div class="card-body">
            <table class="table">
              <thead>
                <tr>
                  <th scope="col">#</th>
                  <th scope="col">Title</th>
                  <th scope="col">File Size</th>
                  <th scope="col">Download</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of resourceParent.Resources; let i = index">
                  <th scope="row">{{ i + 1 }}</th>
                  <td>{{ item.Title }}</td>
                  <td>{{ formatFileSize(item.FileSizeKb) }}</td>
                  <td>
                    <div class="dropdown">
                      <a href="{{ mediaBaseUrl }}{{ item.FilePath }}" class="theme-btn-custom btn-xs theme-btn-light"
                        href="javascript:;" rel="noopener">
                        <i class="fa fa-download mr-1"></i> Download
                      </a>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <div class="pr-4">
        <button class="btn btn-outline-danger mr-2" (click)="modalHideResource()">
          <i class="feather icon-close"></i> Close
        </button>
      </div>
    </div>
  </ng-template>


  <ng-template #templateFeedBackModal>
    <div class="modal-header">
      <h4 class="modal-title float-start">
        Feedbacks on {{ courseData.Title }}
      </h4>
      <button type="button " class="close btn btn-mini btn-outline-danger float-end" aria-label="Close "
        (click)="modalHideFeedBack()">
        <i class="fa fa-close"></i>
      </button>
    </div>
    <div class="modal-body">
      <form class="col-12" [formGroup]="ratingForm" autocomplete="off">
        <div class="row g-3 align-items-center">
          <div class="col-auto">
            <label for="rating" class="col-form-label">Rating on course</label>
          </div>
          <div class="col-auto">
            <rating class="fs-3 text-gold" formControlName="rating"
              [titles]="['Not satisfied at all','It was okay','Satisfactory','Very Good','Great']" [readonly]="false">
            </rating>
          </div>
        </div>
        <div class="row g-3 align-items-center mb-3">
          <div class="col-auto">
            <label for="comment" class="col-form-label">Comment</label>
          </div>
          <div class="col-auto w-100 mt-0">
            <textarea rows="3" class="form-control" formControlName="comment"></textarea>
          </div>
        </div>

        <div class="card card-active mb-3" *ngFor="let item of feedBackQuestionList">
          <div class="card-header py-2 bg-primary text-dark bg-opacity-25">
            <h5 class="card-title mb-0">{{item.Group}} - Feedbacks</h5>
          </div>
          <div class="card-body py-2">
            <div class="row mb-3" *ngFor="let question of item.Questions; let i = index">
              <div class="col-lg-7 col-md-6 col-12">
                <b>{{i + 1}}.</b> {{question.Question}}
              </div>
              <div class="col-lg-5 col-md-4 col-12">
                <div [ngSwitch]="question.QuestionType">
                  <div *ngSwitchCase="'Radio'">
                    <div class="input-group">
                      <div class="form-check form-check-inline" *ngFor="let radio of question.Options; let r = index">
                        <input class="form-check-input" type="radio" id="q-ra-{{question.Id}}-{{r}}"
                          name="radio-{{question.Id}}" [value]="radio" [(ngModel)]="question.Answers"
                          [ngModelOptions]="{standalone: true}">
                        <label class="form-check-label" for="q-ra-{{question.Id}}-{{r}}">{{radio}}</label>
                      </div>
                    </div>
                  </div>

                  <div *ngSwitchCase="'Checkbox'">
                    <div class="form-check mb-1" *ngFor="let ckbox of question.Options; let c = index">
                      <input class="form-check-input" type="checkbox" id="q-ck-{{question.Id}}-{{c}}"
                        [checked]="question.Answers.indexOf(ckbox) !== -1"
                        (change)="onChangeCheckBox($event, question, ckbox)">
                      <label class="form-check-label" for="q-ck-{{question.Id}}-{{c}}">{{ckbox}}</label>
                    </div>
                  </div>

                  <div *ngSwitchCase="'Dropdown'">
                    <ng-select class="rounded-2 w-100" [clearable]="false" [(ngModel)]="question.Answers"
                      [ngModelOptions]="{standalone: true}" [clearOnBackspace]="false" [items]="question.Options"
                      placeholder="Select">
                    </ng-select>
                  </div>

                  <div *ngSwitchCase="'Rating'">
                    <div class="input-group">
                      <rating class="fs-3 text-gold" [(ngModel)]="question.Answers"
                        [ngModelOptions]="{standalone: true}"
                        [titles]="['Not satisfied at all','It was okay','Satisfactory','Very Good','Great']"
                        [readonly]="false">
                      </rating>
                    </div>
                  </div>

                  <div *ngSwitchCase="'Textbox'">
                    <div class="input-group">
                      <textarea rows="3" class="form-control px-2" placeholder="Write anser here"
                        [(ngModel)]="question.Answers" [ngModelOptions]="{standalone: true}"></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button class="btn btn-outline-danger me-2" (click)="modalHideFeedBack()">
        <i class="fa fa-close"></i> Close
      </button>
      <button class="btn btn-primary" (click)="onSubmitFeedback()">
        <i class="fa fa-save"></i> Submit
      </button>
    </div>
  </ng-template>


</block-ui>