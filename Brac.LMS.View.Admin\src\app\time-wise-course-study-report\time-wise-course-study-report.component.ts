import { Component, ViewEncapsulation, OnInit, ViewChild } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import { BlockUI, NgBlockUI } from "ng-block-ui";
import { BsDaterangepickerConfig } from "ngx-bootstrap/datepicker";
import { AuthenticationService } from "../_services/authentication.service";
import { CommonService } from "../_services/common.service";
import { environment } from "src/environments/environment";
import { ResponseStatus } from "../_models/enum";
import { Observable, Subject, concat, of } from "rxjs";
import {
  debounceTime,
  distinctUntilChanged,
  tap,
  switchMap,
  catchError,
} from "rxjs/operators";
import * as moment from "moment";
@Component({
  selector: "app-time-wise-course-study-report",
  templateUrl: "./time-wise-course-study-report.component.html",
  encapsulation: ViewEncapsulation.None,
})
export class TimeWiseCourseStudyReportComponent implements OnInit {
  @BlockUI() blockUI: NgBlockUI;

  courseList: Array<any> = [];
  traineeList: Array<any> = [];

  baseUrl = environment.baseUrl;
  filterForm: FormGroup;
  submitted = false;
  currentUser: any;
  url: any;
  trainee$: Observable<any[]>;
  traineeLoading = false;
  traineeInput$ = new Subject<string>();
  traineeDisabled: boolean = false;

  bsConfig: Partial<BsDaterangepickerConfig>;
  bsValue: Date[] = [];

  @ViewChild("pdfViewerOnDemand", { static: false }) pdfViewerOnDemand: any;
  reportFileName: string;

  constructor(
    private authService: AuthenticationService,
    public formBuilder: FormBuilder,
    private _service: CommonService,
    private toastr: ToastrService
  ) {
    this.authService.getCurrentUser().subscribe((user) => {
      this.currentUser = user;
    });
  }

  ngOnInit() {
    this.bsConfig = Object.assign(
      {},
      {
        maxDate: new Date(),
        containerClass: "theme-blue",
        rangeInputFormat: "DD MMM YYYY",
      }
    );

    this.filterForm = this.formBuilder.group({
      dates: [[]],
      // traineeId: [null,],
    });
  }

  get f() {
    return this.filterForm.controls;
  }

  showReport(reportType) {
    this.submitted = true;
    if (this.filterForm.invalid) return;

    this.blockUI.start("Generating report. Please wait...");

    const obj = {
      reportType: reportType === "WebView" ? "Pdf" : reportType,
      startDate:
        this.filterForm.value.dates.length === 2
          ? moment(this.filterForm.value.dates[0]).format("DD-MMM-YYYY")
          : null,
      endDate:
        this.filterForm.value.dates.length === 2
          ? moment(this.filterForm.value.dates[1]).format("DD-MMM-YYYY")
          : null,
    };

    this._service
      .downloadFile("course/get-time-wise-course-study-report", obj)
      .subscribe(
        (res) => {
          this.submitted = false;
          this.blockUI.stop();
          if (reportType === "WebView") {
            this.reportFileName = "Time_Wise_Course_Study_Report.pdf";
            this.url = res;
            return;
          }

          const url = window.URL.createObjectURL(res);
          var link = document.createElement("a");
          link.href = url;
          link.download =
            "Time_Wise_Course_Study_Report_" +
            (reportType === "Excel" ? "xlsx" : "pdf");
          link.click();
        },
        (err) => {
          this.toastr.warning(err.message || err, "Warning!");
          this.blockUI.stop();
        }
      );
  }
}
