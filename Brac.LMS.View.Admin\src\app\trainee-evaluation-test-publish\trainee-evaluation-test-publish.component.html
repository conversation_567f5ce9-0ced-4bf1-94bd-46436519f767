<block-ui>
  <div class="col-12">
    <div class="card card-border-primary">
      <div class="card-header">
        <h5 class="card-title">
          Trainee Evaluation Test Publish</h5>
        <button class="btn btn-info btn-mini float-end" [routerLink]="['/trainee-evaluation-test-list']">
          <i class="feather icon-arrow-left"></i> Go Back
        </button>
      </div>
      <div class="card-block ">
        <div class="row ">


          <div class="col-lg-12 ">

            <form autocomplete="off">

              <div class="row">
                <div class="col-lg-12">
                  <div class="mb-3 row">


                    <label class="col-sm-1 col-form-label col-form-label-sm text-right required"> Exam
                    </label>
                    <div class="col-sm-4">
                      <ng-select #selectElement (click)="handleSelectClick(selectElement)"
                        class="form-control form-control-sm" (change)="filterList($event)" [clearable]="false"
                        [clearOnBackspace]="false" [items]="examList" bindLabel="Name" bindValue="Id"
                        placeholder="Select">
                      </ng-select>
                    </div>
                  </div>

                  <div class="card mb-0" *ngIf="rows.length > 0">
                    <div class="card-header p-2">
                      <h5 class="card-title">Select To Publish</h5>
                      <div class="custom-control custom-checkbox float-end">
                        <input name="selectAll" id="selectAll" type="checkbox" class="custom-control-input"
                          [(ngModel)]="selectAll" [ngModelOptions]="{standalone: true}"
                          (change)="onChangeSelectAll($event)">
                        <label for="selectAll" class="custom-control-label">
                          Select All
                        </label>
                      </div>
                    </div>
                    <div class="card-body p-2">
                      <div class="table-responsive">
                        <table class="table table-sm table-hover table-bordered table-striped">
                          <thead>
                            <tr>
                              <th>#</th>
                              <th>Name</th>
                              <th>Status</th>
                              <th>Marked On</th>
                              <th>Marks</th>
                              <th>Result</th>
                            </tr>
                          </thead>

                          <tbody>
                            <tr *ngFor="let item of rows  ">
                              <td>
                                <div class="custom-control custom-checkbox">
                                  <input name="{{item.Id}}" id="{{item.Id}}" type="checkbox"
                                    class="custom-control-input" [(ngModel)]="item.selected"
                                    [ngModelOptions]="{standalone: true}" value="{{item.Id}}">
                                  <label for="{{item.Id}}" class="custom-control-label">
                                  </label>
                                </div>
                              </td>
                              <td>
                                {{item.Name}}
                              </td>
                              <td>
                                {{item.Status}}
                              </td>

                              <td>
                                {{ item.MarkedOn | amDateFormat:'DD MMM YYYY, h:mm a'}}
                              </td>

                              <td>
                                {{ item.GainedMarks }} / {{ item.TotalMarks }}
                              </td>
                              <td>
                                <p *ngIf="item.Result === 'Passed'" class="badge bg-success">{{ item.Result }}</p>
                                <p *ngIf="item.Result === 'Failed'" class="badge bg-danger">{{ item.Result }}</p>
                              </td>
                            </tr>
                          </tbody>

                        </table>

                      </div>

                    </div>
                  </div>

                  <div *ngIf="selectedExam && rows.length === 0">
                    <br />
                    <h4 class="text-danger">No Item Found!!</h4>
                  </div>
                </div>
              </div>
            </form>
          </div>

        </div>
      </div>
      <div class="card-footer">
        <button *ngIf="rows.length > 0" class="btn btn-theme btn-testz" (click)="onPublish()"><i
            class="feather icon-check-circle"></i>
          Publish</button>
      </div>
      <!-- end of card-footer -->
    </div>
  </div>


</block-ui>