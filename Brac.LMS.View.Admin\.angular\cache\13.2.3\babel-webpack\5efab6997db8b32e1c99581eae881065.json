{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AdminViewRoutingModule } from './dashboard-admin-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport { HighchartsChartModule } from 'highcharts-angular';\nimport { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';\nimport * as i0 from \"@angular/core\";\nexport let AdminViewViewModule = /*#__PURE__*/(() => {\n  class AdminViewViewModule {}\n\n  AdminViewViewModule.ɵfac = function AdminViewViewModule_Factory(t) {\n    return new (t || AdminViewViewModule)();\n  };\n\n  AdminViewViewModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AdminViewViewModule\n  });\n  AdminViewViewModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, AdminViewRoutingModule, SharedModule, NgbTooltipModule, HighchartsChartModule]]\n  });\n  return AdminViewViewModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}