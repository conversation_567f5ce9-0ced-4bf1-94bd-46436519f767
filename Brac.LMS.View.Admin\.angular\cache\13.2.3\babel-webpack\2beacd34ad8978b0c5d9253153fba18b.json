{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CertificationTestEntryComponent } from './certification-test-entry.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: CertificationTestEntryComponent\n}];\nexport let CertificationTestEntryRoutingModule = /*#__PURE__*/(() => {\n  class CertificationTestEntryRoutingModule {}\n\n  CertificationTestEntryRoutingModule.ɵfac = function CertificationTestEntryRoutingModule_Factory(t) {\n    return new (t || CertificationTestEntryRoutingModule)();\n  };\n\n  CertificationTestEntryRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CertificationTestEntryRoutingModule\n  });\n  CertificationTestEntryRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return CertificationTestEntryRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}