{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { TimeWiseCourseStudyReportComponent } from './time-wise-course-study-report.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: TimeWiseCourseStudyReportComponent\n}];\nexport let TimeWiseCourseStudyReportRoutingModule = /*#__PURE__*/(() => {\n  class TimeWiseCourseStudyReportRoutingModule {}\n\n  TimeWiseCourseStudyReportRoutingModule.ɵfac = function TimeWiseCourseStudyReportRoutingModule_Factory(t) {\n    return new (t || TimeWiseCourseStudyReportRoutingModule)();\n  };\n\n  TimeWiseCourseStudyReportRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TimeWiseCourseStudyReportRoutingModule\n  });\n  TimeWiseCourseStudyReportRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return TimeWiseCourseStudyReportRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}