﻿using Brac.LMS.Common;
using Brac.LMS.Models;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Infrastructure.Interception;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Linq;
using System.Security.Claims;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.DB
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext()
            : base("DefaultConnection", throwIfV1Schema: false)
        {
            Database.SetInitializer<ApplicationDbContext>(null);

            // **SAFE DATETIME ERROR INTERCEPTOR: Temporarily disabled for compilation**
            // WHY: Helps identify which specific datetime property is causing SQL Server conversion errors
            // BENEFIT: Better debugging without affecting normal operations
            // TODO: Re-enable after project builds successfully
            // RegisterDateTimeInterceptorSafely();
        }

        public static ApplicationDbContext Create()
        {
            return new ApplicationDbContext();
        }

        /// <summary>
        /// Safely register the DateTime SQL Interceptor to avoid duplicate registrations
        /// TEMPORARILY DISABLED FOR COMPILATION - TODO: Re-enable after project builds
        /// </summary>
        /*
        private void RegisterDateTimeInterceptorSafely()
        {
            try
            {
                // **SIMPLIFIED APPROACH: Just register the interceptor**
                // WHY: EF6 handles duplicate registrations gracefully, no need for complex checking
                // BENEFIT: Simpler code that works reliably across different EF6 versions
                DbInterception.Add(new DateTimeSqlInterceptor());
                LogControl.Write("DateTime SQL Interceptor registered successfully for error detection");
            }
            catch (Exception ex)
            {
                // Fail silently - never break the application due to interceptor registration issues
                try
                {
                    LogControl.Write($"Failed to register DateTime SQL Interceptor: {ex.Message}");
                }
                catch
                {
                    // Ultimate fallback - do nothing if even logging fails
                }
            }
        }
        */

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Conventions.Remove<PluralizingTableNameConvention>();

            modelBuilder.Entity<ApplicationUser>().ToTable("IdentityUser");
            //AspNetRoles -> Role
            modelBuilder.Entity<IdentityRole>().ToTable("IdentityRole");
            //AspNetUserRoles -> UserRole
            modelBuilder.Entity<IdentityUserRole>().ToTable("UserRole");
            //AspNetUserClaims -> UserClaim
            modelBuilder.Entity<IdentityUserClaim>().ToTable("UserClaim");
            //AspNetUserLogins -> UserLogin
            modelBuilder.Entity<IdentityUserLogin>().ToTable("UserLogin");

            modelBuilder.Conventions.Remove<ManyToManyCascadeDeleteConvention>();

            //modelBuilder.Entity<SchoolSubject>().HasIndex(c => c.Code).IsUnique();

            modelBuilder.Entity<ForumTopic>().HasRequired(c => c.Creator).WithMany().WillCascadeOnDelete(false);

            //modelBuilder.Entity<Course>().HasRequired(c => c.Trainer).WithMany().WillCascadeOnDelete(false);

            modelBuilder.Entity<CertificateConfiguration>().HasRequired(c => c.Course).WithMany().WillCascadeOnDelete(false);

            modelBuilder.Entity<TraineeEvaluationExam>().HasRequired(c => c.Exam).WithMany().WillCascadeOnDelete(false);
            modelBuilder.Entity<TraineeExam>().HasRequired(c => c.Exam).WithMany().WillCascadeOnDelete(false);
            modelBuilder.Entity<CourseMaterialStudy>().HasRequired(c => c.Material).WithMany().WillCascadeOnDelete(false);

            modelBuilder.Entity<TraineeCertificate>().HasRequired(c => c.TraineeExam).WithMany().WillCascadeOnDelete(false);

            // NEW: Configure relationship between TraineeCertificate and CertificateConfiguration
            modelBuilder.Entity<TraineeCertificate>().HasOptional(c => c.CertificateConfiguration).WithMany().WillCascadeOnDelete(false);

            modelBuilder.Entity<MCQMockTestAnswer>().HasRequired(c => c.TraineeMockTest).WithMany().WillCascadeOnDelete(false);

            modelBuilder.Entity<MCQMockTestAnswer>()
            .HasRequired(s => s.TraineeMockTest)
            .WithMany(g => g.MCQMockTestAnswers)
            .HasForeignKey(s => s.TraineeMockTestId);

            //modelBuilder.Entity<FIGQuestion>().HasRequired(c => c.Segment).WithMany().WillCascadeOnDelete(false);

            modelBuilder.Entity<UserGroupRole>().HasRequired(s => s.UserGroup).WithMany().WillCascadeOnDelete(false);
            modelBuilder.Entity<UserGroupRole>().HasRequired(s => s.Role).WithMany().WillCascadeOnDelete(false);
            modelBuilder.Entity<UserGroupRole>().HasKey(q =>
                new
                {
                    q.UserGroupId,
                    q.RoleId
                });

            modelBuilder.Entity<UserGroupRole>()
                .HasRequired(t => t.UserGroup)
                .WithMany(t => t.Roles)
                .HasForeignKey(t => t.UserGroupId);

            modelBuilder.Entity<UserGroupRole>()
                .HasRequired(t => t.Role)
                .WithMany(t => t.UserGroupRoles)
                .HasForeignKey(t => t.RoleId);

            modelBuilder.Entity<ForumPostLike>().HasRequired(s => s.Post).WithMany().WillCascadeOnDelete(false);
            modelBuilder.Entity<ForumPostLike>().HasRequired(s => s.User).WithMany().WillCascadeOnDelete(false);
            modelBuilder.Entity<ForumPostLike>().HasKey(q =>
                new
                {
                    q.PostId,
                    q.UserId
                });

            modelBuilder.Entity<ForumPostLike>()
                .HasRequired(t => t.Post)
                .WithMany(t => t.Likes)
                .HasForeignKey(t => t.PostId);

            modelBuilder.Entity<ForumPostLike>()
                .HasRequired(t => t.User)
                .WithMany(t => t.PostLikes)
                .HasForeignKey(t => t.UserId);

            modelBuilder.Entity<ForumTopic>().HasMany(s => s.Tags).WithMany(c => c.Topics)
                .Map(cs =>
                {
                    cs.MapLeftKey("TopicId");
                    cs.MapRightKey("TagId");
                    cs.ToTable("ForumTopicTag");
                });

            modelBuilder.Entity<ForumCategory>().HasMany(s => s.Trainees).WithMany(c => c.ForumCategories)
                .Map(cs =>
                {
                    cs.MapLeftKey("CategoryId");
                    cs.MapRightKey("TraineeId");
                    cs.ToTable("ForumCategoryTrainee");
                });

            modelBuilder.Entity<OpenMaterial>().HasMany(s => s.Trainees).WithMany(c => c.OpenMaterials)
                .Map(cs =>
                {
                    cs.MapLeftKey("MaterialId");
                    cs.MapRightKey("TraineeId");
                    cs.ToTable("OpenMaterialTrainee");
                });

            modelBuilder.Entity<EvaluationExam>().HasMany(s => s.Trainees).WithMany(c => c.EvaluationExams)
                .Map(cs =>
                {
                    cs.MapLeftKey("ExamId");
                    cs.MapRightKey("TraineeId");
                    cs.ToTable("EvaluationExamTrainee");
                });


            modelBuilder.Entity<TraineeEvaluationExamAttempt>().HasKey(q =>
                new
                {
                    q.TraineeId,
                    q.ExamId
                });

            modelBuilder.Entity<TraineeEvaluationExamAttempt>()
                .HasRequired(t => t.Trainee)
                .WithMany(t => t.EvaluationExamAttempts)
                .HasForeignKey(t => t.TraineeId);

            modelBuilder.Entity<TraineeEvaluationExamAttempt>()
                .HasRequired(t => t.Exam)
                .WithMany(t => t.TraineeAttempts)
                .HasForeignKey(t => t.ExamId);




            modelBuilder.Entity<TraineeMockTestAttempt>().HasKey(q =>
                new
                {
                    q.TraineeId,
                    q.ExamId
                });

            modelBuilder.Entity<TraineeMockTestAttempt>()
                .HasRequired(t => t.Trainee)
                .WithMany(t => t.MockTestAttempts)
                .HasForeignKey(t => t.TraineeId);

            modelBuilder.Entity<TraineeMockTestAttempt>()
                .HasRequired(t => t.Exam)
                .WithMany(t => t.TraineeAttempts)
                .HasForeignKey(t => t.ExamId);

            modelBuilder.Entity<TraineeExamAttempt>().HasKey(q =>
                new
                {
                    q.TraineeId,
                    q.ExamId
                });

            modelBuilder.Entity<TraineeExamAttempt>()
                .HasRequired(t => t.Trainee)
                .WithMany(t => t.ExamAttempts)
                .HasForeignKey(t => t.TraineeId);

            modelBuilder.Entity<TraineeExamAttempt>()
                .HasRequired(t => t.Exam)
                .WithMany(t => t.TraineeAttempts)
                .HasForeignKey(t => t.ExamId);


            modelBuilder.Entity<SegmentWiseQsSetup>().HasKey(q =>
                new
                {
                    q.ExamId,
                    q.SegmentId
                });

            modelBuilder.Entity<SegmentWiseQsSetup>()
                .HasRequired(t => t.Exam)
                .WithMany(t => t.SegmentWiseQsSetups)
                .HasForeignKey(t => t.ExamId);

            modelBuilder.Entity<SegmentWiseQsSetup>()
                .HasRequired(t => t.Segment)
                .WithMany(t => t.QuestionForExamSetups)
                .HasForeignKey(t => t.SegmentId);
        }


        public virtual DbSet<UserGroup> UserGroups { get; set; }
        public virtual DbSet<Configuration> Configurations { get; set; }
        public virtual DbSet<Division> Divisions { get; set; }
        public virtual DbSet<Department> Departments { get; set; }
        public virtual DbSet<Trainee> Trainees { get; set; }
        public virtual DbSet<Course> Courses { get; set; }
        public virtual DbSet<CourseMaterial> CourseMaterials { get; set; }
        public virtual DbSet<MaterialResource> MaterialResources { get; set; }
        public virtual DbSet<CourseEnrollment> CourseEnrollments { get; set; }
        public virtual DbSet<CourseBookmark> CourseBookmarks { get; set; }

        public virtual DbSet<CourseExam> CourseExams { get; set; }
        public virtual DbSet<MCQQuestion> MCQQuestions { get; set; }
        public virtual DbSet<FIGQuestion> FIGQuestions { get; set; }
        public virtual DbSet<TrueFalseQuestion> TrueFalseQuestions { get; set; }
        public virtual DbSet<MatchingQuestion> MatchingQuestions { get; set; }
        public virtual DbSet<WrittenQuestion> WrittenQuestions { get; set; }

        public virtual DbSet<TraineeCourseActivity> TraineeCourseActivities { get; set; }
        public virtual DbSet<CourseMaterialStudy> CourseMaterialStudies { get; set; }
        public virtual DbSet<TraineeExam> TraineeExams { get; set; }
        public virtual DbSet<TraineeExamAttempt> TraineeExamAttempts { get; set; }

        public virtual DbSet<MCQEvaluationAnswer> MCQEvaluationAnswers { get; set; }
        public virtual DbSet<TrueFalseEvaluationAnswer> TrueFalseEvaluationAnswers { get; set; }
        public virtual DbSet<FIGEvaluationAnswer> FIGEvaluationAnswers { get; set; }
        public virtual DbSet<MatchingEvaluationAnswer> MatchingEvaluationAnswers { get; set; }
        public virtual DbSet<WrittenEvaluationAnswer> WrittenEvaluationAnswers { get; set; }

        public virtual DbSet<MCQAnswer> MCQAnswers { get; set; }
        public virtual DbSet<TrueFalseAnswer> TrueFalseAnswers { get; set; }
        public virtual DbSet<FIGAnswer> FIGAnswers { get; set; }
        public virtual DbSet<MatchingAnswer> MatchingAnswers { get; set; }
        public virtual DbSet<WrittenAnswer> WrittenAnswers { get; set; }

        public virtual DbSet<GradingPolicy> GradingPolicies { get; set; }
        public virtual DbSet<CourseDiscussion> CourseDiscussions { get; set; }
        public virtual DbSet<Notification> Notifications { get; set; }
        public virtual DbSet<CertificateConfiguration> CertificateConfigurations { get; set; }
        public virtual DbSet<TraineeCourseExamConfiguration> TraineeCourseExamConfigurations { get; set; }
        public virtual DbSet<CourseContentDependency> CourseContentDependencies { get; set; }


        public virtual DbSet<ForumTopic> ForumTopics { get; set; }
        public virtual DbSet<ForumPost> ForumPosts { get; set; }
        public virtual DbSet<ForumCategory> ForumCategories { get; set; }
        public virtual DbSet<ForumTag> ForumTags { get; set; }

        public virtual DbSet<FAQ> FAQs { get; set; }
        public virtual DbSet<Unit> Units { get; set; }
        public virtual DbSet<SubUnit> SubUnits { get; set; }


        public virtual DbSet<OpenMaterial> OpenMaterials { get; set; }
        public virtual DbSet<EvaluationExam> EvaluationExams { get; set; }
        public virtual DbSet<MCQEvaluationQuestion> MCQEvaluationQuestions { get; set; }
        public virtual DbSet<FIGEvaluationQuestion> FIGEvaluationQuestions { get; set; }
        public virtual DbSet<TrueFalseEvaluationQuestion> TrueFalseEvaluationQuestions { get; set; }
        public virtual DbSet<MatchingEvaluationQuestion> MatchingEvaluationQuestions { get; set; }
        public virtual DbSet<WrittenEvaluationQuestion> WrittenEvaluationQuestions { get; set; }
        public virtual DbSet<TraineeEvaluationExamAttempt> TraineeEvaluationExamAttempts { get; set; }
        public virtual DbSet<TraineeEvaluationExam> TraineeEvaluationExams { get; set; }
        public virtual DbSet<CourseMockTest> CourseMockTests { get; set; }
        public virtual DbSet<MockTestMCQQuestion> MockTestMCQQuestions { get; set; }
        public virtual DbSet<TraineeMockTest> TraineeMockTests { get; set; }
        public virtual DbSet<TraineeMockTestAttempt> TraineeMockTestAttempts { get; set; }
        public virtual DbSet<TraineeCertificate> TraineeCertificates { get; set; }
        public virtual DbSet<MCQMockTestAnswer> MCQMockTestAnswers { get; set; }
        public virtual DbSet<Library> Libraries { get; set; }
        public virtual DbSet<FeedbackQuestion> FeedbackQuestions { get; set; }
        public virtual DbSet<CourseFeedback> CourseFeedbacks { get; set; }
        public virtual DbSet<CourseFeedbackAnswer> CourseFeedbackAnswers { get; set; }
        public virtual DbSet<LearningHourFeedback> LearningHourFeedbacks { get; set; }
        public virtual DbSet<CourseCategory> CourseCategories { get; set; }
        public virtual DbSet<LhCategory> LhCategories { get; set; }
        public virtual DbSet<LibraryCategory> LibraryCategories { get; set; }
        public virtual DbSet<TraineeDevice> TraineeDevices { get; set; }
        public virtual DbSet<ExternalCourse> ExternalCourses { get; set; }
        public virtual DbSet<CourseSegment> CourseSegments { get; set; }
        public virtual DbSet<UserOtp> UserOtps { get; set; }


        public virtual DbSet<PendingNotification> PendingNotifications { get; set; }
        public virtual DbSet<NotificationEvent> NotificationEvents { get; set; }
        public virtual DbSet<NotificationEmail> NotificationEmails { get; set; }
        public virtual DbSet<AuditLog> AuditLogs { get; set; }
        public virtual DbSet<TraineeEvaluationActivity> TraineeEvaluationActivities { get; set; }
        public virtual DbSet<GhooriCertificate> GhooriCertificates { get; set; }


    }
}
