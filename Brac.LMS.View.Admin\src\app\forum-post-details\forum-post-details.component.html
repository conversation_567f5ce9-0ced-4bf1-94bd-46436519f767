<block-ui>
  <div class="row">
    <div class="col-lg-12" *ngIf="topic">
      <div class="d-flex flex-column h-100 bg-light rounded-1 shadow-lg">
        <div class="pt-2 p-md-3">
          <div class="d-sm-flex align-items-center justify-content-between pb-1 text-center text-sm-start">
            <h1 class="h3 mb-2 text-break">{{ topic.Title }}</h1>
            <a class="btn btn-link fs-5 fw-bold btn-sm mb-2 text-primary" (click)="backClicked()"><i
                class="fs-5 fa fa-arrow-left fs-base me-2"></i>Go Back</a>
          </div>
          <hr class="mt-1 mb-4" />
          <!-- Content-->

          <div class="row">
            <div class="col-12 border-end">
              <div class="border-bottom">
                <!-- <div class="d-flex align-items-center pb-1">
                  <h2 class="h3 nav-heading"><a [routerLink]="['/forum-details/1']"
                      routerLinkActive="router-link-active">Simple steps to an effective brand
                      strategy. Real life examples</a></h2>
                </div> -->
                <p class="mb-1" [innerHTML]="topic.Description | safe: 'html'"></p>

                <div class="d-sm-flex align-items-center justify-content-start text-center text-sm-start py-2">
                  <button class="btn btn-primary btn-sm me-2 py-1 px-2 fs-7"
                    tooltip="Category">{{topic.Category}}</button>
                  <button type="button" class="btn btn-outline-primary btn-sm me-2 py-1 px-2 fs-7"
                    *ngFor="let tag of topic.Tags" tooltip="tag">
                    {{ tag }}
                  </button>
                </div>


              </div>
              <!-- <hr class="mt-4"> -->

              <div class="row d-flex align-content-stretch  border-top border-bottom mb-4">
                <div class="col-lg-5 col-12 py-3 pe-md-3 border-end">
                  <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                    <div class="d-flex align-items-center me-grid-gutter">
                      <img *ngIf="
                          !topic.CreatorImage && topic.CreatorGender == 'Male'
                        " class="rounded-circle me-1" src="assets/images/user/male.jpg" alt="{{ topic.Creator }}"
                        width="64" />
                      <img *ngIf="
                          !topic.CreatorImage &&
                          topic.CreatorGender == 'Female'
                        " class="rounded-circle me-1" src="assets/images/user/female.jpg" alt="{{ topic.Creator }}"
                        width="64" />
                      <img *ngIf="
                          !topic.CreatorImage &&
                          topic.CreatorGender == 'Others'
                        " class="rounded-circle me-1" src="assets/images/user/other.jpg" alt="{{ topic.Creator }}"
                        width="64" />
                      <img *ngIf="topic.CreatorImage" class="rounded-circle me-1" [src]="baseUrl + topic.CreatorImage"
                        alt="{{ topic.Creator }}" width="64" />
                      <div class="ps-2">
                        <div class="text-nowrap">
                          <h6 class="fs-6 mb-n1">
                            Posted by:
                            <span class="text-primary">{{
                              topic.Creator
                              }}</span>
                          </h6>
                          <span class="fs-xs text-muted">
                            {{ topic.CreatedDate | amTimeAgo }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-lg-5 col-12 ps-md-3 py-3 border-end d-flex align-items-center justify-content-center">
                  <div [ngSwitch]="topic.Status">
                    <ng-container *ngSwitchCase="'Pending'">
                      <button class="btn btn-success me-1" container="body" tooltip="Approve Post"
                        (click)="approveTopic(topic.Id)">
                        <i class="fas fa-check"></i> Approve
                      </button>
                      <button class="btn btn-outline-danger me-1" container="body" tooltip="Delete Post"
                        (click)="deleteTopic(topic.Id)">
                        <i class="fas fa-trash"></i> Delete
                      </button>
                    </ng-container>

                    <button *ngSwitchCase="'Open'" class="btn btn-outline-danger me-1" container="body"
                      tooltip="Close Post" (click)="closeTopic(topic.Id)">
                      <i class="fas fa-times"></i> Close
                    </button>

                    <button *ngSwitchCase="'Closed'" class="btn btn-success me-1" container="body" tooltip="Open Post"
                      (click)="approveTopic(topic.Id)">
                      <i class="feather icon-check"></i> Open
                    </button>
                  </div>
                </div>
                <div class="col-lg-2 col-12 ps-md-3 py-3 d-flex align-items-center justify-content-end">
                  <!-- <button class="btn-like fs-5 cursor-default" type="button">
                      {{ topic.NoOfLikes }}
                    </button> -->
                  <div class="btn-comment cursor-default fs-5">
                    <i class="far fa-comment-alt text-primary"></i>
                    {{ topic.NoOfReplies }}
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-8">
                  <div class="d-sm-flex align-items-center justify-content-between pb-2 text-center text-sm-start">
                    <h1 class="h3 mb-0 text-nowrap">Comments</h1>
                  </div>
                </div>
                <div class="col-md-4">
                  <div *ngIf="topic.Status !== 'Open'"
                    class="d-sm-flex align-items-center justify-content-end text-center text-sm-start">
                    <span class="badge bg-success me-2 px-3 py-2 fs-6 rounded-3" [ngClass]="{
                        'bg-warning': topic.Status === 'Pending',
                        'bg-danger': topic.Status === 'Closed'
                      }">
                      {{ topic.Status }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="py-3">
                <div class="comment" *ngFor="let item of postList">

                  <div class="d-flex justify-content-start">
                    <p class="admin-font-16" [innerHTML]="item.Comment | safe: 'html'"></p>
                    <span tooltip="Delete the comment" (click)="deleteComment(item.Id,'comment')"
                      class="label label-danger text-danger ms-2 cursor-div"><i class="fas fa-times m-0"></i></span>
                  </div>


                  <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                      <img *ngIf="
                          !item.CreatorImage && item.CreatorGender == 'Male'
                        " class="rounded-circle" src="assets/img/user/male.jpg" alt="{{ item.Creator }}" width="42" />
                      <img *ngIf="
                          !item.CreatorImage && item.CreatorGender == 'Female'
                        " class="rounded-circle" src="assets/img/user/female.jpg" alt="{{ item.Creator }}"
                        width="42" />
                      <img *ngIf="
                          !item.CreatorImage && item.CreatorGender == 'Others'
                        " class="rounded-circle" src="assets/img/user/other.jpg" alt="{{ item.Creator }}" width="42" />
                      <img *ngIf="item.CreatorImage" class="rounded-circle" [src]="baseUrl + item.CreatorImage"
                        alt="{{ item.Creator }}" width="42" />
                      <div class="ps-2 ms-1">
                        <h4 class="fs-sm mb-0">{{ item.Creator }}</h4>
                        <span class="fs-xs text-muted">{{
                          item.CreatedDate | amTimeAgo
                          }}</span>
                      </div>
                    </div>
                    <div class="d-flex align-items-center justify-content-center justify-content-md-end">
                      <button [disabled]="true" class="btn-comment-like fs-5 fw-bold me-2" type="button">
                        <i class="far fa-thumbs-up me-1" [ngClass]="{
                            'fa-solid text-primary': item.Liked,
                            'fa-regular': !item.Liked
                          }"></i>
                        {{ item.NoOfLikes }}
                      </button>
                      <button class="btn-comment fs-5 fw-bold  cursor-default" type="button">
                        <i class="far fa-comment-alt me-1"></i>
                        {{ item.Replies.length }}
                      </button>
                    </div>
                  </div>
                  <div class="comment mt-3" *ngFor="let reply of item.Replies">

                    <!-- <div class="comment mt-3"><span> ব্র্যাক ব্যাংক কবে থেকে প্রণোদনা দেওয়া শুরু হবে? </span>
                      <div class="d-inline-flex align-items-center"> – <span class="fw-bold text-primary">Ullah, Mr.
                          Shah Wali </span>
                      </div>
                      <span class="text-muted"> Today at 11:09 AM </span>
                    </div> -->


                    <div class="admin-font-14" class="d-flex justify-content-start">
                      {{ reply.Comment }}
                      <span tooltip="Delete the reply" (click)="deleteComment(reply.Id,'reply')"
                        class="label label-danger text-danger ms-2 my-auto cursor-div"><i
                          class="fas fa-times m-0"></i></span>
                    </div>

                    <!-- <span class="admin-font-14">
                      {{ reply.Comment }}
                    </span> -->
                    <div class="d-inline-flex align-items-center">
                      –
                      <span class="fw-bold text-primary">
                        {{ reply.Creator }}</span>
                    </div>
                    <span class="text-muted">
                      {{ reply.CreatedDate | amCalendar }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</block-ui>