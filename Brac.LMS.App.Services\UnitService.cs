﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.Services
{
    public class UnitService : IUnitService
    {
        private readonly ApplicationDbContext _context;
        public UnitService()
        {
            _context = new ApplicationDbContext();
        }
        public async Task<APIResponse> UnitCreateOrUpdate(UnitModel model, IIdentity identity)
        {
            bool isEdit = true;
            try
            {
                if (await _context.Units.AnyAsync(x => x.Id != model.Id && x.Name == model.Name))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Already exists: " + model.Name
                    };

                Unit item;
                if (model.Id.HasValue && model.Id.Value > 0)
                {
                    item = await _context.Units.FindAsync(model.Id);
                    if (item == null) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Trainee Group not found"
                    };
                }
                else
                {
                    item = new Unit();
                    isEdit = false;
                }

                item.Name = model.Name;
                item.Active = model.Active;
                item.CreatedDate = DateTime.UtcNow.ToKindLocal();

                if (!isEdit)
                {
                    _context.Units.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved"),
                    Data = item.Id
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetUnitList(string name, int size, int pageNumber)
        {
            try
            {
                var query = _context.Units.AsQueryable();
                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Name.Contains(name));
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderBy(x => x.CreatedDate)
                               .Skip(pageNumber * size).Take(size);
                var data = await filteredQuery.Select(x => new { x.Id, x.Name, x.Active }).ToListAsync();
                var count = await ((!string.IsNullOrEmpty(name)) ? filteredQuery.CountAsync() : _context.Units.CountAsync());


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Total = count
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetUnitById(long id)
        {
            try
            {
                var data = await _context.Units.Where(t => t.Id == id)
                .Select(t => new
                {
                    t.Id,
                    t.Name,
                    t.Active
                }).FirstOrDefaultAsync();
                if (data == null)
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Trainee Group not found"
                    };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetUnitDropDownList()
        {
            try
            {
                var data = await _context.Units.Where(x => x.Active).OrderBy(o => o.Name)
                .Select(t => new
                {
                    t.Id,
                    t.Name
                }).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> DeleteUnitById(long id)
        {
            try
            {
                var item = await _context.Units.FindAsync(id);
                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Trainee Group not found"
                };

                if (await _context.Trainees.AnyAsync(x => x.UnitId == item.Id))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "This trainee group has already been tagged with any trainee. So, you can't delete this trainee group"
                    };

                _context.Entry(item).State = EntityState.Deleted;

                await _context.SaveChangesAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Deleted"
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
    }

    public interface IUnitService
    {
        Task<APIResponse> UnitCreateOrUpdate(UnitModel model, IIdentity identity);
        Task<APIResponse> GetUnitList(string name, int size, int pageNumber);
        Task<APIResponse> GetUnitById(long id);
        Task<APIResponse> GetUnitDropDownList();
        Task<APIResponse> DeleteUnitById(long id);
    }
}
