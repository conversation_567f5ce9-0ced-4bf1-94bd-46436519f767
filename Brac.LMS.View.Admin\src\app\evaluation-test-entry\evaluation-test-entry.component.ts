import {
  Component,
  TemplateRef,
  ViewEncapsulation,
  OnInit,
} from "@angular/core";
import { ColumnMode } from "@swimlane/ngx-datatable";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { CommonService } from "../_services/common.service";
import { ToastrService } from "ngx-toastr";
import { BlockUI, NgBlockUI } from "ng-block-ui";

import { NgbTimepickerConfig } from "@ng-bootstrap/ng-bootstrap";
import * as moment from "moment-timezone";
import { trigger, transition, style, animate } from "@angular/animations";
import { ResponseStatus } from "../_models/enum";
import { BsModalRef, BsModalService } from "ngx-bootstrap/modal";
import { BsDatepickerConfig } from "ngx-bootstrap/datepicker";
import { debounceTime } from "rxjs/operators";
import { environment } from "src/environments/environment";
import { Editor, Toolbar } from "ngx-editor";
import { AppComponent } from "../app.component";

@Component({
  selector: "app-evaluation-test-entry",
  templateUrl: "./evaluation-test-entry.component.html",

  encapsulation: ViewEncapsulation.None,
  animations: [
    trigger("inOutAnimation", [
      transition(":enter", [
        style({ height: 0, opacity: 0 }),
        animate("1s ease-out", style({ height: 300, opacity: 1 })),
      ]),
      transition(":leave", [
        animate("1s ease-out", style({ height: 0, opacity: 0 })),
      ]),
    ]),
  ],
})
export class EvaluationTestEntryComponent implements OnInit {
  entryForm: FormGroup;
  submitted = false;
  @BlockUI() blockUI: NgBlockUI;
  formTitle = "Evaluation Test Entry";
  btnSaveText = "Save";
  selectedSet: any;
  id: string;
  is_per = true;
  ExamExists = false;
  ExamExistsMsg = "";
  modalTitle = "Set Time";

  rows = [];
  loadingIndicator = false;
  ColumnMode = ColumnMode;
  isEdit: boolean = false;

  mcqList = [];
  loadingMCQ = false;
  mcqFile: any;
  btnMCQSaveText: string = "Save";
  modalMCQTitle: string = "Add MCQ Questions";
  modalMCQRef: BsModalRef;

  truFalseList = [];
  tfQuestionList = [];
  tfqFile: any;
  loadingTFQ = false;
  btnTFQSaveText: string = "Save";
  modalTFTitle: string = "Add True/False Questions";
  modalTFRef: BsModalRef;

  figList = [];
  figQuestionList = [];
  figqFile: any;
  loadingFIG = false;
  btnFIGSaveText: string = "Save";
  modalFIGTitle: string = "Add fill in the gaps Questions";
  modalFIGRef: BsModalRef;

  matchingList = [];
  matchingQuestionList = [];
  matchingqFile: any;
  loadingMatching = false;
  btnMatchingSaveText: string = "Save";
  modalMatchingTitle: string = "Add Left Right Matching Questions";
  modalMatchingRef: BsModalRef;

  writtenList = [];
  writtenQuestionList = [];
  wqFile: any;
  loadingWritten = false;
  btnWrittenSaveText: string = "Save";
  modalWrittenTitle: string = "Add Written Questions";
  modalWrittenRef: BsModalRef;

  modalConfig: any = { class: "gray modal-lg", backdrop: "static" };
  modalRef: BsModalRef;
  bsConfig: Partial<BsDatepickerConfig>;

  courseList: Array<any> = [];
  examList: Array<any> = [];
  setList: Array<any> = [];
  mcqQuestionList: Array<any> = [];

  traineeForm: FormGroup;
  traineeList: Array<any> = [];
  selectedTraineeList: Array<any> = [];
  allowedTrainees: Array<any> = [];
  traineeSelected = false;

  selectedExam: any;

  MCQMark: number;
  TrueFalseMark: number;
  FIGMark: number;
  MatchingMark: number;
  WrittenMark: number;

  modalTraineeRef: BsModalRef;

  categoryList: Array<any> = [];
  departmentList: Array<any> = [];
  departmentSelected = false;

  divisionList: Array<any> = [];
  divisionSelected = false;

  unitList: Array<any> = [];
  unitSelected = false;
  allowList: Array<any> = ["All", "Division", "Department", "Unit", "Trainee"];

  select_All = false;
  modalxLConfig: any = { class: "gray modal-xl", backdrop: "static" };

  imageFile: any;
  imageUrl: string | ArrayBuffer;

  editor: Editor;
  toolbar: Toolbar = [
    ["bold", "italic"],
    ["underline", "strike"],
    ["ordered_list", "bullet_list"],
    [{ heading: ["h1", "h2", "h3", "h4", "h5", "h6"] }],
    ["text_color", "background_color"],
    ["align_left", "align_center", "align_right", "align_justify"],
  ];

  scrollBarHorizontal = window.innerWidth < 1200;

  constructor(
    private appComponent: AppComponent,
    private modalService: BsModalService,
    public formBuilder: FormBuilder,
    private _service: CommonService,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    config: NgbTimepickerConfig
  ) {
    window.onresize = () => {
      this.scrollBarHorizontal = window.innerWidth < 1200;
    };
    this.bsConfig = Object.assign({}, { containerClass: "theme-blue" });
    config.seconds = false;
    config.spinners = false;
    if (this.route.snapshot.queryParamMap.has("id")) {
      this.id = this.route.snapshot.queryParamMap.get("id");
      this.isEdit = true;
      this.getMarks();
      this.getItem();
    }

    console.log(moment.tz.guess());
  }
  handleSelectClick(selectElement) {
    this.appComponent.handleSelectClick(selectElement);
  }
  ngOnInit() {
    this.editor = new Editor();

    this.entryForm = this.formBuilder.group({
      id: [this.id],
      instructions: [null],
      exam: [null, [Validators.required]],
      duration: [null, [Validators.required]],
      categoryId: [true, [Validators.required]],
      mcqOnly: [true],
      random: [false],
      publish: [false],
      quota: [null, [Validators.required]],
      no_of_mcq: [null, [Validators.required]],
      no_of_tfq: [null],
      no_of_figq: [null],
      no_of_mq: [null],
      no_of_wq: [null],
      startDate: [null],
      endDate: [null],
      allowFor: [null, [Validators.required]],
      division: [null],
      department: [null],
      unit: [null],
      pin: [null],
      active: true,
      order: [null],
    });

    if (!this.isEdit) this.getMarks();

    this.traineeForm = this.formBuilder.group({
      selectionType: [null],
      pin: [null],
      file: [null],
    });

    this.traineeForm
      .get("pin")
      .valueChanges.pipe(debounceTime(700))
      .subscribe((value) => {
        this.getTraineelList(value);
      });

    this.getCategoryList();
  }

  get f() {
    return this.entryForm.controls;
  }
  get tf() {
    return this.traineeForm.controls;
  }

  getCategoryList() {
    this._service.get("learning-hour-category/dropdown-list").subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.categoryList = res.Data;
      },
      (err) => {}
    );
  }

  getMarks() {
    this._service.get("configuration/get-exam-data").subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.entryForm.controls["instructions"].setValue(
          res.Data.ExamInstruction
        );
        this.MCQMark = res.Data.MCQMark;
        this.TrueFalseMark = res.Data.TrueFalseMark;
        this.FIGMark = res.Data.FIGMark;
        this.MatchingMark = res.Data.MatchingMark;
        this.WrittenMark = res.Data.WrittenMark;
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
  }

  formatAMPM(date) {
    var hours = date.getHours();
    var minutes = date.getMinutes();
    var ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    minutes = minutes < 10 ? "0" + minutes : minutes;
    var strTime = hours + ":" + minutes + " " + ampm;
    return strTime;
  }

  getItem() {
    this.blockUI.start("Getting data. Please wait ...");
    this._service.get("evaluation-exam/get/" + this.id).subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        // let startDate, endDate, startTime, endTime;
        // if (res.Data.StartDate && res.Data.EndDate) {
        //   startDate = new Date(res.Data.StartDate);
        //   startDate.setMinutes(startDate.getMinutes());

        //   endDate = new Date(res.Data.EndDate);
        //   endDate.setMinutes(endDate.getMinutes());

        //   startTime = this.formatAMPM(startDate);
        //   endTime = this.formatAMPM(endDate);
        // }

        this.entryForm.controls["exam"].setValue(res.Data.ExamName);
        this.entryForm.controls["instructions"].setValue(
          res.Data.ExamInstructions
        );
        this.entryForm.controls["categoryId"].setValue(res.Data.CategoryId);
        this.entryForm.controls["quota"].setValue(res.Data.Quota);
        this.entryForm.controls["random"].setValue(res.Data.Random);
        this.entryForm.controls["publish"].setValue(res.Data.Publish);
        // this.entryForm.controls['minPercentage'].setValue(res.Data.MinPercentageForEvaluation);
        // this.entryForm.controls['minMarks'].setValue(res.Data.MinMarksForEvaluation);
        this.entryForm.controls["duration"].setValue(res.Data.DurationMnt);
        this.entryForm.controls["no_of_mcq"].setValue(res.Data.ExamMCQNo);
        this.entryForm.controls["no_of_tfq"].setValue(res.Data.ExamTrueFalseNo);
        this.entryForm.controls["no_of_figq"].setValue(res.Data.ExamFIGNo);
        this.entryForm.controls["no_of_mq"].setValue(res.Data.ExamMatchingNo);
        this.entryForm.controls["no_of_wq"].setValue(res.Data.ExamWritingNo);
        this.entryForm.controls["mcqOnly"].setValue(res.Data.MCQOnly);
        this.entryForm.controls["allowFor"].setValue(res.Data.AllowFor);
        this.entryForm.controls["active"].setValue(res.Data.Active);
        this.entryForm.controls["order"].setValue(res.Data.Order);
        this.imageUrl = environment.baseUrl + res.Data.ImagePath;
        this.editChangeAllowedFor(
          res.Data.AllowFor,
          res.Data.DivisionId,
          res.Data.DepartmentId,
          res.Data.UnitId,
          res.Data.Trainees
        );

        // **FIX: Don't double-convert time - backend already returns local time**
        // WHY: Backend now stores local time, so no need to convert from UTC
        // BENEFIT: Prevents time shifting issues when editing exams
        if (res.Data.StartDate)
          this.entryForm.controls["startDate"].setValue(
            moment(res.Data.StartDate).format("yyyy-MM-DDTHH:mm:ss")
          );
        if (res.Data.EndDate)
          this.entryForm.controls["endDate"].setValue(
            moment(res.Data.EndDate).format("yyyy-MM-DDTHH:mm:ss")
          );

        this.btnSaveText = "Update";
        this.getMCQList();
        this.getTFQList();
        this.geWrittenList();
        this.geFIGIGList();
        this.getMatchingList();
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
    this.submitted = false;
  }

  onFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid) {
      return;
    }

    if (!this.entryForm.value.duration) {
      this.toastr.warning(
        "Please insert exam duration for this exam",
        "WARNING!",
        { timeOut: 3000 }
      );
      return;
    }

    if (!this.entryForm.value.id && !this.imageUrl) {
      this.toastr.warning("Please upload a cover photo", "Warning!");
      return;
    }

    if (
      this.entryForm.value.allowFor === "Trainee" &&
      this.allowedTrainees.length === 0
    ) {
      this.toastr.warning("Please select at least one trainee ", "Warning!");
      return;
    }

    this.blockUI.start("Saving...");

    const obj = {
      Id: this.entryForm.value.id ? this.entryForm.value.id : null,
      ExamName: this.entryForm.value.exam,
      ExamInstructions: this.entryForm.value.instructions
        ? this.entryForm.value.instructions.trim()
        : "",
      Quota: Number(this.entryForm.value.quota),
      Random: Number(this.entryForm.value.random),
      Publish: Number(this.entryForm.value.publish),
      ExamMCQNo: this.entryForm.value.no_of_mcq
        ? Number(this.entryForm.value.no_of_mcq)
        : 0,
      ExamTrueFalseNo: this.entryForm.value.no_of_tfq
        ? Number(this.entryForm.value.no_of_tfq)
        : 0,
      ExamFIGNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq
          ? Number(this.entryForm.value.no_of_figq)
          : 0,
      ExamMatchingNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq
          ? Number(this.entryForm.value.no_of_mq)
          : 0,
      ExamWritingNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq
          ? Number(this.entryForm.value.no_of_wq)
          : 0,
      CategoryId: this.entryForm.value.categoryId,
      MCQOnly: this.entryForm.value.mcqOnly,
      DurationMnt: Number(this.entryForm.value.duration),
      QuesType: "NoQues",
      StartDate: this.entryForm.value.startDate
        ? moment(this.entryForm.value.startDate).toISOString()
        : "",
      EndDate: this.entryForm.value.endDate
        ? moment(this.entryForm.value.endDate).toISOString()
        : "",
      AllowFor: this.entryForm.value.allowFor,
      DivisionId: this.entryForm.value.division,
      DepartmentId: this.entryForm.value.department,
      UnitId: this.entryForm.value.unit,
      Trainees: this.allowedTrainees.map((x) => x.Id),
      Active: this.entryForm.value.active,
      Order: this.entryForm.value.order,
    };
    const formdata = new FormData();
    formdata.append("Model", JSON.stringify(obj));
    if (this.imageFile) formdata.append("Image", this.imageFile);
    console.log("obj", obj);
    this._service.post("evaluation-exam/create-or-update", formdata).subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
        this.entryForm.controls["id"].setValue(res.Data);
        this.btnSaveText = "Update";
        this.id = res.Data;
        this.isEdit = true;
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
  }

  clearForm() {
    this.entryForm.reset();
    this.submitted = false;
    this.formTitle = "Create Evaluation Test";
    this.btnSaveText = "Save";
    this.entryForm.controls["mcqOnly"].setValue(true);
  }

  deleteQuestion(id: number, type: string) {
    let url = "";
    switch (type) {
      case "MCQ":
        url = "evaluation-exam/delete-mcq/" + id;
        break;
      case "TFQ":
        url = "evaluation-exam/delete-true-false/" + id;
        break;
      case "FIGQ":
        url = "evaluation-exam/delete-fill-in-the-gap/" + id;
        break;
      case "WQ":
        url = "evaluation-exam/delete-written/" + id;
        break;
      case "MatchingQ":
        url = "evaluation-exam/delete-matching/" + id;
        break;
    }
    this.blockUI.start("Deleting data. Please wait ...");
    this._service.get(url).subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.toastr.success(res.Message, "SUCCESS!", { timeOut: 2000 });
        switch (type) {
          case "MCQ":
            this.getMCQList();
            break;
          case "TFQ":
            this.getTFQList();
            break;
          case "FIGQ":
            this.geFIGIGList();
            break;
          case "MatchingQ":
            this.getMatchingList();
            break;
          case "WQ":
            this.geWrittenList();
            break;
        }
      },
      (err) => {
        this.toastr.error(err.message || err, "Error!", { timeOut: 10000 });
        this.blockUI.stop();
      }
    );
  }

  downloadQuestionList(type) {
    let url = "",
      title = "";
    let timeZoneOffset = new Date().getTimezoneOffset();
    switch (type) {
      case "MCQ":
        url =
          "evaluation-exam/get-mcq-list-excel/" +
          this.id +
          "/" +
          timeZoneOffset;
        title = "evaluation_test_mcq_file.xlsx";
        break;
      case "TFQ":
        url =
          "evaluation-exam/get-true-false-list-excel/" +
          this.id +
          "/" +
          timeZoneOffset;
        title = "evaluation_test_true_false_file.xlsx";
        break;
      case "FIGQ":
        url =
          "evaluation-exam/get-fill-in=the=gap-list-excel/" +
          this.id +
          "/" +
          timeZoneOffset;
        title = "evaluation_test_fill_in_the_gap_file.xlsx";
        break;
      case "MatchingQ":
        url =
          "evaluation-exam/get-matching-list-excel/" +
          this.id +
          "/" +
          timeZoneOffset;
        title = "evaluation_test_left_right_matching_file.xlsx";
        break;
      case "WQ":
        url =
          "evaluation-exam/get-written-list-excel/" +
          this.id +
          "/" +
          timeZoneOffset;
        title = "evaluation_test_written_file.xlsx";
        break;

      default:
        break;
    }
    this.blockUI.start("Generating excel file. Please wait ...");
    return this._service.downloadFile(url).subscribe(
      (res) => {
        this.blockUI.stop();
        const url = window.URL.createObjectURL(res);
        var link = document.createElement("a");
        link.href = url;
        link.download = title;
        link.click();
      },
      (error) => {
        this.toastr.error(error.message || error, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
  }

  // ============== MCQ Question ====================
  getMCQList() {
    const obj = {
      examId: this.id,
      // examId: this.entryForm.value.examId,
      // setId: this.entryForm.value.setId
    };

    this.loadingMCQ = true;
    this._service.get("evaluation-exam/get-mcq-list/" + this.id).subscribe(
      (res) => {
        setTimeout(() => {
          this.loadingMCQ = false;
        }, 1000);

        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.mcqList = res.Data;
        console.log("res.Data", res.Data);
        // let answers = [];
        // res.Data.forEach(element => {
        //   answers = element.Answers.split(',');
        //   this.mcqList.push({
        //     Question: element.Question,
        //     Options: [{ Text: element.Option1, Selected: answers.indexOf('0') !== -1 },
        //     { Text: element.Option2, Selected: answers.indexOf('1') !== -1 },
        //     { Text: element.Option3, Selected: answers.indexOf('2') !== -1 },
        //     { Text: element.Option4, Selected: answers.indexOf('3') !== -1 }],
        //     Mark: element.Mark
        //   });
        // });
      },
      (err) => {
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
        setTimeout(() => {
          this.loadingMCQ = false;
        }, 1000);
      }
    );
  }

  openMCQModal(template: TemplateRef<any>) {
    let answers = [];
    if (this.mcqList.length === 0) {
      this.btnMCQSaveText = "Save";
      this.addNewMCQ();
    } else {
      this.mcqList.forEach((element) => {
        answers = element.Answers.split(",");
        this.mcqQuestionList.push({
          Question: element.Question,
          Options: [
            { Text: element.Option1, Selected: answers.indexOf("1") !== -1 },
            { Text: element.Option2, Selected: answers.indexOf("2") !== -1 },
            { Text: element.Option3, Selected: answers.indexOf("3") !== -1 },
            { Text: element.Option4, Selected: answers.indexOf("4") !== -1 },
          ],
          Mark: element.Mark,
        });
      });
    }
    this.modalMCQRef = this.modalService.show(template, this.modalConfig);
  }

  modalMCQHide() {
    this.modalMCQRef.hide();
    this.mcqQuestionList = [];
    this.mcqFile = null;
  }

  deleteMCQ(index: number) {
    this.mcqQuestionList.splice(index, 1);
  }

  addNewMCQ() {
    this.mcqQuestionList.push({
      Question: null,
      Options: [
        { Text: null, Selected: false },
        { Text: null, Selected: false },
        { Text: null, Selected: false },
        { Text: null, Selected: false },
      ],
      Mark: this.MCQMark,
    });
  }

  downloadSampleMCQ() {
    return this._service
      .downloadFile("exam/download-sample-question", { quesType: "MCQ" })
      .subscribe(
        (res) => {
          const url = window.URL.createObjectURL(res);
          var link = document.createElement("a");
          link.href = url;
          link.download = "evaluation_test_mcq_sample_file.xlsx";
          link.click();
        },
        (error) => {
          this.toastr.error(error.message || error, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  onMCQFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid) {
      this.toastr.warning(
        "Please fill up all the details of the exam first",
        "WARNING!",
        { timeOut: 3000 }
      );
      return;
    }
    if (!this.entryForm.value.duration) {
      this.toastr.warning(
        "Please insert exam duration for this exam",
        "WARNING!",
        { timeOut: 3000 }
      );
      return;
    }

    let questions = [];
    for (let i = 0; i < this.mcqQuestionList.length; i++) {
      const element = this.mcqQuestionList[i];

      if (!element.Question) {
        this.toastr.error(
          "Please Enter Question for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      // if (!element.Options[0].Text || !element.Options[1].Text || !element.Options[2].Text || !element.Options[3].Text) {
      if (
        element.Options.filter((x) => x.Text === null || x.Text === "").length >
        0
      ) {
        this.toastr.error(
          "Value missing for an option of Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      // if (!element.Option1.Selected && !element.Option1.Selected && !element.Option1.Selected && !element.Option1.Selected) {
      if (element.Options.filter((x) => x.Selected).length === 0) {
        this.toastr.error(
          "No option has been selected for answer of Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      if (!element.Mark) {
        this.toastr.error(
          "Please enter mark for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      questions.push({
        Question: element.Question,
        Mark: element.Mark,
        Option1: element.Options[0].Text,
        Option2: element.Options[1].Text,
        Option3: element.Options[2].Text,
        Option4: element.Options[3].Text,
        Answers: element.Options.map(function (x, i) {
          if (x.Selected) return i + 1;
          else return -1;
        })
          .filter((x) => x >= 0)
          .join(),
      });
    }

    if (!this.entryForm.value.id && !this.imageUrl) {
      this.toastr.warning("Please upload a cover photo", "Warning!");
      return;
    }

    if (
      this.entryForm.value.allowFor === "Trainee" &&
      this.allowedTrainees.length === 0
    ) {
      this.toastr.warning("Please select at least one trainee ", "Warning!");
      return;
    }

    this.blockUI.start("Saving...");

    const obj = {
      Id: this.entryForm.value.id ? this.entryForm.value.id : null,
      ExamName: this.entryForm.value.exam,
      ExamInstructions: this.entryForm.value.instructions
        ? this.entryForm.value.instructions.trim()
        : "",
      Quota: Number(this.entryForm.value.quota),
      Random: Number(this.entryForm.value.random),
      Publish: Number(this.entryForm.value.publish),
      ExamMCQNo: this.entryForm.value.no_of_mcq
        ? Number(this.entryForm.value.no_of_mcq)
        : 0,
      ExamTrueFalseNo: this.entryForm.value.no_of_tfq
        ? Number(this.entryForm.value.no_of_tfq)
        : 0,
      ExamFIGNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq
          ? Number(this.entryForm.value.no_of_figq)
          : 0,
      ExamMatchingNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq
          ? Number(this.entryForm.value.no_of_mq)
          : 0,
      ExamWritingNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq
          ? Number(this.entryForm.value.no_of_wq)
          : 0,
      CategoryId: this.entryForm.value.categoryId,
      MCQOnly: this.entryForm.value.mcqOnly,
      // MinPercentageForEvaluation: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,
      // MinMarksForEvaluation: Number(this.entryForm.value.minMarks),
      DurationMnt: Number(this.entryForm.value.duration),
      MCQs: questions,
      QuesType: "MCQ",
      StartDate: this.entryForm.value.startDate
        ? moment(this.entryForm.value.startDate).toISOString()
        : "",
      EndDate: this.entryForm.value.endDate
        ? moment(this.entryForm.value.endDate).toISOString()
        : "",
      AllowFor: this.entryForm.value.allowFor,
      DivisionId: this.entryForm.value.division,
      DepartmentId: this.entryForm.value.department,
      UnitId: this.entryForm.value.unit,
      Trainees: this.allowedTrainees.map((x) => x.Id),
    };
    console.log("obj", obj);
    const formdata = new FormData();
    formdata.append("Model", JSON.stringify(obj));
    if (this.imageFile) formdata.append("Image", this.imageFile);
    if (this.mcqFile) {
      formdata.append("File", this.mcqFile);
    }

    this._service.post("evaluation-exam/create-or-update", formdata).subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
        this.entryForm.controls["id"].setValue(res.Data);
        this.btnSaveText = "Update";
        this.id = res.Data;
        this.isEdit = true;
        this.getMCQList();
        this.modalMCQHide();
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
  }

  loadMCQFile(files) {
    if (files.length === 0) return;
    this.mcqFile = files[0];
  }

  resetMCQFile(element) {
    element.value = "";
    this.mcqFile = null;
  }

  // ============== True/False Question ====================
  getTFQList() {
    const obj = {
      examId: this.id,
    };

    this.loadingTFQ = true;
    this._service
      .get("evaluation-exam/get-true-false-list/" + this.id)
      .subscribe(
        (res) => {
          setTimeout(() => {
            this.loadingTFQ = false;
          }, 1000);

          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.truFalseList = res.Data;
        },
        (err) => {
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          setTimeout(() => {
            this.loadingTFQ = false;
          }, 1000);
        }
      );
  }

  openTFQModal(template: TemplateRef<any>) {
    if (this.truFalseList.length === 0) {
      this.btnTFQSaveText = "Save";
      this.addNewTFQ();
    } else {
      this.truFalseList.forEach((element) => {
        this.tfQuestionList.push({
          Question: element.Question,
          Answer: element.Answer,
          CorrectAnswer: element.CorrectAnswer,
          Mark: element.Mark,
        });
      });
    }
    this.modalTFRef = this.modalService.show(template, this.modalConfig);
  }

  modalTFQHide() {
    this.modalTFRef.hide();
    this.tfQuestionList = [];
    this.tfqFile = null;
  }

  deleteTFQ(index: number) {
    this.tfQuestionList.splice(index, 1);
  }

  addNewTFQ() {
    this.tfQuestionList.push({
      Id: null,
      Question: null,
      Answer: true,
      CorrectAnswer: null,
      Mark: this.TrueFalseMark,
    });
  }

  onTFQFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid) {
      return;
    }

    for (let i = 0; i < this.tfQuestionList.length; i++) {
      const element = this.tfQuestionList[i];
      if (!element.Question) {
        this.toastr.error(
          "Please Enter Question for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      // if (!element.Answer && !element.CorrectAnswer) {
      // if (!element.Answer && !element.CorrectAnswer) {

      //   console.log('element.Answer',element.Answer);
      //   console.log('element.CorrectAnswer',element.CorrectAnswer);
      //   this.toastr.error(
      //     "Please enter correct answer for Question: " + ++i,
      //     "WARNING!",
      //     { timeOut: 4000 }
      //   );
      //   return false;
      // }

      if (!element.Mark) {
        this.toastr.error(
          "Please enter mark for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }
    }

    if (!this.entryForm.value.id && !this.imageUrl) {
      this.toastr.warning("Please upload a cover photo", "Warning!");
      return;
    }

    if (
      this.entryForm.value.allowFor === "Trainee" &&
      this.allowedTrainees.length === 0
    ) {
      this.toastr.warning("Please select at least one trainee ", "Warning!");
      return;
    }

    this.blockUI.start("Saving...");
    const obj = {
      Id: this.entryForm.value.id ? this.entryForm.value.id : null,
      ExamName: this.entryForm.value.exam,
      ExamInstructions: this.entryForm.value.instructions
        ? this.entryForm.value.instructions.trim()
        : "",
      Quota: Number(this.entryForm.value.quota),
      Random: Number(this.entryForm.value.random),
      Publish: Number(this.entryForm.value.publish),
      ExamMCQNo: this.entryForm.value.no_of_mcq
        ? Number(this.entryForm.value.no_of_mcq)
        : 0,
      ExamTrueFalseNo: this.entryForm.value.no_of_tfq
        ? Number(this.entryForm.value.no_of_tfq)
        : 0,
      ExamFIGNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq
          ? Number(this.entryForm.value.no_of_figq)
          : 0,
      ExamMatchingNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq
          ? Number(this.entryForm.value.no_of_mq)
          : 0,
      ExamWritingNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq
          ? Number(this.entryForm.value.no_of_wq)
          : 0,
      CategoryId: this.entryForm.value.categoryId,
      MCQOnly: this.entryForm.value.mcqOnly,
      // MinPercentageForEvaluation: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,
      // MinMarksForEvaluation: Number(this.entryForm.value.minMarks),
      DurationMnt: Number(this.entryForm.value.duration),
      TruFalseQs: this.tfQuestionList,
      QuesType: "TrueFalse",
      StartDate: this.entryForm.value.startDate
        ? moment(this.entryForm.value.startDate).toISOString()
        : "",
      EndDate: this.entryForm.value.endDate
        ? moment(this.entryForm.value.endDate).toISOString()
        : "",
      AllowFor: this.entryForm.value.allowFor,
      DivisionId: this.entryForm.value.division,
      DepartmentId: this.entryForm.value.department,
      UnitId: this.entryForm.value.unit,
      Trainees: this.allowedTrainees.map((x) => x.Id),
    };

    const formdata = new FormData();
    formdata.append("Model", JSON.stringify(obj));
    if (this.imageFile) formdata.append("Image", this.imageFile);
    if (this.tfqFile) {
      formdata.append("File", this.tfqFile);
    }

    this._service.post("evaluation-exam/create-or-update", formdata).subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
        this.entryForm.controls["id"].setValue(res.Data);
        this.btnSaveText = "Update";
        this.id = res.Data;
        this.isEdit = true;
        this.getTFQList();
        this.modalTFQHide();
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
  }

  downloadSampleTFQ() {
    return this._service
      .downloadFile("exam/download-sample-question", { quesType: "TrueFalse" })
      .subscribe(
        (res) => {
          const url = window.URL.createObjectURL(res);
          var link = document.createElement("a");
          link.href = url;
          link.download = "evaluation_test_true_false_sample_file.xlsx";
          link.click();
        },
        (error) => {
          this.toastr.error(error.message || error, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  loadTFQFile(files) {
    if (files.length === 0) return;
    this.tfqFile = files[0];
  }

  resetTFQFile(element) {
    element.value = "";
    this.tfqFile = null;
  }

  // ============== Fill in the gaps Question ====================
  geFIGIGList() {
    const obj = {
      examId: this.id,
    };

    this.loadingFIG = true;
    this._service
      .get("evaluation-exam/get-fill-in-the-gap-list/" + this.id)
      .subscribe(
        (res) => {
          setTimeout(() => {
            this.loadingFIG = false;
          }, 1000);

          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.figList = res.Data;
        },
        (err) => {
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          setTimeout(() => {
            this.loadingFIG = false;
          }, 1000);
        }
      );
  }

  openFIGModal(template: TemplateRef<any>) {
    if (this.figList.length === 0) {
      this.btnFIGSaveText = "Save";
      this.addNewFIG();
    } else {
      this.figList.forEach((element) => {
        this.figQuestionList.push({
          Question: element.Question,
          Answer: element.Answer,
          //Serial: element.Serial,
          Mark: element.Mark,
        });
      });
    }
    this.modalFIGRef = this.modalService.show(template, this.modalConfig);
  }

  modalFIGHide() {
    this.modalFIGRef.hide();
    this.figQuestionList = [];
    this.figqFile = null;
  }

  deleteFIG(index: number) {
    this.figQuestionList.splice(index, 1);
  }

  addNewFIG() {
    this.figQuestionList.push({
      Question: null,
      Answer: null,
      //Serial: null,
      Mark: this.FIGMark,
    });
  }

  onFIGFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid) {
      return;
    }

    for (let i = 0; i < this.figQuestionList.length; i++) {
      const element = this.figQuestionList[i];

      if (!element.Question) {
        this.toastr.error(
          "Please Enter Question for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      if (!element.Answer) {
        this.toastr.error(
          "Please enter answer for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      if (!element.Mark) {
        this.toastr.error(
          "Please enter mark for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }
    }

    if (!this.entryForm.value.id && !this.imageUrl) {
      this.toastr.warning("Please upload a cover photo", "Warning!");
      return;
    }

    if (
      this.entryForm.value.allowFor === "Trainee" &&
      this.allowedTrainees.length === 0
    ) {
      this.toastr.warning("Please select at least one trainee ", "Warning!");
      return;
    }

    this.blockUI.start("Saving...");
    const obj = {
      Id: this.entryForm.value.id ? this.entryForm.value.id : null,
      ExamName: this.entryForm.value.exam,
      ExamInstructions: this.entryForm.value.instructions
        ? this.entryForm.value.instructions.trim()
        : "",
      Quota: Number(this.entryForm.value.quota),
      Random: Number(this.entryForm.value.random),
      Publish: Number(this.entryForm.value.publish),
      ExamMCQNo: this.entryForm.value.no_of_mcq
        ? Number(this.entryForm.value.no_of_mcq)
        : 0,
      ExamTrueFalseNo: this.entryForm.value.no_of_tfq
        ? Number(this.entryForm.value.no_of_tfq)
        : 0,
      ExamFIGNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq
          ? Number(this.entryForm.value.no_of_figq)
          : 0,
      ExamMatchingNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq
          ? Number(this.entryForm.value.no_of_mq)
          : 0,
      ExamWritingNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq
          ? Number(this.entryForm.value.no_of_wq)
          : 0,
      CategoryId: this.entryForm.value.categoryId,
      MCQOnly: this.entryForm.value.mcqOnly,
      // MinPercentageForEvaluation: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,
      // MinMarksForEvaluation: Number(this.entryForm.value.minMarks),
      DurationMnt: Number(this.entryForm.value.duration),
      FIGQs: this.figQuestionList,
      QuesType: "FIG",
      StartDate: this.entryForm.value.startDate
        ? moment(this.entryForm.value.startDate).toISOString()
        : "",
      EndDate: this.entryForm.value.endDate
        ? moment(this.entryForm.value.endDate).toISOString()
        : "",
      AllowFor: this.entryForm.value.allowFor,
      DivisionId: this.entryForm.value.division,
      DepartmentId: this.entryForm.value.department,
      UnitId: this.entryForm.value.unit,
      Trainees: this.allowedTrainees.map((x) => x.Id),
      // Active:this.entryForm.value.active
    };

    const formdata = new FormData();
    formdata.append("Model", JSON.stringify(obj));
    if (this.imageFile) formdata.append("Image", this.imageFile);
    if (this.figqFile) {
      formdata.append("File", this.figqFile);
    }

    this._service.post("evaluation-exam/create-or-update", formdata).subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
        this.entryForm.controls["id"].setValue(res.Data);
        this.btnSaveText = "Update";
        this.id = res.Data;
        this.isEdit = true;
        this.geFIGIGList();
        this.modalFIGHide();
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
  }

  downloadSampleFIGQ() {
    return this._service
      .downloadFile("exam/download-sample-question", { quesType: "FIG" })
      .subscribe(
        (res) => {
          const url = window.URL.createObjectURL(res);
          var link = document.createElement("a");
          link.href = url;
          link.download = "evaluation_test_fill_in_the_gap_sample_file.xlsx";
          link.click();
        },
        (error) => {
          this.toastr.error(error.message || error, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  loadFIGQFile(files) {
    if (files.length === 0) return;
    this.figqFile = files[0];
  }

  resetFIGQFile(element) {
    element.value = "";
    this.figqFile = null;
  }

  // ============== Matching Question ====================
  getMatchingList() {
    const obj = {
      examId: this.id,
    };

    this.loadingMatching = true;
    this._service.get("evaluation-exam/get-matching-list/" + this.id).subscribe(
      (res) => {
        setTimeout(() => {
          this.loadingMatching = false;
        }, 1000);

        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.matchingList = res.Data;
      },
      (err) => {
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
        setTimeout(() => {
          this.loadingMatching = false;
        }, 1000);
      }
    );
  }

  openMatchingModal(template: TemplateRef<any>) {
    if (this.matchingList.length === 0) {
      this.btnMatchingSaveText = "Save";
      this.addNewMatching();
    } else {
      this.matchingList.forEach((element) => {
        this.matchingQuestionList.push({
          LeftSide: element.LeftSide,
          RightSide: element.RightSide,
          Mark: element.Mark,
        });
      });
    }
    this.modalMatchingRef = this.modalService.show(template, this.modalConfig);
  }

  modalMatchingHide() {
    this.modalMatchingRef.hide();
    this.matchingQuestionList = [];
    this.matchingqFile = null;
  }

  deleteMatching(index: number) {
    this.matchingQuestionList.splice(index, 1);
  }

  addNewMatching() {
    this.matchingQuestionList.push({
      LeftSide: null,
      RightSide: null,
      Mark: this.MatchingMark,
    });
  }

  onMatchingFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid) {
      return;
    }

    for (let i = 0; i < this.matchingQuestionList.length; i++) {
      const element = this.matchingQuestionList[i];

      if (!element.LeftSide) {
        this.toastr.error(
          "Please Enter Question for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      if (!element.RightSide) {
        this.toastr.error(
          "Please enter right hand side for left hand side: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      if (!element.Mark) {
        this.toastr.error(
          "Please enter mark for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }
    }

    if (!this.entryForm.value.id && !this.imageUrl) {
      this.toastr.warning("Please upload a cover photo", "Warning!");
      return;
    }

    if (
      this.entryForm.value.allowFor === "Trainee" &&
      this.allowedTrainees.length === 0
    ) {
      this.toastr.warning("Please select at least one trainee ", "Warning!");
      return;
    }

    this.blockUI.start("Saving...");
    const obj = {
      Id: this.entryForm.value.id ? this.entryForm.value.id : null,
      ExamName: this.entryForm.value.exam,
      ExamInstructions: this.entryForm.value.instructions
        ? this.entryForm.value.instructions.trim()
        : "",
      Quota: Number(this.entryForm.value.quota),
      Random: Number(this.entryForm.value.random),
      Publish: Number(this.entryForm.value.publish),
      ExamMCQNo: this.entryForm.value.no_of_mcq
        ? Number(this.entryForm.value.no_of_mcq)
        : 0,
      ExamTrueFalseNo: this.entryForm.value.no_of_tfq
        ? Number(this.entryForm.value.no_of_tfq)
        : 0,
      ExamFIGNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq
          ? Number(this.entryForm.value.no_of_figq)
          : 0,
      ExamMatchingNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq
          ? Number(this.entryForm.value.no_of_mq)
          : 0,
      ExamWritingNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq
          ? Number(this.entryForm.value.no_of_wq)
          : 0,
      CategoryId: this.entryForm.value.categoryId,
      MCQOnly: this.entryForm.value.mcqOnly,
      // MinPercentageForEvaluation: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,
      // MinMarksForEvaluation: Number(this.entryForm.value.minMarks),
      DurationMnt: Number(this.entryForm.value.duration),
      MatchingQs: this.matchingQuestionList,
      QuesType: "Matching",
      StartDate: this.entryForm.value.startDate
        ? moment(this.entryForm.value.startDate).toISOString()
        : "",
      EndDate: this.entryForm.value.endDate
        ? moment(this.entryForm.value.endDate).toISOString()
        : "",
      AllowFor: this.entryForm.value.allowFor,
      DivisionId: this.entryForm.value.division,
      DepartmentId: this.entryForm.value.department,
      UnitId: this.entryForm.value.unit,
      Trainees: this.allowedTrainees.map((x) => x.Id),
    };

    const formdata = new FormData();
    formdata.append("Model", JSON.stringify(obj));
    if (this.imageFile) formdata.append("Image", this.imageFile);
    if (this.matchingqFile) {
      formdata.append("File", this.matchingqFile);
    }

    this._service.post("evaluation-exam/create-or-update", formdata).subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
        this.entryForm.controls["id"].setValue(res.Data);
        this.btnSaveText = "Update";
        this.id = res.Data;
        this.isEdit = true;
        this.getMatchingList();
        this.modalMatchingHide();
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
  }

  downloadSampleMatchingQ() {
    return this._service
      .downloadFile("exam/download-sample-question", { quesType: "Matching" })
      .subscribe(
        (res) => {
          const url = window.URL.createObjectURL(res);
          var link = document.createElement("a");
          link.href = url;
          link.download =
            "evaluation_test_left_right_matching_sample_file.xlsx";
          link.click();
        },
        (error) => {
          this.toastr.error(error.message || error, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  loadMatchingQFile(files) {
    if (files.length === 0) return;
    this.matchingqFile = files[0];
  }

  resetMatchingQFile(element) {
    element.value = "";
    this.matchingqFile = null;
  }

  // ============== Written Question ====================
  geWrittenList() {
    this.loadingWritten = true;
    this._service.get("evaluation-exam/get-written-list/" + this.id).subscribe(
      (res) => {
        setTimeout(() => {
          this.loadingWritten = false;
        }, 1000);

        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.writtenList = res.Data;
      },
      (err) => {
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
        setTimeout(() => {
          this.loadingWritten = false;
        }, 1000);
      }
    );
  }

  openWrittenModal(template: TemplateRef<any>) {
    if (this.writtenList.length === 0) {
      this.btnWrittenSaveText = "Save";
      this.addNewWritten();
    } else {
      this.writtenList.forEach((element) => {
        this.writtenQuestionList.push({
          Question: element.Question,
          Mark: element.Mark,
        });
      });
    }
    this.modalWrittenRef = this.modalService.show(template, this.modalConfig);
  }

  modalWrittenHide() {
    this.modalWrittenRef.hide();
    this.writtenQuestionList = [];
    this.wqFile = null;
  }

  deleteWritten(index: number) {
    this.writtenQuestionList.splice(index, 1);
  }

  addNewWritten() {
    this.writtenQuestionList.push({
      Question: null,
      Mark: this.WrittenMark,
    });
  }

  onWrittenFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid) {
      return;
    }

    for (let i = 0; i < this.writtenQuestionList.length; i++) {
      const element = this.writtenQuestionList[i];

      if (!element.Question) {
        this.toastr.error(
          "Please Enter Question for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      if (!element.Mark) {
        this.toastr.error(
          "Please enter mark for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }
    }

    if (!this.entryForm.value.id && !this.imageUrl) {
      this.toastr.warning("Please upload a cover photo", "Warning!");
      return;
    }

    if (
      this.entryForm.value.allowFor === "Trainee" &&
      this.allowedTrainees.length === 0
    ) {
      this.toastr.warning("Please select at least one trainee ", "Warning!");
      return;
    }

    this.blockUI.start("Saving...");
    const obj = {
      Id: this.entryForm.value.id ? this.entryForm.value.id : null,
      ExamName: this.entryForm.value.exam,
      ExamInstructions: this.entryForm.value.instructions
        ? this.entryForm.value.instructions.trim()
        : "",
      Quota: Number(this.entryForm.value.quota),
      Random: Number(this.entryForm.value.random),
      Publish: Number(this.entryForm.value.publish),
      ExamMCQNo: this.entryForm.value.no_of_mcq
        ? Number(this.entryForm.value.no_of_mcq)
        : 0,
      ExamTrueFalseNo: this.entryForm.value.no_of_tfq
        ? Number(this.entryForm.value.no_of_tfq)
        : 0,
      ExamFIGNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq
          ? Number(this.entryForm.value.no_of_figq)
          : 0,
      ExamMatchingNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq
          ? Number(this.entryForm.value.no_of_mq)
          : 0,
      ExamWritingNo:
        !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq
          ? Number(this.entryForm.value.no_of_wq)
          : 0,
      CategoryId: this.entryForm.value.categoryId,
      MCQOnly: this.entryForm.value.mcqOnly,
      // MinPercentageForEvaluation: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,
      // MinMarksForEvaluation: Number(this.entryForm.value.minMarks),
      DurationMnt: Number(this.entryForm.value.duration),
      WrittenQs: this.writtenQuestionList,
      QuesType: "Written",
      StartDate: this.entryForm.value.startDate
        ? moment(this.entryForm.value.startDate).toISOString()
        : "",
      EndDate: this.entryForm.value.endDate
        ? moment(this.entryForm.value.endDate).toISOString()
        : "",
      AllowFor: this.entryForm.value.allowFor,
      DivisionId: this.entryForm.value.division,
      DepartmentId: this.entryForm.value.department,
      UnitId: this.entryForm.value.unit,
      Trainees: this.allowedTrainees.map((x) => x.Id),
    };

    const formdata = new FormData();
    formdata.append("Model", JSON.stringify(obj));
    if (this.imageFile) formdata.append("Image", this.imageFile);
    if (this.wqFile) {
      formdata.append("File", this.wqFile);
    }

    this._service.post("evaluation-exam/create-or-update", formdata).subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
        this.entryForm.controls["id"].setValue(res.Data);
        this.btnSaveText = "Update";
        this.id = res.Data;
        this.isEdit = true;
        this.geWrittenList();
        this.modalWrittenHide();
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
  }

  downloadSampleWQ() {
    return this._service
      .downloadFile("exam/download-sample-question", { quesType: "Written" })
      .subscribe(
        (res) => {
          const url = window.URL.createObjectURL(res);
          var link = document.createElement("a");
          link.href = url;
          link.download = "evaluation_test_written_question_sample_file.xlsx";
          link.click();
        },
        (error) => {
          this.toastr.error(error.message || error, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  loadWQFile(files) {
    if (files.length === 0) return;
    this.wqFile = files[0];
  }

  resetWQFile(element) {
    element.value = "";
    this.wqFile = null;
  }

  modalHide() {
    this.modalRef.hide();
    this.submitted = false;
  }

  openModal(template: TemplateRef<any>) {
    // this.entryForm.controls['isActive'].setValue(true);
    this.modalRef = this.modalService.show(template, this.modalConfig);
  }

  changeAllowedFor(event) {
    if (event === "Division") {
      if (this.divisionList.length <= 0) {
        this.getDivisionList();
      }
      this.divisionSelected = true;
      this.departmentSelected = false;
      this.unitSelected = false;
      this.traineeSelected = false;
      return;
    }
    if (event === "Department") {
      if (this.departmentList.length <= 0) {
        this.getDepartmentList();
      }
      this.divisionSelected = false;
      this.departmentSelected = true;
      this.unitSelected = false;
      this.traineeSelected = false;
      return;
    }
    if (event === "Unit") {
      if (this.unitList.length <= 0) {
        this.getUnitList();
      }
      this.divisionSelected = false;
      this.departmentSelected = false;
      this.unitSelected = true;
      this.traineeSelected = false;
      return;
    }
    if (event === "Trainee") {
      // if (this.traineeList.length <= 0) {
      //   this.getTraineelList();
      // }
      this.divisionSelected = false;
      this.departmentSelected = false;
      this.unitSelected = false;
      this.traineeSelected = true;
      return;
    }
    if (event === "All") {
      this.divisionSelected = false;
      this.departmentSelected = false;
      this.unitSelected = false;
      this.traineeSelected = false;
      return;
    }
  }

  editChangeAllowedFor(event, divisionId, departmentId, unitId, trainees) {
    if (event === "Division") {
      if (this.divisionList.length <= 0) {
        this.getDivisionList();
      }
      this.entryForm.controls["division"].setValue(divisionId);
      this.divisionSelected = true;
      this.departmentSelected = false;
      this.unitSelected = false;
      this.traineeSelected = false;
      return;
    }
    if (event === "Department") {
      if (this.departmentList.length <= 0) {
        this.getDepartmentList();
      }
      this.entryForm.controls["department"].setValue(departmentId);
      this.divisionSelected = false;
      this.departmentSelected = true;
      this.unitSelected = false;
      this.traineeSelected = false;
      return;
    }
    if (event === "Unit") {
      if (this.unitList.length <= 0) {
        this.getUnitList();
      }
      this.entryForm.controls["unit"].setValue(unitId);
      this.divisionSelected = false;
      this.departmentSelected = false;
      this.unitSelected = true;
      this.traineeSelected = false;
      return;
    }
    if (event === "Trainee") {
      this.allowedTrainees = trainees;
      this.divisionSelected = false;
      this.departmentSelected = false;
      this.unitSelected = false;
      this.traineeSelected = true;
      return;
    }
    if (event === "All") {
      this.divisionSelected = false;
      this.departmentSelected = false;
      this.unitSelected = false;
      this.traineeSelected = false;
      return;
    }
  }

  getDivisionList() {
    this._service.get("division/dropdown-list").subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.divisionList = res.Data;
      },
      (err) => {
        this.toastr.warning(err.message || err, "warning!", {
          closeButton: true,
          disableTimeOut: false,
        });
      }
    );
  }

  getDepartmentList() {
    this._service.get("department/dropdown-list").subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.departmentList = res.Data;
      },
      (err) => {
        this.toastr.warning(err.message || err, "warning!", {
          closeButton: true,
          disableTimeOut: false,
        });
      }
    );
  }

  getUnitList() {
    this._service.get("unit/dropdown-list").subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.unitList = res.Data;
      },
      (err) => {
        this.toastr.warning(err.message || err, "warning!", {
          closeButton: true,
          disableTimeOut: false,
        });
      }
    );
  }

  selectAll(event) {
    this.traineeList.forEach((element) => {
      element.selected = event.target.checked;
    });

    if (event.target.checked) {
      this.traineeList.forEach((elem) => {
        if (
          elem.selected &&
          !this.selectedTraineeList.find((x) => x.Id === elem.Id)
        )
          this.selectedTraineeList.push(elem);
      });
    } else {
      this.traineeList.forEach((element) => {
        element.selected = event.target.checked;
        if (!element.selected) {
          this.selectedTraineeList = this.selectedTraineeList.filter(
            (obj) => obj !== element
          );
        }
      });
    }
  }

  getTraineelList(name) {
    this.select_All = false;
    if (!name) return;
    this._service.get("trainee/query/10/" + name).subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.traineeList = res.Data;
        let arr = this.selectedTraineeList.map((x) => x.Id);
        this.traineeList.forEach((element) => {
          element.selected = arr.indexOf(element.Id) !== -1;
        });
      },
      () => {}
    );
  }

  onChangeTraineeSelection(item) {
    if (item.Selected)
      this.selectedTraineeList = this.selectedTraineeList.filter(
        (obj) => obj.Id !== item.Id
      );
    else this.selectedTraineeList.push(item);
  }

  removeSelected(item) {
    this.selectedTraineeList = this.selectedTraineeList.filter(
      (obj) => obj !== item
    );
  }

  clearLists() {
    this.divisionList = [];
    this.departmentList = [];
    this.unitList = [];
    this.traineeList = [];
    this.selectedTraineeList = [];
    this.divisionSelected = false;
    this.departmentSelected = false;
    this.unitSelected = false;
    this.traineeSelected = false;
    this.select_All = false;
  }

  openTraineeModal(template: TemplateRef<any>) {
    this.selectedTraineeList = this.allowedTrainees;
    this.modalTraineeRef = this.modalService.show(template, this.modalxLConfig);
  }

  modalTraineeHide() {
    this.selectedTraineeList = [];
    this.traineeList = [];
    this.traineeForm.reset();
    this.modalTraineeRef.hide();
  }

  onTraineeSelectionComplete() {
    this.allowedTrainees = this.selectedTraineeList;
    this.modalTraineeHide();
  }

  readURL(input) {
    if (!input || input.files.length === 0) {
      this.imageUrl = null;
      this.imageFile = null;
      return;
    }

    var mimeType = input.files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      this.toastr.warning("Only images are supported", "Warning!!");
      return;
    }

    var reader = new FileReader();
    this.imageFile = input.files[0];
    reader.readAsDataURL(input.files[0]);
    reader.onload = (_event) => {
      this.imageUrl = reader.result;
    };
  }

  loadAttachment(files) {
    if (files.length === 0) return;
    this.traineeForm.controls["file"].setValue(files[0]);
  }

  getTraineesByExcel() {
    if (!this.traineeForm.value.file) {
      this.toastr.warning("No excel file chosen", "WARNING!");
      return;
    }

    const formData = new FormData();
    formData.append("File", this.traineeForm.value.file);

    this.blockUI.start("Getting data. Please wait...");
    this._service.post("trainee/query-by-excel", formData).subscribe({
      next: (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        if (res.Message)
          this.toastr.warning("PINs not found: " + res.Message, "WARNING!");

        this.traineeList = res.Data;
        let arr = this.selectedTraineeList.map((x) => x.Id);
        this.traineeList.forEach((element) => {
          element.selected = arr.indexOf(element.Id) !== -1;
        });
      },
      error: (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      },
      complete: () => this.blockUI.stop(),
    });
  }

  downloadSampleFile() {
    return this._service
      .downloadFile("course/download-course-enrollment-sample-file")
      .subscribe({
        next: (res) => {
          const url = window.URL.createObjectURL(res);
          var link = document.createElement("a");
          link.href = url;
          link.download = "trainee_sample_file.xlsx";
          link.click();
        },
        error: (error) => {
          this.toastr.error(error.message || error, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        },
        complete: () => {},
      });
  }
}
