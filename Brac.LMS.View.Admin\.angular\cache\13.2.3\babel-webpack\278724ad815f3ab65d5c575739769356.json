{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { TimeWiseCourseStudyReportRoutingModule } from './time-wise-course-study-report-routing.module';\nimport { PdfJsViewerModule } from 'ng2-pdfjs-viewer';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';\nimport * as i0 from \"@angular/core\";\nexport let TimeWiseCourseStudyReportModule = /*#__PURE__*/(() => {\n  class TimeWiseCourseStudyReportModule {}\n\n  TimeWiseCourseStudyReportModule.ɵfac = function TimeWiseCourseStudyReportModule_Factory(t) {\n    return new (t || TimeWiseCourseStudyReportModule)();\n  };\n\n  TimeWiseCourseStudyReportModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TimeWiseCourseStudyReportModule\n  });\n  TimeWiseCourseStudyReportModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, TimeWiseCourseStudyReportRoutingModule, SharedModule, PdfJsViewerModule, NgxExtendedPdfViewerModule]]\n  });\n  return TimeWiseCourseStudyReportModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}