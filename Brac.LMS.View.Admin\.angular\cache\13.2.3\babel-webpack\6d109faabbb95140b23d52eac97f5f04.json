{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AdminViewComponent } from './dashboard-admin.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: AdminViewComponent\n}];\nexport let AdminViewRoutingModule = /*#__PURE__*/(() => {\n  class AdminViewRoutingModule {}\n\n  AdminViewRoutingModule.ɵfac = function AdminViewRoutingModule_Factory(t) {\n    return new (t || AdminViewRoutingModule)();\n  };\n\n  AdminViewRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AdminViewRoutingModule\n  });\n  AdminViewRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return AdminViewRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}