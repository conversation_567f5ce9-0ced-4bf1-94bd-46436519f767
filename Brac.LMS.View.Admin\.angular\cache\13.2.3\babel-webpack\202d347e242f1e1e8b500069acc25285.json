{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { BelatedTraineeNotifyComponent } from './belated-trainee-notify.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: BelatedTraineeNotifyComponent\n}];\nexport let BelatedTraineeNotifyRoutingModule = /*#__PURE__*/(() => {\n  class BelatedTraineeNotifyRoutingModule {}\n\n  BelatedTraineeNotifyRoutingModule.ɵfac = function BelatedTraineeNotifyRoutingModule_Factory(t) {\n    return new (t || BelatedTraineeNotifyRoutingModule)();\n  };\n\n  BelatedTraineeNotifyRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: BelatedTraineeNotifyRoutingModule\n  });\n  BelatedTraineeNotifyRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return BelatedTraineeNotifyRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}