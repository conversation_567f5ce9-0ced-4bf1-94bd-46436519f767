<block-ui>
  <div class="row">

    <div class="col-lg-12">
      <div class="row">
        <div class="col-lg-12">
          <div class="card card-border-primary">
            <div class="card-block ">
              <div class="row ">
                <div class="col-sm-12">
                  <form [formGroup]="filterForm" autocomplete="off">
                    <div class="mb-3 row">
                      <label class="col-sm-1 col-form-label col-form-label-sm text-right required"> Course
                      </label>
                      <div class="col-sm-3">
                        <ng-select *ngIf="courseList.length > 0" formControlName="courseId"
                          class="form-control form-control-sm" [clearable]="false" [clearOnBackspace]="false"
                          [items]="courseList" (change)="onChangeCourse($event)" bindLabel="Title" bindValue="Id"
                          placeholder="Select a course">
                        </ng-select>
                        <div *ngIf="submitted && g.courseId.errors" class="error-text">
                          <span *ngIf="g.courseId.errors.required" class="text-danger">Course is
                            required</span>
                        </div>
                      </div>

                      <label class="col-sm-1 col-form-label col-form-label-sm text-right required"> Exam
                      </label>
                      <div class="col-sm-2">
                        <ng-select formControlName="examId" class="form-control form-control-sm"
                          [ngClass]="{ 'is-invalid': submitted && g.examId.errors }" [clearable]="false"
                          [clearOnBackspace]="false" [items]="examList" (change)="onChangeExam($event)" bindLabel="Name"
                          bindValue="Id" placeholder="Select">
                        </ng-select>
                        <div *ngIf="submitted && g.examId.errors" class="error-text">
                          <span *ngIf="g.examId.errors.required" class="text-danger">Exam is
                            required</span>
                        </div>
                      </div>


                      <div class="col-sm-5 col-12" *ngIf="g.examId.value">
                        <label class="col-form-label col-form-label-sm text-right "> Due Date of the Exam : <b>{{dueDate
                            |
                            amDateFormat: 'DD-MMM-YYYY hh:mm A' }}</b>
                        </label>

                        <!-- <button class="btn btn-theme me-2" (click)="filterList()"><i class="feather icon-search"></i> Search
                        </button> -->
                        <!-- <label>
                          <input type="checkbox"  >
                          <span class="cr">&nbsp;</span>

                        </label> -->
                      </div>
                    </div>

                  </form>
                </div>
                <div class="col-md-8 mb-2 col-xs-12" *ngIf="selected_count>0">
                  <button class="btn btn-sm btn-outline-secondary" (click)="openModal(template)"><i
                      class="feather icon-plus "></i> Notify Belated trainee(s)</button>
                </div>
                <div class="col-lg-12 ">
                  <ngx-datatable class="material table-bordered" [rows]="rows" [loadingIndicator]="loadingIndicator"
                    [columnMode]="ColumnMode.force" [headerHeight]="40" [footerHeight]="50" rowHeight="auto"
                    [limit]="10">


                    <!-- <ngx-datatable-column [width]="50" name="Trainee No" prop="TraineeNo" [draggable]="false"
                      [sortable]="false">
                      <ng-template let-value="value" ngx-datatable-cell-template>
                        <span title="{{ value }}"> {{ value }} </span>
                      </ng-template>
                    </ngx-datatable-column> -->

                    <ngx-datatable-column [width]="100" name="Trainee Name" prop="Name" [draggable]="false"
                      [sortable]="false">
                      <ng-template let-value="value" ngx-datatable-cell-template>
                        <span title="{{ value }}"> {{ value }} </span>
                      </ng-template>
                    </ngx-datatable-column>

                    <ngx-datatable-column [width]="60" prop="Division.Name" [draggable]="false" [sortable]="false">
                      <ng-template let-value="value" ngx-datatable-cell-template>
                        <span title="{{ value }}"> {{ value }} </span>
                      </ng-template>
                    </ngx-datatable-column>

                    <ngx-datatable-column [width]="100" prop="Email" [draggable]="false" [sortable]="false">
                      <ng-template let-value="value" ngx-datatable-cell-template>
                        <span title="{{value}}">{{ value }} </span>
                      </ng-template>
                    </ngx-datatable-column>

                    <ngx-datatable-column [width]="100" prop="Id" name="invite" [draggable]="false" [sortable]="false">
                      <ng-template ngx-datatable-header-template>
                        <label>
                          <input type="checkbox" *ngIf="rows.length>0" [checked]="allRowsSelected"
                            (change)="selectAll($event.currentTarget.checked)" />
                          <span class="cr"> Select All &nbsp;</span>

                        </label>

                      </ng-template>
                      <ng-template ngx-datatable-cell-template let-value="value" let-row="row">
                        <input type="checkbox" [checked]="isSelected"
                          (change)="setSelectedTrainee(row.Id,$event.currentTarget.checked)" />
                      </ng-template>
                      <!-- <ng-template let-row="row">

                        <label>
                          <input type="checkbox"  (change)="setSelectedTrainee(row.Id,$event.currentTarget.checked)">
                          <span class="cr">&nbsp;</span>

                        </label>

                      </ng-template> -->
                    </ngx-datatable-column>


                  </ngx-datatable>
                </div>


              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>

  <ng-template #template>
    <div class="modal-header ">
      <h4 class="modal-title float-start " [innerHtml]="modalTitle" id="modalTitle"></h4>
      <button type="button " class="close float-end btn btn-mini btn-danger btn btn-mini btn-outline-danger"
        aria-label="Close " (click)="modalHide()">
        <i class="feather icon-x"></i>
      </button>
    </div>
    <div class="modal-body ">

      <form [formGroup]="entryForm" autocomplete="off">

        <div class="row">
          <div class="col-lg-12">
            <div class="card">
              <div class="card-block">
                <div class="mb-3  col-12">
                  <label class="col-form-label col-form-label-sm required">Start Date & Time </label>
                  <input [owlDateTime]="startDate" [owlDateTimeTrigger]="startDate" [max]="f.endDate.value"
                    placeholder="Set Start Date & Time" class="form-control form-control-sm"
                    formControlName="startDate">
                  <owl-date-time #startDate></owl-date-time>
                  <div *ngIf="submitted && f.startDate.errors" class="error-text">
                    <span *ngIf="f.startDate.errors.required" class="text-danger"> Start date &
                      time is
                      required</span>
                  </div>
                </div>

                <div class="mb-3  col-12">
                  <label class="col-form-label col-form-label-sm required">End Date & Time </label>
                  <input [owlDateTime]="endDate" [owlDateTimeTrigger]="endDate" [min]="f.startDate.value"
                    placeholder="Set End Date & Time" class="form-control form-control-sm" formControlName="endDate">
                  <owl-date-time #endDate></owl-date-time>

                  <div *ngIf="submitted && f.endDate.errors" class="error-text">
                    <span *ngIf="f.endDate.errors.required" class="text-danger"> End date & time
                      is
                      required</span>
                  </div>
                </div>

                <!-- <label class=" col-form-label col-form-label-sm required"><b>From</b> </label>
                <div class="mb-3 row">
                  <div class="col-sm-6">
                    <label> Date </label>
                    <input type="text" class="form-control form-control-sm"
                      [ngClass]="{ 'is-invalid': submitted && f.startDate.errors }" bsDatepicker [bsConfig]="bsConfig"
                      formControlName="startDate" readonly placement="right" placeholder="Select a date ">
                    <div *ngIf="submitted && f.startDate.errors" class="error-text">
                      <span *ngIf="f.startDate.errors.required" class="text-danger">
                        Date range
                        is
                        required</span>

                    </div>
                  </div>
                  <div class="col-sm-6">

                    <label>Time </label>
                    <input type="text" aceholder="Select a Time " class="form-control form-control-sm" readonly
                      [ngxTimepicker]="picker1" formControlName="startTime">
                    <ngx-material-timepicker #picker1></ngx-material-timepicker>
                  </div>



                </div>
                <label class=" col-form-label col-form-label-sm required"><b>To</b> </label>
                <div class="mb-3 row">

                  <div class="col-sm-6">
                    <label> Date </label>
                    <input type="text" class="form-control form-control-sm" [ngClass]="{ 'is-invalid': submitted && f.endDate.errors }"
                      bsDatepicker [bsConfig]="bsConfig" formControlName="endDate" readonly placement="right"
                      placeholder="Select a date ">

                    <div *ngIf="submitted && f.endDate.errors" class="error-text">
                      <span *ngIf="f.endDate.errors.required" class="text-danger">
                        Date range
                        is
                        required</span>
                    </div>
                  </div>
                  <div class="col-sm-6">
                    <label>Time </label>
                    <input type="text" aceholder="Select a Time " [ngxTimepicker]="picker2" readonly
                      class="form-control form-control-sm" formControlName="endTime">
                    <ngx-material-timepicker #picker2></ngx-material-timepicker>

                  </div>


                </div>
 -->


              </div>
            </div>
          </div>



        </div>



      </form>

    </div>
    <div class="modal-footer">
      <div class="pe-4">

        <button class="btn btn-outline-danger me-2" (click)="modalHide()"> <i class="feather icon-x"></i> Close</button>

        <button class="btn btn-outline-success" (click)="sendEmail()"><i class="feather icon-check-circle"></i> Save and
          Notify</button>


      </div>

    </div>
  </ng-template>
</block-ui>