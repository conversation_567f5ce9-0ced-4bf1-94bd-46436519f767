{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { EvaluationTestEntryComponent } from './evaluation-test-entry.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: EvaluationTestEntryComponent\n}];\nexport let EvaluationTestEntryRoutingModule = /*#__PURE__*/(() => {\n  class EvaluationTestEntryRoutingModule {}\n\n  EvaluationTestEntryRoutingModule.ɵfac = function EvaluationTestEntryRoutingModule_Factory(t) {\n    return new (t || EvaluationTestEntryRoutingModule)();\n  };\n\n  EvaluationTestEntryRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: EvaluationTestEntryRoutingModule\n  });\n  EvaluationTestEntryRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return EvaluationTestEntryRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}