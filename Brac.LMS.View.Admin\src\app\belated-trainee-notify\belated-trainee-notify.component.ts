import {
  Component,
  TemplateRef,
  ViewEncapsulation,
  OnInit,
  Pipe,
  PipeTransform,
} from "@angular/core";
import { BsModalService, BsModalRef } from "ngx-bootstrap/modal";
import { ColumnMode } from "@swimlane/ngx-datatable";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { CommonService } from "../_services/common.service";
import { ToastrService } from "ngx-toastr";
import { BlockUI, NgBlockUI } from "ng-block-ui";
import { environment } from "../../environments/environment";
import { UploadDialogComponent } from "../_helpers/upload-dialog/dialog.component";
import { MatDialog } from "@angular/material/dialog";
import {
  BsDatepickerConfig,
  BsDaterangepickerConfig,
} from "ngx-bootstrap/datepicker";
import * as moment from "moment";
import { ConfirmService } from "../_helpers/confirm-dialog/confirm.service";
import { Page } from "../_models/page";
import { ResponseStatus } from "../_models/enum";
import { NgbTimepickerConfig } from "@ng-bootstrap/ng-bootstrap";
import { trigger, transition, style, animate } from "@angular/animations";

@Component({
  selector: "app-belated-trainee-notify",
  styleUrls: ["./belated-trainee-notify.component.scss"],
  templateUrl: "./belated-trainee-notify.component.html",
  encapsulation: ViewEncapsulation.None,
  animations: [
    trigger("inOutAnimation", [
      transition(":enter", [
        style({ height: 0, opacity: 0 }),
        animate("1s ease-out", style({ height: 300, opacity: 1 })),
      ]),
      transition(":leave", [
        animate("1s ease-out", style({ height: 0, opacity: 0 })),
      ]),
    ]),
  ],
})
export class BelatedTraineeNotifyComponent implements OnInit {
  entryForm: FormGroup;
  filterForm: FormGroup;
  submitted = false;
  filterSubmitted = false;
  isSelected = false;
  allRowsSelected = false;
  @BlockUI() blockUI: NgBlockUI;
  modalTitle = "Extend Time";
  btnSaveText = "Save";

  to_Show = false;

  modalConfig: any = { class: "gray modal-md", backdrop: "static" };
  modalRef: BsModalRef;

  searchText: string = "";
  selected_count: number = 0;
  selected_items: Array<any> = [];

  courseList: Array<any> = [];
  divisionList: Array<any> = [];
  traineeList: Array<any> = [];
  examList: Array<any> = [];
  rows = [];
  loadingIndicator = false;
  ColumnMode = ColumnMode;
  page = new Page();
  scrollBarHorizontal = window.innerWidth < 1200;

  itemObj: any = {};
  timeStamp;
  dueDate = "";
  //imgBaseUrl = environment.imageUrl;
  baseUrl = environment.baseUrl;
  bsConfig: Partial<BsDatepickerConfig>;

  bsValue: Date[] = [];

  constructor(
    private modalService: BsModalService,
    public formBuilder: FormBuilder,
    private _service: CommonService,
    private confirmService: ConfirmService,
    private toastr: ToastrService,
    config: NgbTimepickerConfig,
    private dialog: MatDialog
  ) {
    this.page.pageNumber = 0;
    this.page.size = 5;
    window.onresize = () => {
      this.scrollBarHorizontal = window.innerWidth < 1200;
    };
    this.bsConfig = Object.assign({}, { containerClass: "theme-blue" });
    config.seconds = false;
    config.spinners = false;
  }

  ngOnInit() {
    this.entryForm = this.formBuilder.group({
      startDate: [null, [Validators.required]],
      endDate: [null, [Validators.required]],
      // startTime: [null, [Validators.required]],
      // endTime: [null, [Validators.required]],
    });

    this.filterForm = this.formBuilder.group({
      courseId: [null, [Validators.required]],
      examId: [null, [Validators.required]],
    });

    this.bsConfig = Object.assign(
      {},
      { minDate: new Date(), containerClass: "theme-blue" }
    );
    this.getCourseList();
  }

  setPage(pageInfo) {
    this.page.pageNumber = pageInfo.offset;
  }
  get f() {
    return this.entryForm.controls;
  }

  get g() {
    return this.filterForm.controls;
  }

  getCourseList() {
    this._service.get("course/dropdown-list").subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.courseList = res.Data;
      },
      () => {}
    );
  }

  onFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid) {
      return;
    }

    if (this.selected_count === 0) {
      this.toastr.warning("Please selecta minimum one trainee", "Warning!", {
        timeOut: 2000,
      });
      return;
    }

    this.blockUI.start("Saving...");

    const obj = {
      CourseId: this.entryForm.value.courseId,
      Trainees: this.selected_items.map(function (x) {
        return x.Id;
      }),
    };

    // const formData = new FormData();
    // formData.append('Model', JSON.stringify(obj));

    this._service.post("course/enroll-trainee", obj).subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
        this.modalHide();
        // this.filterList();
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
  }

  modalHide() {
    this.modalRef.hide();
    this.submitted = false;
  }

  openModal(template: TemplateRef<any>) {
    // this.entryForm.controls['isActive'].setValue(true);
    this.modalRef = this.modalService.show(template, this.modalConfig);
  }

  sendEmail() {
    this.submitted = true;
    //this.entryForm.value.startDate.setTime(this.entryForm.value.startTime);
    //this.entryForm.value.endDate.setTime(this.entryForm.value.endTime);
    if (this.entryForm.invalid) {
      return;
    }

    const obj = {
      ExamId: this.filterForm.value.examId,
      Trainees: this.rows
        .filter((y) => y.Selected === true)
        .map(function (x) {
          return x.Id;
        }),
      startDate: moment(this.entryForm.value.startDate).toISOString(),
      endDate: moment(this.entryForm.value.endDate).toISOString(),
    };
    this.modalHide();
    this.confirmService
      .confirm(
        "Are you sure?",
        "By confirming you will notify all the selected trainees and reschedule the exam time for the notified trainees."
      )
      .subscribe((result) => {
        if (result) {
          this.blockUI.start("Notifying Belated Trainees...");
          this._service.post("course/notify-belated-trainees", obj).subscribe(
            (res) => {
              this.blockUI.stop();
              if (res.Status === ResponseStatus.Warning) {
                this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
                return;
              } else if (res.Status === ResponseStatus.Error) {
                this.toastr.error(res.Message, "Error!", {
                  closeButton: true,
                  disableTimeOut: false,
                  enableHtml: true,
                });
                return;
              }

              this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
              this.modalHide();
              this.filterForm.reset();
              //this.traineeList=[];
              this.rows = [];
              this.entryForm.reset();
              this.selected_count = 0;
              (this.isSelected = false), (this.submitted = false);
            },
            (err) => {
              this.blockUI.stop();
              this.toastr.warning(err.Message || err, "Warning!", {
                timeOut: 2000,
              });
            }
          );
        } else {
          this.blockUI.stop();
        }
      });
    // const formData = new FormData();
    // formData.append('Model', JSON.stringify(obj));
  }

  public openUploadDialog() {
    let dialogRef = this.dialog
      .open(UploadDialogComponent, {
        data: {
          url: this._service.generateUrl("Course/UploadCourseAssign"),
          whiteList: ["xlsx", "xls"],
          uploadtext: "Please upload an Excel file",
          title: "Upload trainee Course Assign File",
        },
        width: "50%",
        height: "50%",
      })
      .afterClosed()
      .subscribe((response) => {
        if (response) {
          this.toastr.success(response, "Success!", { timeOut: 2000 });
          this.filterList();
        }
      });
  }

  filterList() {
    this.submitted = true;

    if (this.filterForm.invalid) return;
    this.loadingIndicator = true;
    const obj = {
      courseId: this.filterForm.value.courseId,
      examId: this.filterForm.value.examId,
    };
    this._service.get("course/get-all-trainees-to-notify", obj).subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.rows = res.Data.Records;

        this.rows = this.rows.filter(
          (x) =>
            x.takenExam === false && x.Enrolled === true && x.isBelated === true
        );
        this.traineeList = [];
        this.rows.forEach((s) => {
          s.Selected = false;
        });
        this.submitted = false;
        setTimeout(() => {
          this.loadingIndicator = false;
        }, 1000);
      },
      () => {}
    );
  }
  setSelectedTrainee(id, selected) {
    this.rows.forEach((s) => {
      if (id.indexOf(s.Id) >= 0) {
        s.Selected = selected;
        if (selected) this.selected_count++;
        else this.selected_count--;
      }
    });
  }
  selectAll(value) {
    if (value) {
      this.rows.forEach((s) => {
        s.Selected = true;
        this.selected_count++;
      });
      this.isSelected = true;
    } else {
      this.rows.forEach((s) => {
        s.Selected = false;
        this.selected_count--;
      });
      this.isSelected = false;
    }
  }
  onChangeCourse(event) {
    this.examList = [];
    this.filterForm.controls["examId"].setValue(null);
    this._service.get("exam/certificate/dropdown-list/" + event.Id).subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.examList = res.Data;
      },
      () => {}
    );
  }
  onChangeExam(event) {
    //this.traineeList=[];
    this.selected_count = 0;
    this.rows = [];
    this.filterList();
    this.dueDate = event.Date ? event.Date : "no ending date";
  }
  onTimechange(event) {
    if (this.f.startDate.value == event) {
      console.log("yes");
    }
    if (this.f.endDate.value == event) {
      console.log("yes");
    }
  }
}
