<block-ui>
  <div class="row">
    <div class="col-lg-12">
      <div class="card card-border-primary">
        <div class="card-header">
          <h5>Evaluation Test List</h5>

          <button class="btn btn-theme btn-sm float-end" [routerLink]="['/evaluation-test-entry']">
            <i class="feather icon-plus"></i>
            Create Evaluation Test
          </button>
        </div>
        <div class="card-block">
          <div class="row">
            <form [formGroup]="filterForm" autocomplete="off" class="col-12">
              <div class="row">
                <div class="col-lg-4 col-12 mb-3">
                  <label class="visually-hidden" for="categoryId">Category</label>
                  <ng-select #selectElement (click)="handleSelectClick(selectElement)"
                    class="form-control form-control-sm" [clearable]="true" [clearOnBackspace]="true"
                    formControlName="categoryId" (change)="getList()" [items]="categoryList" bindLabel="Name"
                    bindValue="Id" placeholder="Select Category">
                  </ng-select>
                </div>
              </div>
            </form>

            <div class="col-12">
              <ngx-datatable class="material table-bordered mb-3" [rows]="rows" [loadingIndicator]="loadingIndicator"
                [externalPaging]="true" [columnMode]="ColumnMode.force" [headerHeight]="40" [footerHeight]="50"
                rowHeight="auto" [count]="page.totalElements" [offset]="page.pageNumber" [limit]="page.size"
                (page)="setPage($event)" [scrollbarH]="scrollBarHorizontal">
                >

                <ngx-datatable-column name="Exam" prop="ExamName" [draggable]="false" [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}">{{ value }}</span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="150" prop="Category" name="Category" [draggable]="false"
                  [sortable]="false" cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span *ngIf="value" title="{{ value }}">
                      {{ value }}
                    </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="60" name="Marks" prop="Marks" [draggable]="false" [sortable]="false"
                  cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <b>{{ value }}</b>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="60" name="Quota" prop="Quota" [draggable]="false" [sortable]="false"
                  cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <b>{{ value }}</b>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="200" name="Starts From" prop="StartDate" [draggable]="false"
                  [sortable]="false" cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <b>{{
                      value
                      ? (value
                      | amDateFormat: "DD MMM, YYYY hh:mm A")
                      : "-"
                      }}</b>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="200" name="Ends At" prop="EndDate" [draggable]="false"
                  [sortable]="false" cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <b>{{
                      value
                      ? (value
                      | amDateFormat: "DD MMM, YYYY hh:mm A")
                      : "-"
                      }}</b>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="100" name="Duration" prop="Duration" [draggable]="false"
                  [sortable]="false" cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <b>{{ value }}</b>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="80" name="Random Q." prop="Random" [draggable]="false"
                  [sortable]="false" cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value === true ? 'Yes' : 'No' }}">
                      {{ value === true ? "Yes" : "No" }}
                    </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="80" name="Imd. Publish" prop="Publish" [draggable]="false"
                  [sortable]="false" cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value === true ? 'Yes' : 'No' }}">
                      {{ value === true ? "Yes" : "No" }}
                    </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column prop="AllowFor" name="Allow For" [draggable]="false" [sortable]="false"
                  cellClass="text-center" headerClass="text-center">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    <span title="{{ row.AllowFor }}" *ngIf="row.AllowFor === 'All'">
                      {{ row.AllowFor }}
                    </span>
                    <span title="{{ row.AllowFor }}" *ngIf="row.AllowFor === 'Division'">
                      {{ row.AllowFor }} - {{ row.Division }}
                    </span>
                    <span title="{{ row.AllowFor }}" *ngIf="row.AllowFor === 'Department'">
                      {{ row.AllowFor }} - {{ row.Department }}
                    </span>
                    <span title="{{ row.AllowFor }}" *ngIf="row.AllowFor === 'Unit'">
                      {{ row.AllowFor }} - {{ row.Unit }}
                    </span>
                    <span title="{{ row.AllowFor }}" *ngIf="row.AllowFor === 'Trainee'">
                      {{ row.Trainees }} trainee(s)
                    </span>
                  </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column [maxWidth]="80" name="Active" prop="Active" [draggable]="false" [sortable]="false"
                  cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span *ngIf="value==true">
                      <span class="admin-color-green">Active</span>
                    </span>
                    <span *ngIf="value==false">
                      <span class="admin-color-red">In-Active</span>
                    </span>
                  </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column [maxWidth]="80" name="Order" prop="Order" [draggable]="false" [sortable]="false"
                  cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    {{value}}
                  </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column [maxWidth]="80" name="Action" prop="Id" [draggable]="false" [sortable]="false"
                  cellClass="text-center" headerClass="text-center">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    <button class="btn btn-outline-primary btn-mini" [routerLink]="['/evaluation-test-entry']"
                      [queryParams]="{ id: row.Id }" queryParamsHandling="merge">
                      <i class="feather icon-edit"></i> Edit
                    </button>
                  </ng-template>
                </ngx-datatable-column>
              </ngx-datatable>
            </div>
          </div>
        </div>
        <div class="card-footer"></div>
        <!-- end of card-footer -->
      </div>
    </div>
  </div>
</block-ui>