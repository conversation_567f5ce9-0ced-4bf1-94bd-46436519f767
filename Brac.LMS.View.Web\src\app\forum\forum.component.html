<block-ui>
  <!-- Page content-->
  <div class="container-fluid position-relative zindex-5 pb-0 mb-md-1 p-3 margin-top-neg-15">
    <div class="d-flex flex-column h-100 bg-tranparent-black-glass rounded-1 shadow-lg">
      <div class="pt-2 p-md-3">
        <div class="d-sm-flex align-items-center justify-content-between pb-1 text-center text-sm-start">
          <h1 class="h3 mb-2 text-nowrap">Forum</h1>
          <!-- <a class="btn btn-link fs-4 fw-bold btn-sm mb-2" (click)="backClicked()"><i
          class="  fs-4 ai-arrow-left fs-base me-2"></i>Go Back</a> -->
        </div>
        <hr class="mt-1 mb-4" />
        <!-- Content-->

        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-body">
                <form [formGroup]="filterForm" autocomplete="off">
                  <div class="row">
                    <div class="col-lg-8">
                      <input type="text" formControlName="text" class="form-control px-1"
                        placeholder="Search by title" />
                    </div>
                    <div class="col-lg-1 px-1">
                      <button class="btn btn-outline-primary" (click)="getList()" tooltip="Search">
                        <i class="ai-search fs-lg"></i>
                      </button>
                    </div>

                    <div class="col-lg-3 px-1 text-end">
                      <button *ngIf="favCategories.length > 0" class="btn btn-primary me-1"
                        [routerLink]="['/forum-post']" routerLinkActive="router-link-active">
                        New Post
                      </button>

                      <button type="button" class="btn btn-outline-danger" (click)="openCategoryModal(categoryTemplate)"
                        tooltip="My favorite categories">
                        <i class="fa-solid fa-heart"></i>
                        {{ favCategories.length }}
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-lg-8 border-end">
            <div class="row">
              <div class="col-12" *ngIf="topicList.length > 0; else elseTemplate">
                <div class="pb-grid-gutter border-bottom mt-3" *ngFor="
                        let item of topicList
                          | paginate
                            : {
                                itemsPerPage: page.size,
                                currentPage: page.pageNumber,
                                totalItems: page.totalElements
                              }
                      ">
                  <div class="d-flex align-items-center pb-1">
                    <h2 class="h3 nav-heading">
                      <a [routerLink]="['/forum-details', item.Id]" routerLinkActive="router-link-active">
                        {{
                        item.Title
                        }}
                      </a>
                    </h2>
                  </div>
                  <div class="fs-md" [innerHTML]="
                          item.Description | truncate: 250 | safe: 'html'
                        "></div>
                  <div class="d-sm-flex align-items-center justify-content-start text-center text-sm-start py-2">

                    <button class="btn btn-primary btn-sm me-2 py-1 px-2 fs-7" tooltip="Category">
                      {{item.Category}}
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm me-2 py-1 px-2 fs-7"
                      *ngFor="let tag of item.Tags" tooltip="tag">
                      {{ tag }}
                    </button>
                  </div>
                  <div class="d-flex align-items-center justify-content-between mt-3">
                    <div class="d-flex align-items-center me-3">
                      <img *ngIf="
                              !item.CreatorImage && item.CreatorGender == 'Male'
                            " class="rounded-circle" src="assets/img/user/male.jpg" alt="Emma Brown" width="42" />
                      <img *ngIf="
                              !item.CreatorImage &&
                              item.CreatorGender == 'Female'
                            " class="rounded-circle" src="assets/img/user/female.jpg" alt="Emma Brown" width="42" />
                      <img *ngIf="
                              !item.CreatorImage &&
                              item.CreatorGender == 'Others'
                            " class="rounded-circle" src="assets/img/user/other.jpg" alt="Emma Brown" width="42" />
                      <img *ngIf="item.CreatorImage" class="rounded-circle" [src]="baseUrl + item.CreatorImage"
                        alt="{{ item.Creator }}" width="42" />
                      <div class="ps-2 ms-1">
                        <h6 class="fs-sm mb-n1">
                          Posted by:
                          <span class="text-primary">
                            {{
                            item.Creator
                            }}
                          </span>
                        </h6>
                        <span class="fs-xs text-muted">
                          {{
                          item.CreatedDate | amTimeAgo
                          }}
                        </span>
                      </div>
                    </div>
                    <span class="badge me-2 px-2 py-2 fs-7" [ngClass]="{
                            'bg-warning': item.Status === 'Pending',
                            'bg-danger': item.Status === 'Closed'
                          }">
                      {{ item.Status }}
                    </span>
                    <div class="text-nowrap">
                      <button class="btn-like cursor-default" type="button">
                        {{ item.NoOfLikes }}
                      </button>
                      <button class="btn-comment cursor-default" type="button">
                        <i class="far fa-comment-alt text-primary"></i>
                        {{ item.NoOfReplies }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <ng-template #elseTemplate>
                <div class="col-12" *ngIf="favCategories.length > 0">
                  <h6 class="py-2">No Post Found</h6>
                </div>
              </ng-template>
            </div>

            <div class="row" *ngIf="topicList.length > 0">
              <div class="col-lg-6 col-xs-12">
                <p class="mb-0" *ngIf="page">{{ page.showingResult() }}</p>
              </div>
              <div class="col-lg-6 col-xs-12">
                <nav class="d-flex justify-content-end">
                  <pagination-controls (pageChange)="setPage($event)"></pagination-controls>
                </nav>
              </div>
              <!-- end col -->
            </div>
          </div>
          <div class="col-lg-4 sidebar bg-secondary pt-5 ps-lg-4 pb-md-2 border-end">
            <div class="widget mt-n1" *ngIf="myTopicList.length > 0">
              <h2 class="h3 pb-1">My posts</h2>

              <div class="d-flex align-items-center pb-1 mb-3" *ngFor="let item of myTopicList; let i = index">
                <div class="ms-1">
                  <h4 class="fs-md nav-heading mb-1">
                    <a class="fw-medium fs-5" [routerLink]="['/forum-details', item.Id]"
                      routerLinkActive="router-link-active">{{ i + 1 }}. {{ item.Title }}</a>
                  </h4>
                </div>
              </div>
            </div>

            <hr class="my-4" *ngIf="myTopicList.length > 0" />

            <div class="widget mt-n1" *ngIf="popularList.length > 0">
              <h2 class="h3 pb-1">Popular Post</h2>

              <div class="d-flex align-items-center pb-1 mb-3" *ngFor="let item of popularList; let i = index">
                <div class="ms-1">
                  <h4 class="fs-md nav-heading mb-1">
                    <a class="fw-medium fs-5" [routerLink]="['/forum-details', item.Id]"
                      routerLinkActive="router-link-active">{{ i + 1 }}. {{ item.Title }}</a>
                  </h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #categoryTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left">Add categories that you like</h4>
      <button type="button " class="btn-close" aria-label="Close " (click)="modalCategoryHide()"></button>
    </div>
    <div class="modal-body">
      <form [formGroup]="categoryForm" autocomplete="off">
        <div class="row">
          <div class="col-12">
            <div class="form-group">
              <label class="col-form-label required pb-0"> Categories</label>
              <small class="form-text text-muted">
                By selecting these categoriers you will be able to see the posts
                that you are interested in first.
              </small>
              <ng-select formControlName="category" [multiple]="true" [addTag]="true" [items]="categoryList"
                bindLabel="Name" bindValue="Id" placeholder="Select your favourite categories">
              </ng-select>

              <div *ngIf="submitted && c['category'].errors" class="error-text">
                <span *ngIf="c['category'].errors['required']" class="text-danger fs-6">You cannot continue untill you
                  choose atleast one category</span>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button class="btn btn-outline-secondary btn-sm mr-2" (click)="modalCategoryHide()">
        <i class="fa fa-times-circle"></i> Close
      </button>

      <button *ngIf="favCategories.length > 0" class="btn btn-success btn-sm" (click)="onMyFavCategoryFormSubmit()">
        <i class="fa fa-check-circle"></i> Save
      </button>
    </div>
  </ng-template>
</block-ui>