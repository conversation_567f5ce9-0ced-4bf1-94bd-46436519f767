{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { TimeWiseLearningHourStudyReportComponent } from './time-wise-learning-hour-study-report.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: TimeWiseLearningHourStudyReportComponent\n}];\nexport let TimeWiseLearningHourStudyReportRoutingModule = /*#__PURE__*/(() => {\n  class TimeWiseLearningHourStudyReportRoutingModule {}\n\n  TimeWiseLearningHourStudyReportRoutingModule.ɵfac = function TimeWiseLearningHourStudyReportRoutingModule_Factory(t) {\n    return new (t || TimeWiseLearningHourStudyReportRoutingModule)();\n  };\n\n  TimeWiseLearningHourStudyReportRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TimeWiseLearningHourStudyReportRoutingModule\n  });\n  TimeWiseLearningHourStudyReportRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return TimeWiseLearningHourStudyReportRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}