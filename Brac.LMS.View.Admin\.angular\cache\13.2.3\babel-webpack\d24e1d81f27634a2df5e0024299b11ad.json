{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { CertificationTestEntryRoutingModule } from './certification-test-entry-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport { NgbDatepickerModule, NgbTimepickerModule } from '@ng-bootstrap/ng-bootstrap';\nimport { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';\nimport { OwlDateTimeModule, OwlNativeDateTimeModule } from '@danielmoncada/angular-datetime-picker';\nimport { NgxEditorModule } from 'ngx-editor';\nimport * as i0 from \"@angular/core\";\nexport let CertificationTestEntryModule = /*#__PURE__*/(() => {\n  class CertificationTestEntryModule {}\n\n  CertificationTestEntryModule.ɵfac = function CertificationTestEntryModule_Factory(t) {\n    return new (t || CertificationTestEntryModule)();\n  };\n\n  CertificationTestEntryModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CertificationTestEntryModule\n  });\n  CertificationTestEntryModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, CertificationTestEntryRoutingModule, SharedModule, NgxMaterialTimepickerModule, NgbDatepickerModule, NgbTimepickerModule, OwlDateTimeModule, OwlNativeDateTimeModule, NgxEditorModule]]\n  });\n  return CertificationTestEntryModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}