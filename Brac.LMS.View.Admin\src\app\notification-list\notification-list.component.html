<block-ui>

    <div class="row">
        <!-- Content-->
        <div class="col-lg-10 col-12">
            <div class="d-flex flex-column h-100 bg-light rounded-1 shadow-lg">
                <div class="pt-2 p-md-3">
                    <div class="d-sm-flex align-items-center justify-content-between pb-1 text-center text-sm-start">
                        <h3 class="h3 mb-2 text-nowrap">My Notifications</h3>
                    </div>
                    <hr class="mt-1 mb-4">
                    <!-- Content-->
                    <div class="btn-group btn-group-sm mb-3" role="group" aria-label="Basic radio toggle button group">
                        <input type="radio" [(ngModel)]="unseenOnly" class="btn-check" name="unseenOnly" id="btnAll"
                            autocomplete="off" value="0" (change)="loadList()">
                        <label class="btn btn-outline-primary {{clickedAll}}" for="btnAll">All</label>

                        <input type="radio" [(ngModel)]="unseenOnly" class="btn-check" name="unseenOnly"
                            id="btnUnseenOnly" autocomplete="off" value="1" (change)="loadList()">
                        <label class="btn btn-outline-primary {{clickedUnseen}}" for="btnUnseenOnly">Unread</label>
                    </div>

                    <div id="notification-settings">

                        <div class="row border-bottom py-3" *ngFor="let item of rows | paginate
              : {
                  itemsPerPage: page.size,
                  currentPage: page.pageNumber,
                  totalItems: page.totalElements
                };
          let i = index">

                            <div class="col-12 cursor-pointer" (click)="onClickNotification(item)">
                                <div class="left-div-active me-2"
                                    [ngClass]="{'left-div-active': !item.Seen, 'left-div-inactive': item.Seen}"></div>

                                <div class="fs-sm ps-sm-3">
                                    <h3 class="mb-1 fw-500">{{item.Title}}</h3>
                                    <div class="d-sm-flex text-heading align-items-center">
                                        <div class="d-flex align-items-center">
                                            <div class="">{{item.Details}}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ms-sm-auto text-muted fs-xs pt-2">
                                        {{ item.CreatedOn | amDateFormat: "DD-MMM-YYYY hh:mm:ss A"}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="row text-center" *ngIf="rows.length > 0">
                            <div class="col-md-3 col-xs-12">
                                <p *ngIf="page">{{ page.showingResult() }}</p>
                            </div>
                            <div class="col-md-9 col-xs-12">
                                <nav class="align-items-center">
                                    <pagination-controls (pageChange)="setPage($event)"></pagination-controls>
                                </nav>
                            </div>
                            <!-- end col -->
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

</block-ui>