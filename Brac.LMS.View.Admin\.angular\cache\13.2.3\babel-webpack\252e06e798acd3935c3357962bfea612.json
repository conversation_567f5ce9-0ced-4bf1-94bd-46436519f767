{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EvaluationTestEntryRoutingModule } from './evaluation-test-entry-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport { NgxEditorModule } from 'ngx-editor';\nimport * as i0 from \"@angular/core\";\nexport let EvaluationTestEntryModule = /*#__PURE__*/(() => {\n  class EvaluationTestEntryModule {}\n\n  EvaluationTestEntryModule.ɵfac = function EvaluationTestEntryModule_Factory(t) {\n    return new (t || EvaluationTestEntryModule)();\n  };\n\n  EvaluationTestEntryModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: EvaluationTestEntryModule\n  });\n  EvaluationTestEntryModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, EvaluationTestEntryRoutingModule, SharedModule, NgxEditorModule]]\n  });\n  return EvaluationTestEntryModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}