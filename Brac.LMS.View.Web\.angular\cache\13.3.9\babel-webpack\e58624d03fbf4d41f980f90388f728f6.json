{"ast": null, "code": "import { __awaiter, __decorate } from \"tslib\";\nimport { environment } from 'src/environments/environment';\nimport { Validators } from '@angular/forms';\nimport { NavigationEnd } from '@angular/router';\nimport { BlockUI } from 'ng-block-ui';\nimport { ResponseStatus } from 'src/app/_models/enum';\nimport { Page } from '../_models/page';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"ngx-bootstrap/modal\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"ng-block-ui\";\nimport * as i8 from \"ngx-bootstrap/tooltip\";\nimport * as i9 from \"@angular/flex-layout/extended\";\nimport * as i10 from \"ngx-pagination\";\nimport * as i11 from \"@ng-select/ng-select\";\nimport * as i12 from \"../_helpers/safe-pipe\";\nimport * as i13 from \"../_helpers/truncate-pipe\";\nimport * as i14 from \"ngx-moment\";\nconst _c0 = [\"categoryTemplate\"];\n\nconst _c1 = function () {\n  return [\"/forum-post\"];\n};\n\nfunction ForumComponent_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵtext(1, \" New Post \");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\n\nfunction ForumComponent_div_27_div_1_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tag_r17 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tag_r17, \" \");\n  }\n}\n\nfunction ForumComponent_div_27_div_1_img_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 54);\n  }\n}\n\nfunction ForumComponent_div_27_div_1_img_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 55);\n  }\n}\n\nfunction ForumComponent_div_27_div_1_img_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 56);\n  }\n}\n\nfunction ForumComponent_div_27_div_1_img_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 57);\n  }\n\n  if (rf & 2) {\n    const item_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r11.Creator);\n    i0.ɵɵproperty(\"src\", ctx_r16.baseUrl + item_r11.CreatorImage, i0.ɵɵsanitizeUrl);\n  }\n}\n\nconst _c2 = function (a1) {\n  return [\"/forum-details\", a1];\n};\n\nconst _c3 = function (a0, a1) {\n  return {\n    \"bg-warning\": a0,\n    \"bg-danger\": a1\n  };\n};\n\nfunction ForumComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelementStart(1, \"div\", 31);\n    i0.ɵɵelementStart(2, \"h2\", 32);\n    i0.ɵɵelementStart(3, \"a\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 34);\n    i0.ɵɵpipe(6, \"safe\");\n    i0.ɵɵpipe(7, \"truncate\");\n    i0.ɵɵelementStart(8, \"div\", 35);\n    i0.ɵɵelementStart(9, \"button\", 36);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ForumComponent_div_27_div_1_button_11_Template, 2, 1, \"button\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 38);\n    i0.ɵɵelementStart(13, \"div\", 39);\n    i0.ɵɵtemplate(14, ForumComponent_div_27_div_1_img_14_Template, 1, 0, \"img\", 40);\n    i0.ɵɵtemplate(15, ForumComponent_div_27_div_1_img_15_Template, 1, 0, \"img\", 41);\n    i0.ɵɵtemplate(16, ForumComponent_div_27_div_1_img_16_Template, 1, 0, \"img\", 42);\n    i0.ɵɵtemplate(17, ForumComponent_div_27_div_1_img_17_Template, 1, 2, \"img\", 43);\n    i0.ɵɵelementStart(18, \"div\", 44);\n    i0.ɵɵelementStart(19, \"h6\", 45);\n    i0.ɵɵtext(20, \" Posted by: \");\n    i0.ɵɵelementStart(21, \"span\", 46);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 47);\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"amTimeAgo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 48);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 49);\n    i0.ɵɵelementStart(29, \"button\", 50);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 51);\n    i0.ɵɵelement(32, \"i\", 52);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(23, _c2, item_r11.Id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.Title, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(6, 15, i0.ɵɵpipeBind2(7, 18, item_r11.Description, 250), \"html\"), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.Category, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", item_r11.Tags);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !item_r11.CreatorImage && item_r11.CreatorGender == \"Male\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r11.CreatorImage && item_r11.CreatorGender == \"Female\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r11.CreatorImage && item_r11.CreatorGender == \"Others\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r11.CreatorImage);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.Creator, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(25, 21, item_r11.CreatedDate), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(25, _c3, item_r11.Status === \"Pending\", item_r11.Status === \"Closed\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.Status, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.NoOfLikes, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.NoOfReplies, \" \");\n  }\n}\n\nconst _c4 = function (a0, a1, a2) {\n  return {\n    itemsPerPage: a0,\n    currentPage: a1,\n    totalItems: a2\n  };\n};\n\nfunction ForumComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, ForumComponent_div_27_div_1_Template, 34, 28, \"div\", 29);\n    i0.ɵɵpipe(2, \"paginate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(2, 1, ctx_r1.topicList, i0.ɵɵpureFunction3(4, _c4, ctx_r1.page.size, ctx_r1.page.pageNumber, ctx_r1.page.totalElements)));\n  }\n}\n\nfunction ForumComponent_ng_template_28_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelementStart(1, \"h6\", 59);\n    i0.ɵɵtext(2, \"No Post Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumComponent_ng_template_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ForumComponent_ng_template_28_div_0_Template, 3, 0, \"div\", 58);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.favCategories.length > 0);\n  }\n}\n\nfunction ForumComponent_div_30_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r20.page.showingResult());\n  }\n}\n\nfunction ForumComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelementStart(1, \"div\", 60);\n    i0.ɵɵtemplate(2, ForumComponent_div_30_p_2_Template, 2, 1, \"p\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 60);\n    i0.ɵɵelementStart(4, \"nav\", 62);\n    i0.ɵɵelementStart(5, \"pagination-controls\", 63);\n    i0.ɵɵlistener(\"pageChange\", function ForumComponent_div_30_Template_pagination_controls_pageChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return ctx_r21.setPage($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.page);\n  }\n}\n\nfunction ForumComponent_div_32_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵelementStart(1, \"div\", 69);\n    i0.ɵɵelementStart(2, \"h4\", 70);\n    i0.ɵɵelementStart(3, \"a\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r24 = ctx.$implicit;\n    const i_r25 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c2, item_r24.Id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", i_r25 + 1, \". \", item_r24.Title, \"\");\n  }\n}\n\nfunction ForumComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelementStart(1, \"h2\", 66);\n    i0.ɵɵtext(2, \"My posts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ForumComponent_div_32_div_3_Template, 5, 5, \"div\", 67);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.myTopicList);\n  }\n}\n\nfunction ForumComponent_hr_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\", 72);\n  }\n}\n\nfunction ForumComponent_div_34_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵelementStart(1, \"div\", 69);\n    i0.ɵɵelementStart(2, \"h4\", 70);\n    i0.ɵɵelementStart(3, \"a\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r27 = ctx.$implicit;\n    const i_r28 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c2, item_r27.Id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", i_r28 + 1, \". \", item_r27.Title, \"\");\n  }\n}\n\nfunction ForumComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelementStart(1, \"h2\", 66);\n    i0.ɵɵtext(2, \"Popular Post\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ForumComponent_div_34_div_3_Template, 5, 5, \"div\", 67);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.popularList);\n  }\n}\n\nfunction ForumComponent_ng_template_35_div_14_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 88);\n    i0.ɵɵtext(1, \"You cannot continue untill you choose atleast one category\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumComponent_ng_template_35_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵtemplate(1, ForumComponent_ng_template_35_div_14_span_1_Template, 2, 0, \"span\", 87);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.c[\"category\"].errors[\"required\"]);\n  }\n}\n\nfunction ForumComponent_ng_template_35_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function ForumComponent_ng_template_35_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return ctx_r32.onMyFavCategoryFormSubmit();\n    });\n    i0.ɵɵelement(1, \"i\", 90);\n    i0.ɵɵtext(2, \" Save \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumComponent_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelementStart(1, \"h4\", 74);\n    i0.ɵɵtext(2, \"Add categories that you like\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function ForumComponent_ng_template_35_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return ctx_r34.modalCategoryHide();\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 76);\n    i0.ɵɵelementStart(5, \"form\", 10);\n    i0.ɵɵelementStart(6, \"div\", 6);\n    i0.ɵɵelementStart(7, \"div\", 7);\n    i0.ɵɵelementStart(8, \"div\", 77);\n    i0.ɵɵelementStart(9, \"label\", 78);\n    i0.ɵɵtext(10, \" Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"small\", 79);\n    i0.ɵɵtext(12, \" By selecting these categoriers you will be able to see the posts that you are interested in first. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"ng-select\", 80);\n    i0.ɵɵtemplate(14, ForumComponent_ng_template_35_div_14_Template, 2, 1, \"div\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 82);\n    i0.ɵɵelementStart(16, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function ForumComponent_ng_template_35_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return ctx_r36.modalCategoryHide();\n    });\n    i0.ɵɵelement(17, \"i\", 84);\n    i0.ɵɵtext(18, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ForumComponent_ng_template_35_button_19_Template, 3, 0, \"button\", 85);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"formGroup\", ctx_r9.categoryForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"multiple\", true)(\"addTag\", true)(\"items\", ctx_r9.categoryList);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.submitted && ctx_r9.c[\"category\"].errors);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.favCategories.length > 0);\n  }\n}\n\nexport class ForumComponent {\n  constructor(router, _service, toastr, modalService, formBuilder, _location) {\n    this.router = router;\n    this._service = _service;\n    this.toastr = toastr;\n    this.modalService = modalService;\n    this.formBuilder = formBuilder;\n    this._location = _location;\n    this.baseUrl = environment.baseUrl;\n    this.mediaBaseUrl = environment.mediaBaseUrl;\n    this.submitted = false;\n    this.timestamp = new Date().getTime();\n    this.page = new Page();\n    this.modalConfig = {\n      class: 'gray modal-md',\n      backdrop: 'static',\n      keyboard: false\n    };\n    this.favCategories = [];\n    this.categoryList = [];\n    this.topicList = [];\n    this.myTopicList = [];\n    this.popularList = [];\n    this.page.startsFrom = 1;\n    this.page.pageNumber = 1;\n    this.page.size = 10;\n    this.router.events.subscribe(evt => {\n      if (!(evt instanceof NavigationEnd)) {\n        return;\n      }\n\n      window.scrollTo(0, 0);\n    });\n  }\n\n  ngOnInit() {\n    return __awaiter(this, void 0, void 0, function* () {\n      this.filterForm = this.formBuilder.group({\n        text: [null, [Validators.maxLength(250)]],\n        category: [null],\n        sort: [false]\n      });\n      this.categoryForm = this.formBuilder.group({\n        category: [[], [Validators.required]]\n      });\n      yield this.getMyFavCategories().then(favCatFound => {\n        if (!favCatFound) {\n          this.openCategoryModal(this.content);\n        } else {\n          this.getList();\n          this.getMyTopicList();\n          this.getPopularTopicList();\n        }\n      });\n    });\n  }\n\n  get c() {\n    return this.categoryForm.controls;\n  }\n\n  backClicked() {\n    this._location.back();\n  }\n\n  getMyFavCategories() {\n    return new Promise(resolve => {\n      this.blockUI.start('Loading data. Please wait...');\n\n      this._service.get('forum/my-favorite-categories/get').subscribe({\n        next: res => {\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              timeOut: 2000\n            });\n            return resolve(false);\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              timeOut: 2000\n            });\n            return resolve(false);\n          }\n\n          this.categoryList = res.Data;\n          this.favCategories = this.categoryList.filter(x => x.Selected).map(x => x.Id);\n          this.categoryForm.controls['category'].setValue(this.favCategories);\n          this.filterForm.controls['category'].setValue(this.favCategories);\n          resolve(this.favCategories.length > 0);\n        },\n        error: err => {\n          this.toastr.warning(err.Messaage || err, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          this.blockUI.stop();\n          resolve(false);\n        },\n        complete: () => this.blockUI.stop()\n      });\n    });\n  }\n\n  setPage(pageNumber) {\n    this.page.pageNumber = pageNumber;\n    this.getList();\n  }\n\n  getList() {\n    const obj = {\n      text: this.filterForm.value.text ? this.filterForm.value.text.trim() : this.filterForm.value.text,\n      size: this.page.size,\n      pageNumber: this.page.pageNumber - 1\n    };\n\n    this._service.get('forum/get-topics', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.topicList = res.Data.Records;\n        this.page.pageTotalElements = this.topicList.length;\n        this.page.totalElements = res.Data.Total;\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n        if (this.page.totalPages == 1) this.page.pageNumber = 1;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  getMyTopicList() {\n    this._service.get('forum/get-my-topic-titles/5').subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.myTopicList = res.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => {}\n    });\n  }\n\n  getPopularTopicList() {\n    this._service.get('forum/get-popular-topic-titles/5').subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.popularList = res.Data;\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => {}\n    });\n  }\n\n  modalCategoryHide() {\n    this.categoryForm.reset();\n    this.modalRef.hide();\n    this.submitted = false;\n  }\n\n  openCategoryModal(template) {\n    this.categoryForm.controls['category'].setValue(this.favCategories);\n    this.modalRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  onMyFavCategoryFormSubmit() {\n    this.submitted = true;\n\n    if (this.categoryForm.invalid) {\n      return;\n    }\n\n    this.blockUI.start('Saving your favorits. Please wait...');\n\n    this._service.post('forum/my-favorite-categories/save-or-modify', this.categoryForm.value.category).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, 'Success!', {\n          timeOut: 2000\n        });\n        this.favCategories = this.categoryForm.value.category;\n        this.getList();\n        this.getMyTopicList();\n        this.getPopularTopicList();\n        this.modalCategoryHide();\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n}\n\nForumComponent.ɵfac = function ForumComponent_Factory(t) {\n  return new (t || ForumComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i4.BsModalService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.Location));\n};\n\nForumComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ForumComponent,\n  selectors: [[\"app-forum\"]],\n  viewQuery: function ForumComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n    }\n  },\n  decls: 37,\n  vars: 9,\n  consts: [[1, \"container-fluid\", \"position-relative\", \"zindex-5\", \"pb-0\", \"mb-md-1\", \"p-3\", \"margin-top-neg-15\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-tranparent-black-glass\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-nowrap\"], [1, \"mt-1\", \"mb-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-body\"], [\"autocomplete\", \"off\", 3, \"formGroup\"], [1, \"col-lg-8\"], [\"type\", \"text\", \"formControlName\", \"text\", \"placeholder\", \"Search by title\", 1, \"form-control\", \"px-1\"], [1, \"col-lg-1\", \"px-1\"], [\"tooltip\", \"Search\", 1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"ai-search\", \"fs-lg\"], [1, \"col-lg-3\", \"px-1\", \"text-end\"], [\"class\", \"btn btn-primary me-1\", \"routerLinkActive\", \"router-link-active\", 3, \"routerLink\", 4, \"ngIf\"], [\"type\", \"button\", \"tooltip\", \"My favorite categories\", 1, \"btn\", \"btn-outline-danger\", 3, \"click\"], [1, \"fa-solid\", \"fa-heart\"], [1, \"col-lg-8\", \"border-end\"], [\"class\", \"col-12\", 4, \"ngIf\", \"ngIfElse\"], [\"elseTemplate\", \"\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"col-lg-4\", \"sidebar\", \"bg-secondary\", \"pt-5\", \"ps-lg-4\", \"pb-md-2\", \"border-end\"], [\"class\", \"widget mt-n1\", 4, \"ngIf\"], [\"class\", \"my-4\", 4, \"ngIf\"], [\"categoryTemplate\", \"\"], [\"routerLinkActive\", \"router-link-active\", 1, \"btn\", \"btn-primary\", \"me-1\", 3, \"routerLink\"], [\"class\", \"pb-grid-gutter border-bottom mt-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"pb-grid-gutter\", \"border-bottom\", \"mt-3\"], [1, \"d-flex\", \"align-items-center\", \"pb-1\"], [1, \"h3\", \"nav-heading\"], [\"routerLinkActive\", \"router-link-active\", 3, \"routerLink\"], [1, \"fs-md\", 3, \"innerHTML\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-start\", \"text-center\", \"text-sm-start\", \"py-2\"], [\"tooltip\", \"Category\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", \"py-1\", \"px-2\", \"fs-7\"], [\"type\", \"button\", \"class\", \"btn btn-outline-primary btn-sm me-2 py-1 px-2 fs-7\", \"tooltip\", \"tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\", \"mt-3\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [\"class\", \"rounded-circle\", \"src\", \"assets/img/user/male.jpg\", \"alt\", \"Emma Brown\", \"width\", \"42\", 4, \"ngIf\"], [\"class\", \"rounded-circle\", \"src\", \"assets/img/user/female.jpg\", \"alt\", \"Emma Brown\", \"width\", \"42\", 4, \"ngIf\"], [\"class\", \"rounded-circle\", \"src\", \"assets/img/user/other.jpg\", \"alt\", \"Emma Brown\", \"width\", \"42\", 4, \"ngIf\"], [\"class\", \"rounded-circle\", \"width\", \"42\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"ps-2\", \"ms-1\"], [1, \"fs-sm\", \"mb-n1\"], [1, \"text-primary\"], [1, \"fs-xs\", \"text-muted\"], [1, \"badge\", \"me-2\", \"px-2\", \"py-2\", \"fs-7\", 3, \"ngClass\"], [1, \"text-nowrap\"], [\"type\", \"button\", 1, \"btn-like\", \"cursor-default\"], [\"type\", \"button\", 1, \"btn-comment\", \"cursor-default\"], [1, \"far\", \"fa-comment-alt\", \"text-primary\"], [\"type\", \"button\", \"tooltip\", \"tag\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"me-2\", \"py-1\", \"px-2\", \"fs-7\"], [\"src\", \"assets/img/user/male.jpg\", \"alt\", \"Emma Brown\", \"width\", \"42\", 1, \"rounded-circle\"], [\"src\", \"assets/img/user/female.jpg\", \"alt\", \"Emma Brown\", \"width\", \"42\", 1, \"rounded-circle\"], [\"src\", \"assets/img/user/other.jpg\", \"alt\", \"Emma Brown\", \"width\", \"42\", 1, \"rounded-circle\"], [\"width\", \"42\", 1, \"rounded-circle\", 3, \"src\", \"alt\"], [\"class\", \"col-12\", 4, \"ngIf\"], [1, \"py-2\"], [1, \"col-lg-6\", \"col-xs-12\"], [\"class\", \"mb-0\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [3, \"pageChange\"], [1, \"mb-0\"], [1, \"widget\", \"mt-n1\"], [1, \"h3\", \"pb-1\"], [\"class\", \"d-flex align-items-center pb-1 mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\", \"pb-1\", \"mb-3\"], [1, \"ms-1\"], [1, \"fs-md\", \"nav-heading\", \"mb-1\"], [\"routerLinkActive\", \"router-link-active\", 1, \"fw-medium\", \"fs-5\", 3, \"routerLink\"], [1, \"my-4\"], [1, \"modal-header\"], [1, \"modal-title\", \"pull-left\"], [\"type\", \"button \", \"aria-label\", \"Close \", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\"], [1, \"form-group\"], [1, \"col-form-label\", \"required\", \"pb-0\"], [1, \"form-text\", \"text-muted\"], [\"formControlName\", \"category\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select your favourite categories\", 3, \"multiple\", \"addTag\", \"items\"], [\"class\", \"error-text\", 4, \"ngIf\"], [1, \"modal-footer\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"mr-2\", 3, \"click\"], [1, \"fa\", \"fa-times-circle\"], [\"class\", \"btn btn-success btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"error-text\"], [\"class\", \"text-danger fs-6\", 4, \"ngIf\"], [1, \"text-danger\", \"fs-6\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fa\", \"fa-check-circle\"]],\n  template: function ForumComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r37 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"h1\", 4);\n      i0.ɵɵtext(6, \"Forum\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(7, \"hr\", 5);\n      i0.ɵɵelementStart(8, \"div\", 6);\n      i0.ɵɵelementStart(9, \"div\", 7);\n      i0.ɵɵelementStart(10, \"div\", 8);\n      i0.ɵɵelementStart(11, \"div\", 9);\n      i0.ɵɵelementStart(12, \"form\", 10);\n      i0.ɵɵelementStart(13, \"div\", 6);\n      i0.ɵɵelementStart(14, \"div\", 11);\n      i0.ɵɵelement(15, \"input\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(16, \"div\", 13);\n      i0.ɵɵelementStart(17, \"button\", 14);\n      i0.ɵɵlistener(\"click\", function ForumComponent_Template_button_click_17_listener() {\n        return ctx.getList();\n      });\n      i0.ɵɵelement(18, \"i\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"div\", 16);\n      i0.ɵɵtemplate(20, ForumComponent_button_20_Template, 2, 2, \"button\", 17);\n      i0.ɵɵelementStart(21, \"button\", 18);\n      i0.ɵɵlistener(\"click\", function ForumComponent_Template_button_click_21_listener() {\n        i0.ɵɵrestoreView(_r37);\n\n        const _r8 = i0.ɵɵreference(36);\n\n        return ctx.openCategoryModal(_r8);\n      });\n      i0.ɵɵelement(22, \"i\", 19);\n      i0.ɵɵtext(23);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"div\", 6);\n      i0.ɵɵelementStart(25, \"div\", 20);\n      i0.ɵɵelementStart(26, \"div\", 6);\n      i0.ɵɵtemplate(27, ForumComponent_div_27_Template, 3, 8, \"div\", 21);\n      i0.ɵɵtemplate(28, ForumComponent_ng_template_28_Template, 1, 1, \"ng-template\", null, 22, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(30, ForumComponent_div_30_Template, 6, 1, \"div\", 23);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(31, \"div\", 24);\n      i0.ɵɵtemplate(32, ForumComponent_div_32_Template, 4, 1, \"div\", 25);\n      i0.ɵɵtemplate(33, ForumComponent_hr_33_Template, 1, 0, \"hr\", 26);\n      i0.ɵɵtemplate(34, ForumComponent_div_34_Template, 4, 1, \"div\", 25);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(35, ForumComponent_ng_template_35_Template, 20, 6, \"ng-template\", null, 27, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      const _r2 = i0.ɵɵreference(29);\n\n      i0.ɵɵadvance(12);\n      i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngIf\", ctx.favCategories.length > 0);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate1(\" \", ctx.favCategories.length, \" \");\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx.topicList.length > 0)(\"ngIfElse\", _r2);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.topicList.length > 0);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.myTopicList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.myTopicList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.popularList.length > 0);\n    }\n  },\n  directives: [i7.BlockUIComponent, i5.ɵNgNoValidate, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.DefaultValueAccessor, i5.NgControlStatus, i5.FormControlName, i8.TooltipDirective, i6.NgIf, i1.RouterLinkActive, i1.RouterLink, i6.NgForOf, i1.RouterLinkWithHref, i6.NgClass, i9.DefaultClassDirective, i10.PaginationControlsComponent, i11.NgSelectComponent],\n  pipes: [i10.PaginatePipe, i12.SafePipe, i13.TruncatePipe, i14.TimeAgoPipe],\n  styles: [\"\"]\n});\n\n__decorate([BlockUI()], ForumComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}