import {
  Component,
  TemplateRef,
  ViewEncapsulation,
  OnInit,
  OnDestroy,
} from "@angular/core";
import { ColumnMode } from "@swimlane/ngx-datatable";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { CommonService } from "../_services/common.service";
import { ToastrService } from "ngx-toastr";
import { BlockUI, NgBlockUI } from "ng-block-ui";

import { NgbTimepickerConfig } from "@ng-bootstrap/ng-bootstrap";
import * as moment from "moment";
import { trigger, transition, style, animate } from "@angular/animations";
import { ResponseStatus } from "../_models/enum";
import { BsModalRef, BsModalService } from "ngx-bootstrap/modal";
import { BsDatepickerConfig } from "ngx-bootstrap/datepicker";
import { Editor, Toolbar } from "ngx-editor";
import { AppComponent } from "../app.component";

@Component({
  selector: "app-certification-test-entry",
  templateUrl: "./certification-test-entry.component.html",

  encapsulation: ViewEncapsulation.None,
  animations: [
    trigger("inOutAnimation", [
      transition(":enter", [
        style({ height: 0, opacity: 0 }),
        animate("1s ease-out", style({ height: 300, opacity: 1 })),
      ]),
      transition(":leave", [
        animate("1s ease-out", style({ height: 0, opacity: 0 })),
      ]),
    ]),
  ],
})
export class CertificationTestEntryComponent implements OnInit, OnDestroy {
  entryForm: FormGroup;
  mcqForm: FormGroup;
  mcqEditForm: FormGroup;
  tfqForm: FormGroup;
  tfqEditForm: FormGroup;
  figqForm: FormGroup;
  figqEditForm: FormGroup;
  mqForm: FormGroup;
  mqEditForm: FormGroup;
  wqForm: FormGroup;
  submitted = false;
  editsubmitted = false;
  @BlockUI() blockUI: NgBlockUI;
  formTitle = "Certification Test Entry";
  btnSaveText = "Save";
  selectedSet: any;
  id: string;
  is_per = true;
  ExamExists = false;
  ExamExistsMsg = "";
  modalTitle = "Set Time";

  rows = [];
  loadingIndicator = false;
  ColumnMode = ColumnMode;
  isEdit: boolean = false;

  mcqList = [];
  loadingMCQ = false;
  mcqFile: any;
  btnMCQSaveText: string = "Save";
  modalMCQTitle: string = "Add MCQ Questions";
  modalMCQRef: BsModalRef;

  truFalseList = [];
  tfQuestionList = [];
  tfqFile: any;
  loadingTFQ = false;
  btnTFQSaveText: string = "Save";
  modalTFTitle: string = "Add True/False Questions";
  modalTFRef: BsModalRef;

  figList = [];
  figQuestionList = [];
  figqFile: any;
  loadingFIG = false;
  btnFIGSaveText: string = "Save";
  modalFIGTitle: string = "Add fill in the gaps Questions";
  modalFIGRef: BsModalRef;

  matchingList = [];
  matchingQuestionList = [];
  matchingqFile: any;
  loadingMatching = false;
  btnMatchingSaveText: string = "Save";
  modalMatchingTitle: string = "Add Left Right Matching Questions";
  modalMatchingRef: BsModalRef;

  writtenList = [];
  writtenQuestionList = [];
  wqFile: any;
  loadingWritten = false;
  btnWrittenSaveText: string = "Save";
  modalWrittenTitle: string = "Add Written Questions";
  modalWrittenRef: BsModalRef;

  modalConfig: any = { class: "gray modal-lg", backdrop: "static" };
  modalRef: BsModalRef;
  bsConfig: Partial<BsDatepickerConfig>;

  courseList: Array<any> = [];
  examList: Array<any> = [];
  setList: Array<any> = [];
  mcqQuestionList: Array<any> = [];

  selectedExam: any;

  MCQMark: number;
  TrueFalseMark: number;
  FIGMark: number;
  MatchingMark: number;
  WrittenMark: number;

  segmentList: Array<any> = [];
  segmentWiseQuestionsInput: Array<any> = [];
  segmentWiseQuestions: Array<any> = [];
  totalQuestions: {
    mcq: number;
    tfq: number;
    figq: number;
    matchingq: number;
    writtenq: number;
  };

  scrollBarHorizontal = window.innerWidth < 1200;

  editor: Editor;
  toolbar: Toolbar = [
    ["bold", "italic"],
    ["underline", "strike"],
    ["ordered_list", "bullet_list"],
    [{ heading: ["h1", "h2", "h3", "h4", "h5", "h6"] }],
    ["text_color", "background_color"],
    ["align_left", "align_center", "align_right", "align_justify"],
  ];

  constructor(
    private appComponent: AppComponent,
    private modalService: BsModalService,
    public formBuilder: FormBuilder,
    private _service: CommonService,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    config: NgbTimepickerConfig
  ) {
    window.onresize = () => {
      this.scrollBarHorizontal = window.innerWidth < 1200;
    };
    this.bsConfig = Object.assign({}, { containerClass: "theme-blue" });
    config.seconds = false;
    config.spinners = false;
    if (this.route.snapshot.queryParamMap.has("id")) {
      this.id = this.route.snapshot.queryParamMap.get("id");
      this.isEdit = true;
      this.getMarks();
      this.getItem();
    }
  }

  handleSelectClick(selectElement) {
    this.appComponent.handleSelectClick(selectElement);
  }
  ngOnInit() {
    this.editor = new Editor();
    this.entryForm = this.formBuilder.group({
      id: [this.id],
      courseId: [null, [Validators.required]],
      instructions: [""],
      duration: [null, [Validators.required]],
      mcqOnly: [true],
      random: [false],
      publish: [false],
      marks: [null],
      quota: [null, [Validators.required]],
      // no_of_mcq: [null, [Validators.required]],
      // no_of_tfq: [null],
      // no_of_figq: [null],
      // no_of_mq: [null],
      // no_of_wq: [null],
      startDate: [null],
      endDate: [null],
    });
    this.mcqForm = this.formBuilder.group({
      segmentId: [null, [Validators.required]],
    });
    this.tfqForm = this.formBuilder.group({
      segmentId: [null, [Validators.required]],
    });
    this.figqForm = this.formBuilder.group({
      segmentId: [null, [Validators.required]],
    });
    this.mqForm = this.formBuilder.group({
      segmentId: [null, [Validators.required]],
    });
    this.wqForm = this.formBuilder.group({
      segmentId: [null, [Validators.required]],
    });
    this.mcqEditForm = this.formBuilder.group({
      id: [null, [Validators.required]],
      segmentId: [null, [Validators.required]],
      question: [null, [Validators.required]],
      options: [null, [Validators.required]],
      mark: [null, [Validators.required]],
    });
    this.tfqEditForm = this.formBuilder.group({
      id: [null, [Validators.required]],
      segmentId: [null, [Validators.required]],
      question: [null, [Validators.required]],
      answer: [false, [Validators.required]],
      mark: [null, [Validators.required]],
    });
    this.figqEditForm = this.formBuilder.group({
      id: [null, [Validators.required]],
      segmentId: [null, [Validators.required]],
      question: [null, [Validators.required]],
      answer: [null, [Validators.required]],
      mark: [null, [Validators.required]],
    });
    this.mqEditForm = this.formBuilder.group({
      id: [null, [Validators.required]],
      segmentId: [null, [Validators.required]],
      leftSide: [null, [Validators.required]],
      rightSide: [null, [Validators.required]],
      mark: [null, [Validators.required]],
    });

    ///
    // this.entryForm.get("random").valueChanges.subscribe((isChecked) => {
    //         if (isChecked) {
    //           this.entryForm.get("publish").disable();
    //           this.entryForm.get("publish").setValue(false);
    //           this.entryForm.get("mcqOnly").setValue(false);
    //         }
    //       });
    //       this.entryForm.get("mcqOnly").valueChanges.subscribe((isChecked) => {
    //         if (isChecked) {
    //           this.entryForm.get("random").setValue(false);
    //           this.entryForm.get("publish").setValue(true);

    //         }else{
    //           this.entryForm.get("publish").setValue(false);

    //         }
    //       });
    ///

    if (!this.isEdit) this.getMarks();
    this.getCourseList();
    this.getSegmentList();
  }

  ngOnDestroy(): void {
    this.editor.destroy();
  }

  get f() {
    return this.entryForm.controls;
  }

  getMarks() {
    this._service.get("configuration/get-exam-data").subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.entryForm.controls["instructions"].setValue(
          res.Data.ExamInstruction
        );
        this.MCQMark = res.Data.MCQMark;
        this.TrueFalseMark = res.Data.TrueFalseMark;
        this.FIGMark = res.Data.FIGMark;
        this.MatchingMark = res.Data.MatchingMark;
        this.WrittenMark = res.Data.WrittenMark;
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
  }

  formatAMPM(date) {
    var hours = date.getHours();
    var minutes = date.getMinutes();
    var ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    minutes = minutes < 10 ? "0" + minutes : minutes;
    var strTime = hours + ":" + minutes + " " + ampm;
    return strTime;
  }

  getItem() {
    const obj = {
      id: this.id,
    };
    this.blockUI.start("Getting data. Please wait ...");
    this._service.get("exam/certificate-test/get/" + this.id).subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.entryForm.controls["courseId"].setValue(res.Data.CourseId);
        this.entryForm.controls["marks"].setValue(res.Data.Marks);
        this.entryForm.controls["instructions"].setValue(
          res.Data.ExamInstructions
        );
        this.entryForm.controls["quota"].setValue(res.Data.Quota);
        this.entryForm.controls["random"].setValue(res.Data.Random);
        this.entryForm.controls["publish"].setValue(res.Data.Publish);
        this.entryForm.controls["duration"].setValue(res.Data.DurationMnt);
        // this.entryForm.controls['no_of_mcq'].setValue(res.Data.ExamMCQNo);
        // this.entryForm.controls['no_of_tfq'].setValue(res.Data.ExamTrueFalseNo);
        // this.entryForm.controls['no_of_figq'].setValue(res.Data.ExamFIGNo);
        // this.entryForm.controls['no_of_mq'].setValue(res.Data.ExamMatchingNo);
        // this.entryForm.controls['no_of_wq'].setValue(res.Data.ExamWritingNo);
        this.segmentWiseQuestions = res.Data.QsSetups;
        this.totalQuestions = {
          mcq: this.segmentWiseQuestions
            .map((o) => (o.NoOfMCQ ? Number(o.NoOfMCQ) : 0))
            .reduce((a, c) => {
              return a + c;
            }),
          tfq: this.segmentWiseQuestions
            .map((o) => (o.NoOfTrueFalse ? Number(o.NoOfTrueFalse) : 0))
            .reduce((a, c) => {
              return a + c;
            }),
          figq: this.segmentWiseQuestions
            .map((o) => (o.NoOfFIG ? Number(o.NoOfFIG) : 0))
            .reduce((a, c) => {
              return a + c;
            }),
          matchingq: this.segmentWiseQuestions
            .map((o) => (o.NoOfMatching ? Number(o.NoOfMatching) : 0))
            .reduce((a, c) => {
              return a + c;
            }),
          writtenq: this.segmentWiseQuestions
            .map((o) => (o.NoOfWriting ? Number(o.NoOfWriting) : 0))
            .reduce((a, c) => {
              return a + c;
            }),
        };

        this.entryForm.controls["mcqOnly"].setValue(res.Data.MCQOnly);

        // **FIX: Don't double-convert time - backend already returns local time**
        // WHY: Backend now stores local time, so no need to convert from UTC
        // BENEFIT: Prevents time shifting issues when editing exams
        if (res.Data.StartDate)
          this.entryForm.controls["startDate"].setValue(
            moment(res.Data.StartDate).format("yyyy-MM-DDTHH:mm:ss")
          );
        if (res.Data.EndDate)
          this.entryForm.controls["endDate"].setValue(
            moment(res.Data.EndDate).format("yyyy-MM-DDTHH:mm:ss")
          );

        this.btnSaveText = "Update";
        this.formTitle = "Certification Test Update";
        this.getMCQList();
        this.getTFQList();
        this.geWrittenList();
        this.geFIGList();
        this.getMatchingList();
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
    this.submitted = false;
  }

  getCourseList() {
    this._service.get("course/dropdown-list").subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.courseList = res.Data;
      },
      (err) => {}
    );
  }

  onFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid) {
      return;
    }

    if (!this.entryForm.value.duration) {
      this.toastr.warning(
        "Please insert exam duration for this exam",
        "WARNING!",
        { timeOut: 3000 }
      );
      return;
    }

    if (this.segmentWiseQuestions.length === 0) {
      this.toastr.warning("Segment wise question is not set yet.", "WARNING!", {
        timeOut: 3000,
      });
      return;
    }

    this.segmentWiseQuestions.forEach((element) => {
      element.NoOfFIG = element.NoOfFIG ?? 0;
      element.NoOfMCQ = element.NoOfMCQ ?? 0;
      element.NoOfMatching = element.NoOfMatching ?? 0;
      element.NoOfTrueFalse = element.NoOfTrueFalse ?? 0;
      element.NoOfWriting = element.NoOfWriting ?? 0;
    });

    const obj = {
      Id: this.entryForm.value.id ? this.entryForm.value.id : null,
      CourseId: this.entryForm.value.courseId,
      ExamInstructions: this.entryForm.value.instructions
        ? this.entryForm.value.instructions.trim()
        : "",
      Quota: Number(this.entryForm.value.quota),
      Random: Number(this.entryForm.value.random),
      Publish: Number(this.entryForm.value.publish),
      Marks: Number(this.entryForm.value.marks),
      // ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,
      // ExamTrueFalseNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,
      // ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,
      // ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,
      // ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,
      MCQOnly: this.entryForm.value.mcqOnly,
      // MinPercentageForCertification: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,
      // MinMarksForCertification: Number(this.entryForm.value.minMarks),
      SegmentWiseSetups: this.segmentWiseQuestions,
      DurationMnt: Number(this.entryForm.value.duration),
      QuesType: "NoQues",
      StartDate: this.entryForm.value.startDate
        ? moment(this.entryForm.value.startDate).toISOString()
        : "",
      EndDate: this.entryForm.value.endDate
        ? moment(this.entryForm.value.endDate).toISOString()
        : "",
    };

    const formdata = new FormData();
    formdata.append("Model", JSON.stringify(obj));

    this.blockUI.start("Saving data. Please wait...");
    this._service
      .post("exam/certificate-test/create-or-update", formdata)
      .subscribe(
        (res) => {
          this.blockUI.stop();
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }

          this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
          this.entryForm.controls["id"].setValue(res.Data);
          this.btnSaveText = "Update";
          this.id = res.Data;
          this.isEdit = true;
        },
        (err) => {
          this.blockUI.stop();
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  clearForm() {
    this.entryForm.reset();
    this.submitted = false;
    this.formTitle = "Create Certification Test";
    this.btnSaveText = "Save";
    this.entryForm.controls["mcqOnly"].setValue(true);
  }

  deleteQuestion(id: number, type: string) {
    let url = "";
    switch (type) {
      case "MCQ":
        url = "exam/certificate-test/delete-mcq/" + id;
        break;
      case "TFQ":
        url = "exam/certificate-test/delete-true-false/" + id;
        break;
      case "FIGQ":
        url = "exam/certificate-test/delete-fill-in-the-gap/" + id;
        break;
      case "WQ":
        url = "exam/certificate-test/delete-written/" + id;
        break;
      case "MatchingQ":
        url = "exam/certificate-test/delete-matching/" + id;
        break;
    }
    this.blockUI.start("Deleting data. Please wait ...");
    this._service.get(url).subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.toastr.success(res.Message, "SUCCESS!", { timeOut: 2000 });
        switch (type) {
          case "MCQ":
            this.getMCQList();
            break;
          case "TFQ":
            this.getTFQList();
            break;
          case "FIGQ":
            this.geFIGList();
            break;
          case "MatchingQ":
            this.getMatchingList();
            break;
          case "WQ":
            this.geWrittenList();
            break;
        }
      },
      (err) => {
        this.toastr.error(err.message || err, "Error!", { timeOut: 10000 });
        this.blockUI.stop();
      }
    );
  }

  downloadQuestionList(type) {
    let url = "",
      title = "";
    let timeZoneOffset = new Date().getTimezoneOffset();
    switch (type) {
      case "MCQ":
        url =
          "exam/certificate-test/get-mcq-list-excel/" +
          this.id +
          "/" +
          timeZoneOffset;
        title = "certification_test_mcq_file.xlsx";
        break;
      case "TFQ":
        url =
          "exam/certificate-test/get-true-false-list-excel/" +
          this.id +
          "/" +
          timeZoneOffset;
        title = "certification_test_true_false_file.xlsx";
        break;
      case "FIGQ":
        url =
          "exam/certificate-test/get-fill-in=the=gap-list-excel/" +
          this.id +
          "/" +
          timeZoneOffset;
        title = "certification_test_fill_in_the_gap_file.xlsx";
        break;
      case "MatchingQ":
        url =
          "exam/certificate-test/get-matching-list-excel/" +
          this.id +
          "/" +
          timeZoneOffset;
        title = "certification_test_left_right_matching_file.xlsx";
        break;
      case "WQ":
        url =
          "exam/certificate-test/get-written-list-excel/" +
          this.id +
          "/" +
          timeZoneOffset;
        title = "certification_test_written_file.xlsx";
        break;

      default:
        break;
    }
    this.blockUI.start("Generating excel file. Please wait ...");
    return this._service.downloadFile(url).subscribe(
      (res) => {
        this.blockUI.stop();
        const url = window.URL.createObjectURL(res);
        var link = document.createElement("a");
        link.href = url;
        link.download = title;
        link.click();
      },
      (error) => {
        this.toastr.error(error.message || error, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
  }

  // ============== MCQ Question ====================
  getMCQList() {
    const obj = {
      examId: this.id,
      // examId: this.entryForm.value.examId,
      // setId: this.entryForm.value.setId
    };

    this.loadingMCQ = true;
    this._service
      .get("exam/certificate-test/get-mcq-list/" + this.id)
      .subscribe(
        (res) => {
          setTimeout(() => {
            this.loadingMCQ = false;
          }, 1000);

          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.mcqList = res.Data;
          // let answers = [];
          // res.Data.forEach(element => {
          //   answers = element.Answers.split(',');
          //   this.mcqList.push({
          //     Question: element.Question,
          //     Options: [{ Text: element.Option1, Selected: answers.indexOf('0') !== -1 },
          //     { Text: element.Option2, Selected: answers.indexOf('1') !== -1 },
          //     { Text: element.Option3, Selected: answers.indexOf('2') !== -1 },
          //     { Text: element.Option4, Selected: answers.indexOf('3') !== -1 }],
          //     Mark: element.Mark
          //   });
          // });
        },
        (err) => {
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          setTimeout(() => {
            this.loadingMCQ = false;
          }, 1000);
        }
      );
  }

  openMCQModal(template: TemplateRef<any>) {
    // let answers = [];
    // if (this.mcqList.length === 0) {
    //   this.btnMCQSaveText = 'Save';
    //   this.addNewMCQ();
    // } else {
    //   this.mcqList.forEach(element => {
    //     answers = element.Answers.split(',');
    //     this.mcqQuestionList.push({
    //       Question: element.Question,
    //       Options: [{ Text: element.Option1, Selected: answers.indexOf('1') !== -1 },
    //       { Text: element.Option2, Selected: answers.indexOf('2') !== -1 },
    //       { Text: element.Option3, Selected: answers.indexOf('3') !== -1 },
    //       { Text: element.Option4, Selected: answers.indexOf('4') !== -1 }],
    //       Mark: element.Mark
    //     });
    //   });
    // }

    this.btnMCQSaveText = "Save";
    this.addNewMCQ();
    this.modalMCQRef = this.modalService.show(template, this.modalConfig);
  }

  modalMCQHide() {
    this.modalMCQRef.hide();
    this.mcqQuestionList = [];
    this.mcqFile = null;
    this.mcqForm.reset();
  }

  deleteMCQ(index: number) {
    this.mcqQuestionList.splice(index, 1);
  }

  addNewMCQ() {
    this.mcqQuestionList.push({
      Question: null,
      Options: [
        { Text: null, Selected: false },
        { Text: null, Selected: false },
        { Text: null, Selected: false },
        { Text: null, Selected: false },
      ],
      Mark: this.MCQMark,
    });
  }

  downloadSampleMCQ() {
    return this._service
      .downloadFile("exam/download-sample-question", { quesType: "MCQ" })
      .subscribe(
        (res) => {
          const url = window.URL.createObjectURL(res);
          var link = document.createElement("a");
          link.href = url;
          link.download = "certification_test_mcq_sample_file.xlsx";
          link.click();
        },
        (error) => {
          this.toastr.error(error.message || error, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  onMCQFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid || this.mcqForm.invalid) {
      this.toastr.warning(
        "Please fill up all the details of the exam first",
        "WARNING!",
        { timeOut: 3000 }
      );
      return;
    }

    if (!this.entryForm.value.duration) {
      this.toastr.warning(
        "Please insert exam duration for this exam",
        "WARNING!",
        { timeOut: 3000 }
      );
      return;
    }

    if (this.segmentWiseQuestions.length === 0) {
      this.toastr.warning("Segment wise question is not set yet.", "WARNING!", {
        timeOut: 3000,
      });
      return;
    }

    let questions = [];
    for (let i = 0; i < this.mcqQuestionList.length; i++) {
      const element = this.mcqQuestionList[i];

      if (!element.Question) {
        this.toastr.error(
          "Please Enter Question for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      // if (!element.Options[0].Text || !element.Options[1].Text || !element.Options[2].Text || !element.Options[3].Text) {
      if (
        element.Options.filter((x) => x.Text === null || x.Text === "").length >
        0
      ) {
        this.toastr.error(
          "Value missing for an option of Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      // if (!element.Option1.Selected && !element.Option1.Selected && !element.Option1.Selected && !element.Option1.Selected) {
      if (element.Options.filter((x) => x.Selected).length === 0) {
        this.toastr.error(
          "No option has been selected for answer of Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      if (!element.Mark) {
        this.toastr.error(
          "Please enter mark for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      questions.push({
        Question: element.Question,
        Mark: element.Mark,
        Option1: element.Options[0].Text,
        Option2: element.Options[1].Text,
        Option3: element.Options[2].Text,
        Option4: element.Options[3].Text,
        Answers: element.Options.map(function (x, i) {
          if (x.Selected) return i + 1;
          else return -1;
        })
          .filter((x) => x >= 0)
          .join(),
      });
    }

    this.segmentWiseQuestions.forEach((element) => {
      element.NoOfFIG = element.NoOfFIG ?? 0;
      element.NoOfMCQ = element.NoOfMCQ ?? 0;
      element.NoOfMatching = element.NoOfMatching ?? 0;
      element.NoOfTrueFalse = element.NoOfTrueFalse ?? 0;
      element.NoOfWriting = element.NoOfWriting ?? 0;
    });

    const obj = {
      Id: this.entryForm.value.id ? this.entryForm.value.id : null,
      CourseId: this.entryForm.value.courseId,
      ExamInstructions: this.entryForm.value.instructions
        ? this.entryForm.value.instructions.trim()
        : "",
      Quota: Number(this.entryForm.value.quota),
      Random: Number(this.entryForm.value.random),
      Publish: Number(this.entryForm.value.publish),
      Marks: Number(this.entryForm.value.marks),
      // ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,
      // ExamTrueFalseNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,
      // ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,
      // ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,
      // ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,
      MCQOnly: this.entryForm.value.mcqOnly,
      SegmentWiseSetups: this.segmentWiseQuestions,
      // MinPercentageForCertification: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,
      // MinMarksForCertification: Number(this.entryForm.value.minMarks),
      DurationMnt: Number(this.entryForm.value.duration),
      SegmentId: this.mcqForm.value.segmentId,
      MCQs: questions,
      QuesType: "MCQ",
      StartDate: this.entryForm.value.startDate
        ? moment(this.entryForm.value.startDate).toISOString()
        : "",
      EndDate: this.entryForm.value.endDate
        ? moment(this.entryForm.value.endDate).toISOString()
        : "",
    };
    console.log("obj", obj);
    const formdata = new FormData();
    formdata.append("Model", JSON.stringify(obj));
    if (this.mcqFile) {
      formdata.append("MCQFile", this.mcqFile);
    }

    this.blockUI.start("Saving data. Please wait...");
    this._service
      .post("exam/certificate-test/create-or-update", formdata)
      .subscribe(
        (res) => {
          this.blockUI.stop();
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }

          this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
          this.entryForm.controls["id"].setValue(res.Data);
          this.btnSaveText = "Update";
          this.id = res.Data;
          this.isEdit = true;
          this.getMCQList();
          this.modalMCQHide();
        },
        (err) => {
          this.blockUI.stop();
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  loadMCQFile(files) {
    if (files.length === 0) return;
    this.mcqFile = files[0];
  }

  resetMCQFile(element) {
    element.value = "";
    this.mcqFile = null;
  }

  // ============== True/False Question ====================
  getTFQList() {
    const obj = {
      examId: this.id,
    };

    this.loadingTFQ = true;
    this._service
      .get("exam/certificate-test/get-true-false-list/" + this.id)
      .subscribe(
        (res) => {
          setTimeout(() => {
            this.loadingTFQ = false;
          }, 1000);

          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.truFalseList = res.Data;
        },
        (err) => {
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          setTimeout(() => {
            this.loadingTFQ = false;
          }, 1000);
        }
      );
  }

  openTFQModal(template: TemplateRef<any>) {
    // if (this.truFalseList.length === 0) {
    //   this.btnTFQSaveText = 'Save';
    //   this.addNewTFQ();
    // } else {
    //   this.truFalseList.forEach(element => {
    //     this.tfQuestionList.push({
    //       Question: element.Question,
    //       Answer: element.Answer,
    //       CorrectAnswer: element.CorrectAnswer,
    //       Mark: element.Mark
    //     });
    //   });
    // }
    this.btnTFQSaveText = "Save";
    this.addNewTFQ();
    this.modalTFRef = this.modalService.show(template, this.modalConfig);
  }

  modalTFQHide() {
    this.modalTFRef.hide();
    this.tfQuestionList = [];
    this.tfqFile = null;
    this.tfqForm.reset();
  }

  deleteTFQ(index: number) {
    this.tfQuestionList.splice(index, 1);
  }

  addNewTFQ() {
    this.tfQuestionList.push({
      Id: null,
      Question: null,
      Answer: true,
      CorrectAnswer: null,
      Mark: this.TrueFalseMark,
    });
  }

  onTFQFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid || this.tfqForm.invalid) {
      return;
    }

    if (this.segmentWiseQuestions.length === 0) {
      this.toastr.warning("Segment wise question is not set yet.", "WARNING!", {
        timeOut: 3000,
      });
      return;
    }

    for (let i = 0; i < this.tfQuestionList.length; i++) {
      const element = this.tfQuestionList[i];

      if (!element.Question) {
        this.toastr.error(
          "Please Enter Question for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      // if (!element.Answer && !element.CorrectAnswer) {
      //   this.toastr.error('Please enter correct answer for Question: ' + ++i, 'WARNING!', { timeOut: 4000 });
      //   return false;
      // }

      if (!element.Mark) {
        this.toastr.error(
          "Please enter mark for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }
    }

    this.segmentWiseQuestions.forEach((element) => {
      element.NoOfFIG = element.NoOfFIG ?? 0;
      element.NoOfMCQ = element.NoOfMCQ ?? 0;
      element.NoOfMatching = element.NoOfMatching ?? 0;
      element.NoOfTrueFalse = element.NoOfTrueFalse ?? 0;
      element.NoOfWriting = element.NoOfWriting ?? 0;
    });

    const obj = {
      Id: this.entryForm.value.id ? this.entryForm.value.id : null,
      CourseId: this.entryForm.value.courseId,
      ExamInstructions: this.entryForm.value.instructions
        ? this.entryForm.value.instructions.trim()
        : "",
      Quota: Number(this.entryForm.value.quota),
      Random: Number(this.entryForm.value.random),
      Publish: Number(this.entryForm.value.publish),
      Marks: Number(this.entryForm.value.marks),
      // ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,
      // ExamTrueFalseNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,
      // ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,
      // ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,
      // ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,
      MCQOnly: this.entryForm.value.mcqOnly,
      SegmentWiseSetups: this.segmentWiseQuestions,
      // MinPercentageForCertification: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,
      // MinMarksForCertification: Number(this.entryForm.value.minMarks),
      DurationMnt: Number(this.entryForm.value.duration),
      SegmentId: this.tfqForm.value.segmentId,
      TruFalseQs: this.tfQuestionList,
      QuesType: "TrueFalse",
      StartDate: this.entryForm.value.startDate
        ? moment(this.entryForm.value.startDate).toISOString()
        : "",
      EndDate: this.entryForm.value.endDate
        ? moment(this.entryForm.value.endDate).toISOString()
        : "",
    };

    const formdata = new FormData();
    formdata.append("Model", JSON.stringify(obj));
    if (this.tfqFile) {
      formdata.append("TFQFile", this.tfqFile);
    }

    this.blockUI.start("Saving data. Please wait...");
    this._service
      .post("exam/certificate-test/create-or-update", formdata)
      .subscribe(
        (res) => {
          this.blockUI.stop();
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
          this.entryForm.controls["id"].setValue(res.Data);
          this.btnSaveText = "Update";
          this.id = res.Data;
          this.isEdit = true;
          this.getTFQList();
          this.modalTFQHide();
        },
        (err) => {
          this.blockUI.stop();
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  downloadSampleTFQ() {
    return this._service
      .downloadFile("exam/download-sample-question", { quesType: "TrueFalse" })
      .subscribe(
        (res) => {
          const url = window.URL.createObjectURL(res);
          var link = document.createElement("a");
          link.href = url;
          link.download = "certification_test_true_false_sample_file.xlsx";
          link.click();
        },
        (error) => {
          this.toastr.error(error.message || error, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  loadTFQFile(files) {
    if (files.length === 0) return;
    this.tfqFile = files[0];
  }

  resetTFQFile(element) {
    element.value = "";
    this.tfqFile = null;
  }

  // ============== Fill in the gaps Question ====================
  geFIGList() {
    const obj = {
      examId: this.id,
    };

    this.loadingFIG = true;
    this._service
      .get("exam/certificate-test/get-fill-in-the-gap-list/" + this.id)
      .subscribe(
        (res) => {
          setTimeout(() => {
            this.loadingFIG = false;
          }, 1000);

          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.figList = res.Data;
        },
        (err) => {
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          setTimeout(() => {
            this.loadingFIG = false;
          }, 1000);
        }
      );
  }

  openFIGModal(template: TemplateRef<any>) {
    // if (this.figList.length === 0) {
    //   this.btnFIGSaveText = 'Save';
    //   this.addNewFIG();
    // } else {
    //   this.figList.forEach(element => {
    //     this.figQuestionList.push({
    //       Question: element.Question,
    //       Answer: element.Answer,
    //       //Serial: element.Serial,
    //       Mark: element.Mark
    //     });
    //   });
    // }

    this.btnFIGSaveText = "Save";
    this.addNewFIG();
    this.modalFIGRef = this.modalService.show(template, this.modalConfig);
  }

  modalFIGHide() {
    this.modalFIGRef.hide();
    this.figQuestionList = [];
    this.figqFile = null;
    this.figqForm.reset();
  }

  deleteFIG(index: number) {
    this.figQuestionList.splice(index, 1);
  }

  addNewFIG() {
    this.figQuestionList.push({
      Question: null,
      Answer: null,
      //Serial: null,
      Mark: this.FIGMark,
    });
  }

  onFIGFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid || this.figqForm.invalid) {
      return;
    }

    if (this.segmentWiseQuestions.length === 0) {
      this.toastr.warning("Segment wise question is not set yet.", "WARNING!", {
        timeOut: 3000,
      });
      return;
    }

    for (let i = 0; i < this.figQuestionList.length; i++) {
      const element = this.figQuestionList[i];

      if (!element.Question) {
        this.toastr.error(
          "Please Enter Question for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      if (!element.Answer) {
        this.toastr.error(
          "Please enter answer for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      if (!element.Mark) {
        this.toastr.error(
          "Please enter mark for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }
    }

    this.segmentWiseQuestions.forEach((element) => {
      element.NoOfFIG = element.NoOfFIG ?? 0;
      element.NoOfMCQ = element.NoOfMCQ ?? 0;
      element.NoOfMatching = element.NoOfMatching ?? 0;
      element.NoOfTrueFalse = element.NoOfTrueFalse ?? 0;
      element.NoOfWriting = element.NoOfWriting ?? 0;
    });

    const obj = {
      Id: this.entryForm.value.id ? this.entryForm.value.id : null,
      CourseId: this.entryForm.value.courseId,
      ExamInstructions: this.entryForm.value.instructions
        ? this.entryForm.value.instructions.trim()
        : "",
      Quota: Number(this.entryForm.value.quota),
      Random: Number(this.entryForm.value.random),
      Publish: Number(this.entryForm.value.publish),
      Marks: Number(this.entryForm.value.marks),
      // ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,
      // ExamTrueFalseNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,
      // ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,
      // ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,
      // ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,
      MCQOnly: this.entryForm.value.mcqOnly,
      SegmentWiseSetups: this.segmentWiseQuestions,
      // MinPercentageForCertification: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,
      // MinMarksForCertification: Number(this.entryForm.value.minMarks),
      DurationMnt: Number(this.entryForm.value.duration),
      SegmentId: this.figqForm.value.segmentId,
      FIGQs: this.figQuestionList,
      QuesType: "FIG",
      StartDate: this.entryForm.value.startDate
        ? moment(this.entryForm.value.startDate).toISOString()
        : "",
      EndDate: this.entryForm.value.endDate
        ? moment(this.entryForm.value.endDate).toISOString()
        : "",
    };

    const formdata = new FormData();
    formdata.append("Model", JSON.stringify(obj));
    if (this.figqFile) {
      formdata.append("FIGQFile", this.figqFile);
    }

    this.blockUI.start("Saving data. Please wait...");
    this._service
      .post("exam/certificate-test/create-or-update", formdata)
      .subscribe(
        (res) => {
          this.blockUI.stop();
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
          this.entryForm.controls["id"].setValue(res.Data);
          this.btnSaveText = "Update";
          this.id = res.Data;
          this.isEdit = true;
          this.geFIGList();
          this.modalFIGHide();
        },
        (err) => {
          this.blockUI.stop();
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  downloadSampleFIGQ() {
    return this._service
      .downloadFile("exam/download-sample-question", { quesType: "FIG" })
      .subscribe(
        (res) => {
          const url = window.URL.createObjectURL(res);
          var link = document.createElement("a");
          link.href = url;
          link.download = "certification_test_fill_in_the_gap_sample_file.xlsx";
          link.click();
        },
        (error) => {
          this.toastr.error(error.message || error, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  loadFIGQFile(files) {
    if (files.length === 0) return;
    this.figqFile = files[0];
  }

  resetFIGQFile(element) {
    element.value = "";
    this.figqFile = null;
  }

  // ============== Matching Question ====================
  getMatchingList() {
    const obj = {
      examId: this.id,
    };

    this.loadingMatching = true;
    this._service
      .get("exam/certificate-test/get-matching-list/" + this.id)
      .subscribe(
        (res) => {
          setTimeout(() => {
            this.loadingMatching = false;
          }, 1000);

          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.matchingList = res.Data;
        },
        (err) => {
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          setTimeout(() => {
            this.loadingMatching = false;
          }, 1000);
        }
      );
  }

  openMatchingModal(template: TemplateRef<any>) {
    // if (this.matchingList.length === 0) {
    //   this.btnMatchingSaveText = 'Save';
    //   this.addNewMatching();
    // } else {
    //   this.matchingList.forEach(element => {
    //     this.matchingQuestionList.push({
    //       LeftSide: element.LeftSide,
    //       RightSide: element.RightSide,
    //       Mark: element.Mark
    //     });
    //   });
    // }
    this.btnMatchingSaveText = "Save";
    this.addNewMatching();
    this.modalMatchingRef = this.modalService.show(template, this.modalConfig);
  }

  modalMatchingHide() {
    this.modalMatchingRef.hide();
    this.matchingQuestionList = [];
    this.matchingqFile = null;
    this.mqForm.reset();
  }

  deleteMatching(index: number) {
    this.matchingQuestionList.splice(index, 1);
  }

  addNewMatching() {
    this.matchingQuestionList.push({
      LeftSide: null,
      RightSide: null,
      Mark: this.MatchingMark,
    });
  }

  onMatchingFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid || this.mqForm.invalid) {
      return;
    }

    if (this.segmentWiseQuestions.length === 0) {
      this.toastr.warning("Segment wise question is not set yet.", "WARNING!", {
        timeOut: 3000,
      });
      return;
    }

    for (let i = 0; i < this.matchingQuestionList.length; i++) {
      const element = this.matchingQuestionList[i];

      if (!element.LeftSide) {
        this.toastr.error(
          "Please Enter Question for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      if (!element.RightSide) {
        this.toastr.error(
          "Please enter right hand side for left hand side: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      if (!element.Mark) {
        this.toastr.error(
          "Please enter mark for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }
    }

    this.segmentWiseQuestions.forEach((element) => {
      element.NoOfFIG = element.NoOfFIG ?? 0;
      element.NoOfMCQ = element.NoOfMCQ ?? 0;
      element.NoOfMatching = element.NoOfMatching ?? 0;
      element.NoOfTrueFalse = element.NoOfTrueFalse ?? 0;
      element.NoOfWriting = element.NoOfWriting ?? 0;
    });

    const obj = {
      Id: this.entryForm.value.id ? this.entryForm.value.id : null,
      CourseId: this.entryForm.value.courseId,
      ExamInstructions: this.entryForm.value.instructions
        ? this.entryForm.value.instructions.trim()
        : "",
      Quota: Number(this.entryForm.value.quota),
      Random: Number(this.entryForm.value.random),
      Publish: Number(this.entryForm.value.publish),
      Marks: Number(this.entryForm.value.marks),
      // ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,
      // ExamTrueFalseNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,
      // ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,
      // ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,
      // ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,
      MCQOnly: this.entryForm.value.mcqOnly,
      SegmentWiseSetups: this.segmentWiseQuestions,
      // MinPercentageForCertification: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,
      // MinMarksForCertification: Number(this.entryForm.value.minMarks),
      DurationMnt: Number(this.entryForm.value.duration),
      SegmentId: this.mqForm.value.segmentId,
      MatchingQs: this.matchingQuestionList,
      QuesType: "Matching",
      StartDate: this.entryForm.value.startDate
        ? moment(this.entryForm.value.startDate).toISOString()
        : "",
      EndDate: this.entryForm.value.endDate
        ? moment(this.entryForm.value.endDate).toISOString()
        : "",
    };

    const formdata = new FormData();
    formdata.append("Model", JSON.stringify(obj));
    if (this.matchingqFile) {
      formdata.append("MatchingQFile", this.matchingqFile);
    }

    this.blockUI.start("Saving data. Please wait...");
    this._service
      .post("exam/certificate-test/create-or-update", formdata)
      .subscribe(
        (res) => {
          this.blockUI.stop();
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
          this.entryForm.controls["id"].setValue(res.Data);
          this.btnSaveText = "Update";
          this.id = res.Data;
          this.isEdit = true;
          this.getMatchingList();
          this.modalMatchingHide();
        },
        (err) => {
          this.blockUI.stop();
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  downloadSampleMatchingQ() {
    return this._service
      .downloadFile("exam/download-sample-question", { quesType: "Matching" })
      .subscribe(
        (res) => {
          const url = window.URL.createObjectURL(res);
          var link = document.createElement("a");
          link.href = url;
          link.download =
            "certification_test_left_right_matching_sample_file.xlsx";
          link.click();
        },
        (error) => {
          this.toastr.error(error.message || error, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  loadMatchingQFile(files) {
    if (files.length === 0) return;
    this.matchingqFile = files[0];
  }

  resetMatchingQFile(element) {
    element.value = "";
    this.matchingqFile = null;
  }

  // ============== Written Question ====================
  geWrittenList() {
    this.loadingWritten = true;
    this._service
      .get("exam/certificate-test/get-written-list/" + this.id)
      .subscribe(
        (res) => {
          setTimeout(() => {
            this.loadingWritten = false;
          }, 1000);

          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.writtenList = res.Data;
        },
        (err) => {
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          setTimeout(() => {
            this.loadingWritten = false;
          }, 1000);
        }
      );
  }

  openWrittenModal(template: TemplateRef<any>) {
    // if (this.writtenList.length === 0) {
    //   this.btnWrittenSaveText = 'Save';
    //   this.addNewWritten();
    // } else {
    //   this.writtenList.forEach(element => {
    //     this.writtenQuestionList.push({
    //       Question: element.Question,
    //       Mark: element.Mark
    //     });
    //   });
    // }
    this.btnWrittenSaveText = "Save";
    this.addNewWritten();
    this.modalWrittenRef = this.modalService.show(template, this.modalConfig);
  }

  modalWrittenHide() {
    this.modalWrittenRef.hide();
    this.writtenQuestionList = [];
    this.wqFile = null;
    this.wqForm.reset();
  }

  deleteWritten(index: number) {
    this.writtenQuestionList.splice(index, 1);
  }

  addNewWritten() {
    this.writtenQuestionList.push({
      Question: null,
      Mark: this.WrittenMark,
    });
  }

  onWrittenFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid || this.wqForm.invalid) {
      return;
    }

    if (this.segmentWiseQuestions.length === 0) {
      this.toastr.warning("Segment wise question is not set yet.", "WARNING!", {
        timeOut: 3000,
      });
      return;
    }

    for (let i = 0; i < this.writtenQuestionList.length; i++) {
      const element = this.writtenQuestionList[i];

      if (!element.Question) {
        this.toastr.error(
          "Please Enter Question for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }

      if (!element.Mark) {
        this.toastr.error(
          "Please enter mark for Question: " + ++i,
          "WARNING!",
          { timeOut: 4000 }
        );
        return false;
      }
    }

    this.segmentWiseQuestions.forEach((element) => {
      element.NoOfFIG = element.NoOfFIG ?? 0;
      element.NoOfMCQ = element.NoOfMCQ ?? 0;
      element.NoOfMatching = element.NoOfMatching ?? 0;
      element.NoOfTrueFalse = element.NoOfTrueFalse ?? 0;
      element.NoOfWriting = element.NoOfWriting ?? 0;
    });

    const obj = {
      Id: this.entryForm.value.id ? this.entryForm.value.id : null,
      CourseId: this.entryForm.value.courseId,
      ExamInstructions: this.entryForm.value.instructions
        ? this.entryForm.value.instructions.trim()
        : "",
      Quota: Number(this.entryForm.value.quota),
      Random: Number(this.entryForm.value.random),
      Publish: Number(this.entryForm.value.publish),
      Marks: Number(this.entryForm.value.marks),
      // ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,
      // ExamTrueFalseNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,
      // ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,
      // ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,
      // ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,
      MCQOnly: this.entryForm.value.mcqOnly,
      SegmentWiseSetups: this.segmentWiseQuestions,
      // MinPercentageForCertification: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,
      // MinMarksForCertification: Number(this.entryForm.value.minMarks),
      DurationMnt: Number(this.entryForm.value.duration),
      SegmentId: this.wqForm.value.segmentId,
      WrittenQs: this.writtenQuestionList,
      QuesType: "Written",
      StartDate: this.entryForm.value.startDate
        ? moment(this.entryForm.value.startDate).toISOString()
        : "",
      EndDate: this.entryForm.value.endDate
        ? moment(this.entryForm.value.endDate).toISOString()
        : "",
    };

    const formdata = new FormData();
    formdata.append("Model", JSON.stringify(obj));
    if (this.wqFile) {
      formdata.append("WQFile", this.wqFile);
    }

    this.blockUI.start("Saving data. Please wait...");
    this._service
      .post("exam/certificate-test/create-or-update", formdata)
      .subscribe(
        (res) => {
          this.blockUI.stop();
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
          this.entryForm.controls["id"].setValue(res.Data);
          this.btnSaveText = "Update";
          this.id = res.Data;
          this.isEdit = true;
          this.geWrittenList();
          this.modalWrittenHide();
        },
        (err) => {
          this.blockUI.stop();
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  downloadSampleWQ() {
    return this._service
      .downloadFile("exam/download-sample-question", { quesType: "Written" })
      .subscribe(
        (res) => {
          const url = window.URL.createObjectURL(res);
          var link = document.createElement("a");
          link.href = url;
          link.download =
            "certification_test_written_question_sample_file.xlsx";
          link.click();
        },
        (error) => {
          this.toastr.error(error.message || error, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      );
  }

  loadWQFile(files) {
    if (files.length === 0) return;
    this.wqFile = files[0];
  }

  resetWQFile(element) {
    element.value = "";
    this.wqFile = null;
  }

  modalHide() {
    this.modalRef.hide();
    this.submitted = false;
  }

  openModal(template: TemplateRef<any>) {
    // this.entryForm.controls['isActive'].setValue(true);
    this.modalRef = this.modalService.show(template, this.modalConfig);
  }

  OnCourseChange(event) {
    this.ExamExistsMsg = "";
    this.loadingWritten = true;
    this._service.get("course/has-certificate-exam/" + event.Id).subscribe(
      (res) => {
        setTimeout(() => {
          this.loadingWritten = false;
        }, 1000);

        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.ExamExists = res.Data;
        if (this.ExamExists)
          this.ExamExistsMsg =
            "There is already an exam for this course. You cannot add more. Try another course.";
      },
      (err) => {
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
        setTimeout(() => {
          this.loadingWritten = false;
        }, 1000);
      }
    );
  }

  getSegmentList() {
    this._service.get("course-segment/dropdown-list").subscribe({
      next: (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.segmentList = res.Data;
      },
      error: (err) =>
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        }),
      complete: () => {},
    });
  }

  // =================== Segment Wise Question Setup =======================
  onChangeSegment(segment, item, idx: number) {
    this.segmentWiseQuestionsInput.forEach((element, i) => {
      if (i !== idx && element.SegmentId === segment.Id) {
        this.toastr.warning("Already exist", "WARNING!");
        setTimeout(() => {
          item.SegmentId = null;
        }, 200);
        return false;
      }
    });
    item.SegmentName = segment ? segment.Name : null;
  }

  openQuestionSetupModal(template: TemplateRef<any>) {
    this.segmentWiseQuestions.forEach((element) => {
      this.segmentWiseQuestionsInput.push(element);
    });
    if (this.segmentWiseQuestionsInput.length === 0)
      this.addRowIntoQsSetupTable();
    this.modalRef = this.modalService.show(template, {
      class: "gray modal-xl",
      backdrop: "static",
    });
  }

  modalQsSetupHide() {
    this.segmentWiseQuestionsInput = [];
    this.modalRef.hide();
  }

  addRowIntoQsSetupTable() {
    this.segmentWiseQuestionsInput.push({
      SegmentId: null,
      SegmentName: null,
      NoOfMCQ: null,
      NoOfTrueFalse: null,
      NoOfFIG: null,
      NoOfMatching: null,
      NoOfWriting: null,
    });
  }

  deleteRowFromQsSetupTable(idx: number) {
    this.segmentWiseQuestionsInput.splice(idx, 1);
  }

  saveSegmentWiseQuestionSetup() {
    this.segmentWiseQuestionsInput.forEach((element, idx) => {
      if (
        !element.SegmentId ||
        (!element.NoOfMCQ &&
          !element.NoOfTrueFalse &&
          !element.NoOfFIG &&
          !element.NoOfMatching &&
          !element.NoOfWriting)
      ) {
        this.toastr.warning("Invalid data in row: " + (idx + 1), "WARNING!!");
        return false;
      }
    });
    this.segmentWiseQuestions = this.segmentWiseQuestionsInput;
    this.totalQuestions = {
      mcq: this.segmentWiseQuestions
        .map((o) => (o.NoOfMCQ ? Number(o.NoOfMCQ) : 0))
        .reduce((a, c) => {
          return a + c;
        }),
      tfq: this.segmentWiseQuestions
        .map((o) => (o.NoOfTrueFalse ? Number(o.NoOfTrueFalse) : 0))
        .reduce((a, c) => {
          return a + c;
        }),
      figq: this.segmentWiseQuestions
        .map((o) => (o.NoOfFIG ? Number(o.NoOfFIG) : 0))
        .reduce((a, c) => {
          return a + c;
        }),
      matchingq: this.segmentWiseQuestions
        .map((o) => (o.NoOfMatching ? Number(o.NoOfMatching) : 0))
        .reduce((a, c) => {
          return a + c;
        }),
      writtenq: this.segmentWiseQuestions
        .map((o) => (o.NoOfWriting ? Number(o.NoOfWriting) : 0))
        .reduce((a, c) => {
          return a + c;
        }),
    };
    this.modalQsSetupHide();
  }
  // =================== End of Segment Wise Question Setup =======================

  // =================== MCQ Edit =======================
  private addOptions(preset?) {
    return [
      {
        Text: preset ? preset.Option1 : null,
        Selected: preset && preset.Answers.indexOf("1") !== -1,
      },
      {
        Text: preset ? preset.Option2 : null,
        Selected: preset && preset.Answers.indexOf("2") !== -1,
      },
      {
        Text: preset ? preset.Option3 : null,
        Selected: preset && preset.Answers.indexOf("3") !== -1,
      },
      {
        Text: preset ? preset.Option4 : null,
        Selected: preset && preset.Answers.indexOf("4") !== -1,
      },
    ];
  }

  editMCQ(item, template: TemplateRef<any>) {
    console.log(item);
    this.mcqEditForm.controls["id"].setValue(item.Id);
    this.mcqEditForm.controls["question"].setValue(item.Question);
    this.mcqEditForm.controls["segmentId"].setValue(item.SegmentId);
    this.mcqEditForm.controls["mark"].setValue(item.Mark);
    this.mcqEditForm.controls["options"].setValue(this.addOptions(item));
    this.modalRef = this.modalService.show(template, {
      class: "gray modal-lg",
      backdrop: "static",
    });
  }

  modalEditMCQHide() {
    this.mcqEditForm.reset({
      options: this.addOptions(),
    });
    this.modalRef.hide();
    this.editsubmitted = false;
  }

  onMCQUpdate() {
    this.editsubmitted = true;
    if (this.mcqEditForm.invalid) return;

    if (
      this.mcqEditForm.value.options.filter(
        (x) => x.Text === null || x.Text === ""
      ).length > 0
    ) {
      this.toastr.error("Value missing for an option", "WARNING!", {
        timeOut: 4000,
      });
      return false;
    }

    if (this.mcqEditForm.value.options.filter((x) => x.Selected).length === 0) {
      this.toastr.error("No option has been selected for answer", "WARNING!", {
        timeOut: 4000,
      });
      return false;
    }

    const obj = {
      Id: this.mcqEditForm.value.id,
      SegmentId: this.mcqEditForm.value.segmentId,
      Question: this.mcqEditForm.value.question,
      Mark: this.mcqEditForm.value.mark,
      Option1: this.mcqEditForm.value.options[0].Text,
      Option2: this.mcqEditForm.value.options[1].Text,
      Option3: this.mcqEditForm.value.options[2].Text,
      Option4: this.mcqEditForm.value.options[3].Text,
      Answers: this.mcqEditForm.value.options
        .map(function (x, i) {
          if (x.Selected) return i + 1;
          else return -1;
        })
        .filter((x) => x >= 0)
        .join(),
    };

    this.blockUI.start("Updating data. Please wait...");
    this._service.post("exam/certificate-test/mcq/update", obj).subscribe({
      next: (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
        this.modalEditMCQHide();
        this.getMCQList();
      },
      error: (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      },
      complete: () => this.blockUI.stop(),
    });
  }
  // =================== End of MCQ Edit =======================

  // =================== True/False Edit =======================
  editTrueFalse(item, template: TemplateRef<any>) {
    console.log(item);
    this.tfqEditForm.controls["id"].setValue(item.Id);
    this.tfqEditForm.controls["question"].setValue(item.Question);
    this.tfqEditForm.controls["segmentId"].setValue(item.SegmentId);
    this.tfqEditForm.controls["mark"].setValue(item.Mark);
    this.tfqEditForm.controls["answer"].setValue(item.Answer);
    this.modalRef = this.modalService.show(template, {
      class: "gray modal-lg",
      backdrop: "static",
    });
  }

  modalEditTrueFalseHide() {
    this.tfqEditForm.reset({
      answer: false,
    });
    this.modalRef.hide();
    this.editsubmitted = false;
  }

  onTrueFalseUpdate() {
    this.editsubmitted = true;
    if (this.tfqEditForm.invalid) return;

    const obj = {
      Id: this.tfqEditForm.value.id,
      SegmentId: this.tfqEditForm.value.segmentId,
      Question: this.tfqEditForm.value.question,
      Mark: this.tfqEditForm.value.mark,
      Answer: this.tfqEditForm.value.answer,
    };

    this.blockUI.start("Updating data. Please wait...");
    this._service
      .post("exam/certificate-test/true-false/update", obj)
      .subscribe({
        next: (res) => {
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }

          this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
          this.modalEditTrueFalseHide();
          this.getTFQList();
        },
        error: (err) => {
          this.blockUI.stop();
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        },
        complete: () => this.blockUI.stop(),
      });
  }
  // =================== End of True/False Edit =======================

  // =================== Fill in the gap Edit =======================
  editFIG(item, template: TemplateRef<any>) {
    console.log(item);
    this.figqEditForm.controls["id"].setValue(item.Id);
    this.figqEditForm.controls["question"].setValue(item.Question);
    this.figqEditForm.controls["segmentId"].setValue(item.SegmentId);
    this.figqEditForm.controls["mark"].setValue(item.Mark);
    this.figqEditForm.controls["answer"].setValue(item.Answer);
    this.modalRef = this.modalService.show(template, {
      class: "gray modal-lg",
      backdrop: "static",
    });
  }

  modalEditFIGHide() {
    this.figqEditForm.reset({
      answer: false,
    });
    this.modalRef.hide();
    this.editsubmitted = false;
  }

  onFIGUpdate() {
    this.editsubmitted = true;
    if (this.figqEditForm.invalid) return;

    const obj = {
      Id: this.figqEditForm.value.id,
      SegmentId: this.figqEditForm.value.segmentId,
      Question: this.figqEditForm.value.question,
      Mark: this.figqEditForm.value.mark,
      Answer: this.figqEditForm.value.answer,
    };

    this.blockUI.start("Updating data. Please wait...");
    this._service
      .post("exam/certificate-test/fill-in-the-gap/update", obj)
      .subscribe({
        next: (res) => {
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }

          this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
          this.modalEditFIGHide();
          this.geFIGList();
        },
        error: (err) => {
          this.blockUI.stop();
          this.toastr.error(err.message || err, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        },
        complete: () => this.blockUI.stop(),
      });
  }
  // =================== End of Fill in the gap Edit =======================

  // =================== Matching Edit =======================
  editMatching(item, template: TemplateRef<any>) {
    console.log(item);
    this.mqEditForm.controls["id"].setValue(item.Id);
    this.mqEditForm.controls["leftSide"].setValue(item.LeftSide);
    this.mqEditForm.controls["segmentId"].setValue(item.SegmentId);
    this.mqEditForm.controls["mark"].setValue(item.Mark);
    this.mqEditForm.controls["rightSide"].setValue(item.RightSide);
    this.modalRef = this.modalService.show(template, {
      class: "gray modal-lg",
      backdrop: "static",
    });
  }

  modalEditMatchingHide() {
    this.mqEditForm.reset({
      answer: false,
    });
    this.modalRef.hide();
    this.editsubmitted = false;
  }

  onMatchingUpdate() {
    this.editsubmitted = true;
    if (this.mqEditForm.invalid) return;

    const obj = {
      Id: this.mqEditForm.value.id,
      SegmentId: this.mqEditForm.value.segmentId,
      LeftSide: this.mqEditForm.value.leftSide,
      Mark: this.mqEditForm.value.mark,
      RightSide: this.mqEditForm.value.rightSide,
    };

    this.blockUI.start("Updating data. Please wait...");
    this._service.post("exam/certificate-test/matching/update", obj).subscribe({
      next: (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
        this.modalEditMatchingHide();
        this.getMatchingList();
      },
      error: (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      },
      complete: () => this.blockUI.stop(),
    });
  }
  // =================== End of Matching Edit =======================
}
