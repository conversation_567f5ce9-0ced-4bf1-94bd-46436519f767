<block-ui>
  <div class="row">
    <div class="col-sm-12">
      <div class="card card-border-default">
        <div class="card-header">
          <h5>Guest User List</h5>
        </div>
        <div class="card-block">
          <div class="row">
            <form [formGroup]="filterForm" class="col-lg-10 col-md-10 col-12 mb-3" autocomplete="off">
              <input type="text" formControlName="text" class="form-control form-control-sm"
                placeholder="Search By PIN/Name" />
            </form>
            <div class="col-lg-2 col-md-2 col-12 mb-3">
              <button class="btn btn-theme btn-sm float-lg-end float-md-end" [routerLink]="['/guest-user-entry']">
                <i class="feather icon-plus"></i> Create User
              </button>
            </div>
            <div class="col-12">
              <ngx-datatable class="material table-bordered" [rows]="rows" [loadingIndicator]="loadingIndicator"
                [externalPaging]="true" [columnMode]="ColumnMode.force" [headerHeight]="40" [footerHeight]="50"
                rowHeight="auto" [count]="page.totalElements" [offset]="page.pageNumber" [limit]="page.size"
                (page)="setPage($event)" [scrollbarH]="true">

                <ngx-datatable-column [minWidth]="200" name="UserName" prop="UserName" [draggable]="false"
                  [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}"> {{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column name="First Name" prop="FirstName" [draggable]="false" [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}"> {{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column name="Last Name" prop="LastName" [draggable]="false" [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}"> {{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column name="Phone No" prop="PhoneNumber" [draggable]="false" [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}"> {{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [minWidth]="170" name="Last Log On" prop="LastLogOn" [draggable]="false"
                  [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span *ngIf="value" title="{{ value | amDateFormat: 'DD MMM, YYYY hh:mm A' }}">
                      {{ value | amDateFormat: 'DD MMM, YYYY hh:mm A' }} </span>
                    <span *ngIf="!value"> - </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="80" name="Active" prop="Active" [draggable]="false" [sortable]="false"
                  cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value ? 'Yes' : 'No' }}">
                      {{ value ? "Yes" : "No" }}
                    </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [minWidth]="70" [maxWidth]="100" name="Action" prop="Id" [draggable]="false"
                  [sortable]="false">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    <button class="btn btn-outline-primary btn-mini" [routerLink]="['/guest-user-entry']"
                      [queryParams]="{ id: row.Id }" queryParamsHandling="merge">
                      <i class="feather icon-edit"></i> Edit
                    </button>
                  </ng-template>
                </ngx-datatable-column>
              </ngx-datatable>
            </div>
          </div>
        </div>
        <!-- end of card-footer -->
      </div>
    </div>
  </div>
</block-ui>