<block-ui>
  <div class="row">
    <div class="col-12">
      <div class="card card-border-primary">
        <div class="card-body">
          <form [formGroup]="filterForm" class="row custom-inline-form" autocomplete="off">
            <div class="col-lg-3 col-md-4 col-12 mb-3">
              <div class="form-group">
                <label class="col-form-label col-form-label-sm fw-bold required">
                  Course
                </label>
                <ng-select formControlName="courseId" class="form-control form-control-sm" [ngClass]="{
                    'is-invalid': filterSubmitted && g.courseId.errors
                  }" [items]="courseList" bindLabel="Title" bindValue="Id" placeholder="Select">
                </ng-select>
                <div *ngIf="filterSubmitted && g.courseId.errors" class="error-text">
                  <span *ngIf="g.courseId.errors.required" class="text-danger">
                    Course is required</span>
                </div>
              </div>
            </div>

            <div class="col-lg-3 col-md-4 col-12 mb-3">
              <div class="form-group">
                <label class="col-form-label col-form-label-sm fw-bold">Division
                </label>
                <ng-select class="form-control form-control-sm" formControlName="divisionId" [items]="divisionList"
                  bindLabel="Name" bindValue="Id" placeholder="Select a Division">
                </ng-select>
              </div>
            </div>

            <div class="col-lg-6 col-md-8 col-12 mb-3">
              <button class="btn btn-theme btn-sm" (click)="filterList()">
                <i class="feather icon-search"></i> Search
              </button>

              <button class="btn btn-theme btn-sm ms-2" (click)="downloadEnrolledTrainee()">
                <i class="feather icon-download"></i> Download
              </button>

              <button class="btn btn-theme btn-sm float-end" (click)="openModal(template)">
                <i class="feather icon-plus"></i> Enroll Guest to Course
              </button>
            </div>
          </form>

          <div class="row">
            <div class="col-12">
              <ngx-datatable class="material table-bordered" [rows]="rows" [loadingIndicator]="loadingIndicator"
                [columnMode]="ColumnMode.force" [headerHeight]="40" [footerHeight]="50" rowHeight="auto"
                [scrollbarH]="scrollBarHorizontal" [limit]="10">
                <ngx-datatable-column [maxWidth]="80" name="PIN" prop="PIN" [draggable]="false" [sortable]="false"
                  cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}"> {{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [width]="100" name="Guest Name" prop="Name" [draggable]="false"
                  [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}"> {{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [width]="100" prop="Email" [draggable]="false" [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}">{{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="110" prop="PhoneNo" [draggable]="false" [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}">{{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="110" prop="EnrollmentDate" [draggable]="false" [sortable]="false"
                  cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value | amDateFormat:'DD MMM YYYY' }}">
                      {{ value | amDateFormat:'DD MMM YYYY' }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="100" prop="ExpireDate" [draggable]="false" [sortable]="false"
                  cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span *ngIf="value" title="{{ value | amDateFormat:'DD MMM YYYY' }}">
                      {{ value | amDateFormat:'DD MMM YYYY' }}
                    </span>
                    <span *ngIf="!value"> - </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [minWidth]="70" [maxWidth]="70" name="Action" [draggable]="false"
                  [sortable]="false" cellClass="text-center my-auto" headerClass="text-center">
                  <ng-template let-row="row" let-rowIndex="rowIndex" ngx-datatable-cell-template>
                    <button class="btn btn-danger btn-mini" (click)="cancelEnrollment(row)" container="body"
                      [placement]="'top'" tooltip="Cancel Enrollment">
                      <i class="feather icon-x"></i>
                    </button>
                  </ng-template>
                </ngx-datatable-column>
              </ngx-datatable>
            </div>
            <div class="col-sm-2 mt-2" *ngIf="to_Show">
              <button class="btn btn-sm btn-outline-secondary" (click)="downloadSample()">
                <i class="feather icon-download"></i> Download Sample
              </button>
            </div>
            <div class="col-sm-4 offset-sm-3 mt-2" *ngIf="to_Show">
              <button class="btn btn-theme btn-sm" (click)="openUploadDialog()">
                <i class="feather icon-upload"></i>Upload File
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #template>
    <div class="modal-header">
      <h4 class="modal-title float-start" [innerHtml]="modalTitle" id="modalTitle"></h4>
      <button type="button " class="close float-end btn btn-mini btn-danger btn btn-mini btn-outline-danger"
        aria-label="Close " (click)="modalHide()">
        <i class="feather icon-x"></i>
      </button>
    </div>
    <div class="modal-body">
      <form [formGroup]="entryForm" class="row" autocomplete="off">
        <div class="col-12">
          <div class="form-group row mb-3">
            <label class="col-sm-4 col-form-label col-form-label-sm required">Select Course
            </label>
            <div class="col-sm-8">
              <ng-select class="form-control form-control-sm" formControlName="courseId" (change)="getTraineeList()"
                [clearable]="false" [clearOnBackspace]="false" [items]="courseList" bindLabel="Title" bindValue="Id"
                placeholder="Select">
              </ng-select>

              <div *ngIf="submitted && f.courseId.errors" class="error-text">
                <span *ngIf="f.courseId.errors.required" class="text-danger">
                  Course is required</span>
              </div>
            </div>
          </div>
          <div class="form-group row mb-3">
            <label class="col-sm-4 col-form-label col-form-label-sm required">Enrollment Type
            </label>
            <div class="col-sm-8">
              <ng-select class="form-control form-control-sm" formControlName="enrollType"
                (change)="onEnrollmentTypeChange($event)" [clearable]="false" [clearOnBackspace]="false"
                [items]="enrollTypeList" bindLabel="text" bindValue="id" placeholder="Select">
              </ng-select>

              <div *ngIf="submitted && f.enrollType.errors" class="error-text">
                <span *ngIf="f.enrollType.errors.required" class="text-danger">
                  Enrollment type is required</span>
              </div>
            </div>
          </div>
          <div class="form-group row mb-3" *ngIf="f.enrollType.value && f.enrollType.value === 'BySelection'">
            <label class="col-sm-4 col-form-label col-form-label-sm required">Division
            </label>
            <div class="col-sm-8">
              <ng-select class="form-control form-control-sm" formControlName="divisionId" (change)="getTraineeList()"
                [ngClass]="{
                  'is-invalid': submitted && f.divisionId.errors
                }" [clearable]="false" bindLabel="Name" bindValue="Id" [clearOnBackspace]="false"
                [items]="divisionList" placeholder="Select a division">
              </ng-select>

              <div *ngIf="submitted && f.divisionId.errors" class="error-text">
                <span *ngIf="f.divisionId.errors.required" class="text-danger">Division is required</span>
              </div>
            </div>
          </div>
          <div class="form-group row mb-3" *ngIf="f.enrollType.value && f.enrollType.value === 'ByExcel'">
            <label class="col-sm-4 col-form-label col-form-label-sm required">Excel File
            </label>
            <div class="col-sm-8">
              <input #attachment (change)="loadAttachment(attachment.files)" class="form-control form-control-sm"
                type="file" formControlName="attachmentFile" accept=".xls,.xlsx" />

              <div *ngIf="submitted && f.attachmentFile.errors" class="error-text">
                <span *ngIf="f.attachmentFile.errors.required" class="text-danger">
                  File is required</span>
              </div>

              <button type="button" class="btn btn-link mt-3" (click)="downloadSampleFile()">Download sample
                file</button>
            </div>
          </div>
        </div>
        <div class="col-12">
          <div class="card mb-0"
            *ngIf="!entryForm.invalid && f.enrollType.value && f.enrollType.value === 'BySelection'">
            <div class="card-header p-2">
              <div class="row">
                <div class="col-lg-3 col-md-4 col-12">
                  <h5 class="card-title pt-2">Select Trainees</h5>
                </div>
                <div class="col-lg-7 col-md-5 col-12">
                  <input type="text" class="form-control form-control-sm" [(ngModel)]="traineeFilter"
                    [ngModelOptions]="{ standalone: true }" (ngModelChange)="onTraineeFilterChanged()"
                    placeholder="Please enter 3 or more characters of PIN/Name" />
                </div>
                <div class="col-lg-2 col-md-3 col-12">
                  <div class="custom-control custom-checkbox pt-2">
                    <input name="selectAll" id="selectAll" type="checkbox" class="custom-control-input"
                      (change)="selectAll($event)" />
                    <label for="selectAll" class="custom-control-label">
                      Select All
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-body p-2 gcac-style-1">
              <ul class="list-group">
                <li class="list-group-item d-flex justify-content-between align-items-center list-group-item-action"
                  *ngFor="let item of traineeList">
                  <div class="custom-control custom-checkbox">
                    <input name="{{ item.Id }}" id="{{ item.Id }}" type="checkbox" class="custom-control-input"
                      [(ngModel)]="item.selected" [ngModelOptions]="{ standalone: true }" value="{{ item.Id }}"
                      (change)="onItemSelect($event, item)" />
                    <label for="{{ item.Id }}" class="custom-control-label">
                      {{ item.Name }} - {{ item.PIN }}
                    </label>
                  </div>
                  <p class="text-muted mb-0">{{ item.Position }}</p>
                </li>
              </ul>
            </div>
            <div class="card-footer p-2">
              <span class="mb-0 fw-bold">Total Selected: {{ selected_items.length }}</span>
              <div class="custom-control custom-checkbox float-end" *ngIf="selected_items.length > 0">
                <input name="selectedOnly" id="selectedOnly" type="checkbox" class="custom-control-input"
                  (change)="showSelectedonly($event)" />
                <label for="selectedOnly" class="custom-control-label">
                  Show Selected Only
                </label>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <div class="pe-4">
        <button class="btn btn-outline-secondary me-2 btn-sm" (click)="modalHide()">
          <i class="feather icon-x"></i> Close
        </button>

        <button class="btn btn-theme btn-sm" (click)="onFormSubmit()">
          <i class="feather icon-check-circle"></i> {{ btnSaveText }}
        </button>
      </div>
    </div>
  </ng-template>
</block-ui>