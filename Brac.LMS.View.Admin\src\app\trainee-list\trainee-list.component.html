<block-ui>
  <div class="row">
    <div class="col-sm-12">
      <div class="card card-border-default">
        <div class="card-header">
          <h5>Trainee List </h5>
          <!-- <button class="btn btn-theme  btn-sm float-end" [routerLink]="[ '/trainee-entry' ]"><i
              class="feather icon-plus "></i> Create Trainee</button> -->
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-12">
              <form [formGroup]="filterForm" class="row custom-inline-form" autocomplete="off">
                <div class="col-lg-3 col-md-4 col-12 ">
                  <div class="mb-3">
                    <label class="col-form-label col-form-label-sm"> Text </label>
                    <input type="text" formControlName="name" class="form-control form-control-sm"
                      placeholder="Search By name/email/phone/PIN" />
                  </div>
                </div>

                <div class="col-lg-3 col-md-4 col-12">
                  <div class="mb-3">
                    <label class="col-form-label col-form-label-sm"> Division </label>
                    <ng-select #selectElement (click)="handleSelectClick(selectElement)"
                      class="form-control form-control-sm" formControlName="division" [clearable]="true"
                      [clearOnBackspace]="true" (change)="getList()" [items]="divisionList" bindLabel="Name"
                      bindValue="Id" placeholder="--All--">
                    </ng-select>
                  </div>
                </div>

                <div class="col-lg-3 col-md-4 col-12">
                  <div class="mb-3">
                    <label class="col-form-label col-form-label-sm">Department </label>
                    <ng-select #selectElementD (click)="handleSelectClick(selectElementD)"
                      class="form-control form-control-sm" formControlName="department" [clearable]="true"
                      [clearOnBackspace]="true" (change)="getList()" [items]="departmentList" bindLabel="Name"
                      bindValue="Id" placeholder="--All--">
                    </ng-select>
                  </div>
                </div>

                <div class="col-lg-3 col-md-4 col-12">
                  <button class="btn btn-theme btn-sm" (click)="downloadTraineeList()"><i
                      class="feather icon-download"></i>
                    Download Excel </button>
                </div>
              </form>
            </div>


          </div>
          <div class="row ">
            <div class="col-lg-12 ">

              <ngx-datatable class="material table-bordered" [rows]="rows" [loadingIndicator]="loadingIndicator"
                [columnMode]="ColumnMode.force" [headerHeight]="40" [footerHeight]="50" [externalPaging]="true"
                [count]="page.totalElements" [offset]="page.pageNumber" [limit]="page.size" (page)="setPage($event)">

                <ngx-datatable-column [maxWidth]="50" name="SL#" [draggable]="false" [sortable]="false"
                  headerClass="text-center" cellClass="text-center">
                  <ng-template let-row="row" let-rowIndex="rowIndex" ngx-datatable-cell-template>
                    <strong> {{ (page.pageNumber * page.size) + rowIndex + 1 }} </strong>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="70" prop="PIN" [draggable]="false" [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}"> {{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>


                <ngx-datatable-column name="Name" prop="Name" [draggable]="false" [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}"> {{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>


                <ngx-datatable-column [maxWidth]="150" prop="Division" [draggable]="false" [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}"> {{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="150" prop="Department" [draggable]="false" [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}"> {{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="130" prop="Position" [draggable]="false" [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}"> {{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column name="Email" prop="Email" [maxWidth]="300" [draggable]="false" [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}"> {{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="110" prop="PhoneNo" [draggable]="false" [sortable]="false">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}"> {{ value }} </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column [maxWidth]="60" name="Active" prop="Active" [draggable]="false" [sortable]="false"
                  headerClass="text-center" cellClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value ? 'Yes' : 'No' }}"> {{ value ? 'Yes' : 'No' }}
                    </span>
                  </ng-template>
                </ngx-datatable-column>



                <ngx-datatable-column name="Action" [draggable]="false" [sortable]="false" headerClass="text-center"
                  cellClass="text-center">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    <button class="btn btn-outline-primary btn-mini me-1" (click)="getItem(row.Id,template)">
                      <i class="feather icon-info"></i> Details
                    </button>
                    <button *ngIf="isSuperAdmin" class="btn btn-outline-primary btn-mini"
                      [routerLink]="['/trainee-entry']" [queryParams]="{ id: row.Id }" queryParamsHandling="merge">
                      <i class="feather icon-edit"></i> Edit
                    </button>
                  </ng-template>
                </ngx-datatable-column>
              </ngx-datatable>

            </div>

          </div>
        </div>
        <!-- <div class="card-footer ">
          <div class="row mt-2">
            <div class="col-lg-6 col-12">
              <button class="btn btn-theme  btn-sm" (click)="openUploadDialog()"><i class="fas fa-file-upload"></i>
                Bulk Upload Trainee </button>

            </div>


            <div class="col-lg-6 col-12">
              <button (click)="downloadSample()" class="btn btn-outline-secondary btn-sm float-end me-2"><i
                  class=" feather icon-download "></i>
                Download Sample File</button>
            </div>
          </div>
        </div> -->
        <!-- end of card-footer -->
      </div>
    </div>
  </div>

  <ng-template #template>
    <div class="modal-header ">
      <h4 class="modal-title float-start"> Trainee's Detail</h4>
      <button type="button " class="close float-end btn btn-mini btn-danger btn btn-mini btn-outline-danger"
        aria-label="Close " (click)="modalHide()">
        <i class="feather icon-x"></i>
      </button>
    </div>
    <div class="modal-body ">
      <div class="row">
        <div class="col-12 ">
          <div class="row">
            <div class="col-6">
              <h3 class="mb-0"><i class="far fa-clone p-r-1"></i>General Information</h3>
            </div>

          </div>

          <div class="row">

            <div class="col-lg-6 col-12 table-responsive-lg table-responsive-xl ">

              <table class="table table-sm table-bordered">
                <tbody>
                  <tr>
                    <td rowspan="10" width="130">
                      <img *ngIf="this.imageUrl" [src]="this.imageUrl" class="tlc-max-width-130">
                      <img *ngIf="!this.imageUrl && !Trainee.Gender" src="assets/images/user/user-avatar-blank.png"
                        class="tlc-max-width-130">
                      <img *ngIf="!this.imageUrl && Trainee.Gender=='Others'" src="assets/images/user/other.jpg"
                        class="tlc-max-width-130" />
                      <img *ngIf="!this.imageUrl && Trainee.Gender=='Male'" src="assets/images/user/male.jpg"
                        class="tlc-max-width-130" />
                      <img *ngIf="!this.imageUrl && Trainee.Gender=='Female'" src="assets/images/user/female.jpg"
                        class="tlc-max-width-130" />

                    </td>
                  </tr>
                  <tr>
                    <th width="30%">PIN </th>
                    <td width="2%">:</td>
                    <td class="text-wrap">{{Trainee.PIN}}</td>
                  </tr>
                  <tr>
                    <th width="30%">Name </th>
                    <td width="2%">:</td>
                    <td class="text-wrap">{{Trainee.Name}}</td>
                  </tr>

                  <tr>
                    <th width="30%">Email </th>
                    <td width="2%">:</td>
                    <td class="text-wrap admin-word-break">{{Trainee.Email}}</td>
                  </tr>
                  <tr>
                    <th width="30%">Phone </th>
                    <td width="2%">:</td>
                    <td class="text-wrap">{{Trainee.PhoneNo}}</td>
                  </tr>
                  <tr>
                    <th width="30%">Date Of Joining </th>
                    <td width="2%">:</td>
                    <td class="text-wrap">
                      {{ Trainee.DateOfJoining? (Trainee.DateOfJoining | amDateFormat: 'DD MMM, YYYY hh:mm A') : "-" }}
                    </td>
                  </tr>
                  <tr>
                    <th width="30%">Gender </th>
                    <td width="2%">:</td>
                    <td>{{Trainee.Gender}}</td>
                  </tr>
                  <tr rowspan="2">
                    <th width="30%">WorkLocation</th>
                    <td width="2%">:</td>
                    <td class="text-wrap">{{Trainee.WorkLocation}}</td>
                  </tr>
                  <tr>
                    <th width="30%">Address</th>
                    <td width="2%">:</td>
                    <td class="text-wrap admin-word-break">{{Trainee.Address}}</td>
                  </tr>

                  <tr>
                    <th width="30%">Active</th>
                    <td width="2%">:</td>
                    <td *ngIf="Trainee.Active"><i class="feather icon-check-circle admin-color-green"></i></td>
                    <td *ngIf="!Trainee.Active"><i class="feather icon-x-circle admin-color-green"></i></td>
                  </tr>

                </tbody>
              </table>


            </div>
            <div class="col-lg-6 col-12  table-responsive-lg table-responsive-xl">

              <table class="table table-sm table-bordered">
                <tr>
                  <th width="30%">Division</th>
                  <td width="2%">:</td>
                  <td class="text-wrap">{{Trainee.Division}}
                  </td>
                </tr>
                <tr>
                  <th width="30%">Department</th>
                  <td width="2%">:</td>
                  <td class="text-wrap">{{Trainee.Department}}</td>
                </tr>

                <tr>
                  <th width="30%">Position</th>
                  <td width="2%">:</td>
                  <td class="text-wrap admin-word-break">{{Trainee.Position}}</td>
                </tr>
                <tr>
                  <th width="30%">Grade</th>
                  <td width="2%">:</td>
                  <td class="text-wrap">{{Trainee.Grade}}</td>
                </tr>

                <tr>
                  <th width="30%">Employee Type </th>
                  <td width="2%">:</td>
                  <td class="text-wrap">{{Trainee.EmployeeType}}</td>
                </tr>
                <tr>
                  <th width="30%">Unit</th>
                  <td width="2%">:</td>
                  <td class="text-wrap">{{Trainee.Unit}}</td>
                </tr>
                <tr>
                  <th width="30%">Sub Unit</th>
                  <td width="2%">:</td>
                  <td class="text-wrap">{{Trainee.SubUnit}}</td>
                </tr>
                <tr>
                  <th width="30%">Line Manager Name</th>
                  <td width="2%">:</td>
                  <td class="text-wrap">{{Trainee.LineManagerName}}</td>
                </tr>
                <tr>
                  <th width="30%">Line Manager PIN</th>
                  <td width="2%">:</td>
                  <td class="text-wrap">{{Trainee.LineManagerPIN}}</td>
                </tr>

              </table>


            </div>
          </div>

        </div>
      </div>

    </div>
    <div class="modal-footer">
      <div class="p-r-4">

        <button class="btn btn-outline-secondary me-2" (click)="modalHide()"> <i class="feather icon-x"></i>
          Close</button>


      </div>

    </div>
  </ng-template>

</block-ui>