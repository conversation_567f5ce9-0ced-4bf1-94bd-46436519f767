using System;
using System.Data.Common;
using System.Data.Entity.Infrastructure.Interception;
using System.Data.SqlClient;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Brac.LMS.Common
{
    /// <summary>
    /// Safe SQL Command Interceptor that logs datetime conversion errors without affecting normal operations
    /// </summary>
    public class DateTimeSqlInterceptor : DbCommandInterceptor
    {
        public override void NonQueryExecuted(DbCommand command, DbCommandInterceptionContext<int> interceptionContext)
        {
            LogDateTimeErrors(command, interceptionContext.Exception, "NonQueryExecuted");
            base.NonQueryExecuted(command, interceptionContext);
        }

        public override async Task NonQueryExecutedAsync(DbCommand command, DbCommandInterceptionContext<int> interceptionContext, CancellationToken cancellationToken)
        {
            LogDateTimeErrors(command, interceptionContext.Exception, "NonQueryExecutedAsync");
            await base.NonQueryExecutedAsync(command, interceptionContext, cancellationToken);
        }

        public override void ReaderExecuted(DbCommand command, DbCommandInterceptionContext<DbDataReader> interceptionContext)
        {
            LogDateTimeErrors(command, interceptionContext.Exception, "ReaderExecuted");
            base.ReaderExecuted(command, interceptionContext);
        }

        public override async Task ReaderExecutedAsync(DbCommand command, DbCommandInterceptionContext<DbDataReader> interceptionContext, CancellationToken cancellationToken)
        {
            LogDateTimeErrors(command, interceptionContext.Exception, "ReaderExecutedAsync");
            await base.ReaderExecutedAsync(command, interceptionContext, cancellationToken);
        }

        public override void ScalarExecuted(DbCommand command, DbCommandInterceptionContext<object> interceptionContext)
        {
            LogDateTimeErrors(command, interceptionContext.Exception, "ScalarExecuted");
            base.ScalarExecuted(command, interceptionContext);
        }

        public override async Task ScalarExecutedAsync(DbCommand command, DbCommandInterceptionContext<object> interceptionContext, CancellationToken cancellationToken)
        {
            LogDateTimeErrors(command, interceptionContext.Exception, "ScalarExecutedAsync");
            await base.ScalarExecutedAsync(command, interceptionContext, cancellationToken);
        }

        /// <summary>
        /// Safely logs datetime conversion errors without affecting application flow
        /// </summary>
        private void LogDateTimeErrors(DbCommand command, Exception exception, string operation)
        {
            if (exception == null) return;

            try
            {
                // Check for SQL Server datetime conversion error (Error 242)
                var sqlException = GetSqlException(exception);
                if (sqlException?.Number == 242)
                {
                    LogDateTimeConversionError(command, sqlException, operation);
                }
                // Also check for other datetime-related errors
                else if (IsDateTimeRelatedError(exception))
                {
                    LogGeneralDateTimeError(command, exception, operation);
                }
            }
            catch
            {
                // Fail silently - never break the application due to logging issues
                try
                {
                    LogControl.Write($"DATETIME ERROR INTERCEPTOR | Failed to log error details | Operation: {operation}");
                }
                catch
                {
                    // Ultimate fallback - do nothing if even basic logging fails
                }
            }
        }

        /// <summary>
        /// Recursively find SqlException in exception chain
        /// </summary>
        private SqlException GetSqlException(Exception exception)
        {
            var current = exception;
            while (current != null)
            {
                if (current is SqlException sqlEx)
                    return sqlEx;
                current = current.InnerException;
            }
            return null;
        }

        /// <summary>
        /// Check if exception is datetime-related based on message content
        /// </summary>
        private bool IsDateTimeRelatedError(Exception exception)
        {
            var message = exception.Message?.ToLower() ?? "";
            return message.Contains("datetime") || 
                   message.Contains("conversion") || 
                   message.Contains("out-of-range") ||
                   message.Contains("datetime2");
        }

        /// <summary>
        /// Log detailed information about datetime conversion errors
        /// </summary>
        private void LogDateTimeConversionError(DbCommand command, SqlException sqlException, string operation)
        {
            try
            {
                var parameters = ExtractParameters(command);
                var dateTimeParameters = FilterDateTimeParameters(parameters);

                LogControl.Write($"DATETIME CONVERSION ERROR DETECTED | " +
                    $"Operation: {operation} | " +
                    $"SQL Error: {sqlException.Number} | " +
                    $"Message: {sqlException.Message} | " +
                    $"DateTime Parameters: {dateTimeParameters} | " +
                    $"All Parameters: {parameters} | " +
                    $"SQL: {TruncateSql(command.CommandText)}");
            }
            catch
            {
                LogControl.Write($"DATETIME CONVERSION ERROR DETECTED | Operation: {operation} | Could not extract parameter details");
            }
        }

        /// <summary>
        /// Log general datetime-related errors
        /// </summary>
        private void LogGeneralDateTimeError(DbCommand command, Exception exception, string operation)
        {
            try
            {
                var parameters = ExtractParameters(command);
                var dateTimeParameters = FilterDateTimeParameters(parameters);

                LogControl.Write($"DATETIME RELATED ERROR | " +
                    $"Operation: {operation} | " +
                    $"Error: {exception.Message} | " +
                    $"DateTime Parameters: {dateTimeParameters} | " +
                    $"SQL: {TruncateSql(command.CommandText)}");
            }
            catch
            {
                LogControl.Write($"DATETIME RELATED ERROR | Operation: {operation} | Could not extract details");
            }
        }

        /// <summary>
        /// Safely extract parameter information
        /// </summary>
        private string ExtractParameters(DbCommand command)
        {
            try
            {
                if (command.Parameters == null || command.Parameters.Count == 0)
                    return "No parameters";

                return string.Join(", ", 
                    command.Parameters.Cast<DbParameter>()
                    .Select(p => $"{p.ParameterName}={FormatParameterValue(p.Value)}"));
            }
            catch
            {
                return "Could not extract parameters";
            }
        }

        /// <summary>
        /// Filter and highlight datetime parameters
        /// </summary>
        private string FilterDateTimeParameters(string allParameters)
        {
            try
            {
                if (allParameters == "No parameters" || allParameters == "Could not extract parameters")
                    return allParameters;

                var dateTimeParams = allParameters.Split(',')
                    .Where(p => p.ToLower().Contains("date") || p.ToLower().Contains("time") || p.ToLower().Contains("created") || p.ToLower().Contains("modified"))
                    .ToList();

                return dateTimeParams.Any() ? string.Join(", ", dateTimeParams) : "No datetime parameters found";
            }
            catch
            {
                return "Could not filter datetime parameters";
            }
        }

        /// <summary>
        /// Safely format parameter values
        /// </summary>
        private string FormatParameterValue(object value)
        {
            try
            {
                if (value == null || value == DBNull.Value)
                    return "NULL";

                if (value is DateTime dt)
                    return $"{dt:yyyy-MM-dd HH:mm:ss.fff}";

                return value.ToString();
            }
            catch
            {
                return "Could not format value";
            }
        }

        /// <summary>
        /// Truncate SQL for logging to avoid huge log entries
        /// </summary>
        private string TruncateSql(string sql)
        {
            try
            {
                if (string.IsNullOrEmpty(sql))
                    return "No SQL";

                return sql.Length > 500 ? sql.Substring(0, 500) + "..." : sql;
            }
            catch
            {
                return "Could not extract SQL";
            }
        }
    }
}
