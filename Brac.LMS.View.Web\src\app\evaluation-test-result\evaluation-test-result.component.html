<!-- Page content-->
<div class="container-fluid position-relative zindex-5 pb-0 mb-md-1 p-3 margin-top-neg-15">
  <div class="d-flex flex-column h-100 bg-tranparent-black-glass rounded-1 shadow-lg">
    <div class="pt-2 p-md-3">
      <div class="d-sm-flex align-items-center justify-content-between pb-1 text-center text-sm-start">
        <h1 class="h3 mb-2 text-nowrap">Evaluation Test Result</h1>
      </div>
      <hr class="mt-1 mb-4">
      <!-- Content-->


      <div class="row">
        <form [formGroup]="filterForm" autocomplete="off" class="col-lg-8 col-12">
          <div class="row">
            <div class="col-lg-8 col-12 mb-3">
              <div class="input-group">
                <label class="visually-hidden" for="name">Name</label>
                <input type="text" class="form-control rounded-custom pe-5" formControlName="name"
                  placeholder="Type exam name to search...">
                <i class="ai-search position-absolute top-50 end-0 translate-middle-y me-3"></i>
              </div>
            </div>

            <div class="col-lg-4 col-12 mb-3">
              <label class="visually-hidden" for="categoryId">Category</label>
              <ng-select #selectElement (click)="handleSelectClick(selectElement)" [clearable]="true"
                [clearOnBackspace]="true" formControlName="categoryId" (change)="getList()" [items]="categoryList"
                bindLabel="Name" bindValue="Id" placeholder="Category">
              </ng-select>
            </div>
          </div>
        </form>
      </div>

      <div class="section">
        <div class="col-12">
          <div class="row justify-content-center">

            <div class="col-lg-12 min-height-400">
              <ngx-datatable class="material" [scrollbarH]="scrollBarHorizontal" [rows]="rows"
                [loadingIndicator]="loadingIndicator" [columnMode]="ColumnMode.force" [headerHeight]="40"
                [footerHeight]="50" rowHeight="auto" [externalPaging]="true" [count]="page.totalElements"
                [offset]="page.pageNumber" [limit]="page.size" (page)="setPage($event)">



                <ngx-datatable-column [width]="400" name="Exam Name" [draggable]="false" [sortable]="false">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    <a class="fs-6" routerLinkActive="router-link-active" [routerLink]="['/evaluation-test',row.ExamId]"
                      title="{{row['ExamName']}}">
                      {{row['ExamName']}}
                    </a>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column name="Attend Date" prop="StartDate" [draggable]="false" [sortable]="false"
                  cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span>
                      <!-- **FIX: Don't convert time again - backend already sends local time** -->
                      {{ value | amDateFormat: "DD-MMM-YYYY" }}
                    </span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column name="Marks" [draggable]="false" [sortable]="false" cellClass="text-center"
                  headerClass="text-center">
                  <ng-template let-row="row" ngx-datatable-cell-template>
                    <span *ngIf="row['Status'] === 'Published'">
                      {{ row.GainedMarks + "/" + row.TotalMarks }}
                    </span>
                    <span *ngIf="row['Status'] !== 'Published'">-</span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column name="Result" prop="Result" [draggable]="false" [sortable]="false"
                  cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <container-element [ngSwitch]="value">
                      <span class="badge bg-danger" *ngSwitchCase="'Failed'">{{ value }}</span>
                      <span class="badge bg-success" *ngSwitchCase="'Passed'">{{ value }}</span>
                      <span class="badge bg-warning" *ngSwitchDefault>
                        {{
                        value
                        }}
                      </span>
                    </container-element>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column name="Comments" prop="CheckerComments" [draggable]="false" [sortable]="false"
                  cellClass="text-truncate" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}">{{ value }}</span>
                  </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column name="Score" prop="Score" [draggable]="false" [sortable]="false"
                  cellClass="text-center" headerClass="text-center">
                  <ng-template let-value="value" ngx-datatable-cell-template>
                    <span title="{{ value }}">{{ value }}</span>
                  </ng-template>
                </ngx-datatable-column>
              </ngx-datatable>
            </div>

          </div>

        </div>
        <!-- end container -->
      </div>




    </div>
  </div>
</div>