import {
  Component,
  TemplateRef,
  ViewEncapsulation,
  OnInit,
  Pipe,
  PipeTransform,
} from "@angular/core";
import { BsModalService, BsModalRef } from "ngx-bootstrap/modal";
import { ColumnMode } from "@swimlane/ngx-datatable";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { CommonService } from "../_services/common.service";
import { ToastrService } from "ngx-toastr";
import { BlockUI, NgBlockUI } from "ng-block-ui";
import { environment } from "../../environments/environment";
import { UploadDialogComponent } from "../_helpers/upload-dialog/dialog.component";
import { MatDialog } from "@angular/material/dialog";
import {
  BsDatepickerConfig,
  BsDaterangepickerConfig,
} from "ngx-bootstrap/datepicker";
import * as moment from "moment";
import { ResponseStatus } from "../_models/enum";

@Component({
  selector: "app-belated-trainee-notify-list",
  templateUrl: "./belated-trainee-notify-list.component.html",
  encapsulation: ViewEncapsulation.None,
})
export class BelatedTraineeNotifyListComponent implements OnInit {
  entryForm: FormGroup;
  filterForm: FormGroup;
  submitted = false;
  filterSubmitted = false;
  isSelected = false;
  allRowsSelected = false;
  @BlockUI() blockUI: NgBlockUI;
  modalTitle = "Extended Time";
  btnSaveText = "Save";
  to_Show = false;
  dueDate = "";
  modalConfig: any = { class: "gray modal-md", backdrop: "static" };
  modalRef: BsModalRef;

  searchText: string = "";
  selected_count: number = 0;
  selected_items: Array<any> = [];

  courseList: Array<any> = [];
  divisionList: Array<any> = [];
  traineeList: Array<any> = [];
  examList: Array<any> = [];
  rows = [];
  loadingIndicator = false;
  ColumnMode = ColumnMode;

  scrollBarHorizontal = window.innerWidth < 1200;

  itemObj: any = {};
  timeStamp;

  //imgBaseUrl = environment.imageUrl;
  baseUrl = environment.baseUrl;
  bsConfig: Partial<BsDatepickerConfig>;

  bsValue: Date[] = [];

  constructor(
    private modalService: BsModalService,
    public formBuilder: FormBuilder,
    private _service: CommonService,
    private toastr: ToastrService,
    private dialog: MatDialog
  ) {
    window.onresize = () => {
      this.scrollBarHorizontal = window.innerWidth < 1200;
    };
  }

  ngOnInit() {
    this.entryForm = this.formBuilder.group({
      startDate: [new Date().toISOString(), [Validators.required]],
      endDate: [new Date().toISOString(), [Validators.required]],
      startTime: [null, [Validators.required]],
      endTime: [null, [Validators.required]],
    });

    this.filterForm = this.formBuilder.group({
      courseId: [null, [Validators.required]],
      examId: [null, [Validators.required]],
    });

    this.bsConfig = Object.assign(
      {},
      { minDate: new Date(), containerClass: "theme-blue" }
    );
    this.getCourseList();
  }

  get f() {
    return this.entryForm.controls;
  }

  get g() {
    return this.filterForm.controls;
  }

  getCourseList() {
    this._service.get("course/dropdown-list").subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.courseList = res.Data;
      },
      () => {}
    );
  }

  onFormSubmit() {
    this.submitted = true;
    if (this.entryForm.invalid) {
      return;
    }

    if (this.selected_count === 0) {
      this.toastr.warning("Please selecta minimum one trainee", "Warning!", {
        timeOut: 2000,
      });
      return;
    }

    this.blockUI.start("Saving...");

    const obj = {
      CourseId: this.entryForm.value.courseId,
      Trainees: this.selected_items.map(function (x) {
        return x.Id;
      }),
    };

    // const formData = new FormData();
    // formData.append('Model', JSON.stringify(obj));

    this._service.post("course/", obj).subscribe(
      (data) => {
        this.blockUI.stop();
        if (data.Success) {
          this.toastr.success(data.Message, "Success!", { timeOut: 2000 });
          this.modalHide();
          // this.filterList();
        } else {
          this.toastr.error(data.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
        }
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );
  }

  modalHide() {
    this.modalRef.hide();
    this.submitted = false;
  }

  openModal(template: TemplateRef<any>) {
    // this.entryForm.controls['isActive'].setValue(true);
    this.modalRef = this.modalService.show(template, this.modalConfig);
  }

  sendEmail() {
    this.submitted = true;
    //this.entryForm.value.startDate.setTime(this.entryForm.value.startTime);
    //this.entryForm.value.endDate.setTime(this.entryForm.value.endTime);
    if (this.entryForm.invalid) {
      return;
    }
    let startDate = moment(this.entryForm.value.startDate).toISOString();
    let endDate = moment(this.entryForm.value.endDate).toISOString();
    if (moment(startDate) > moment(endDate)) {
      this.toastr.warning("Please place a valid date range", "Warning!", {
        timeOut: 2000,
      });
      return;
    }
    const obj = {
      ExamId: this.filterForm.value.examId,
      Trainees: this.traineeList
        .filter((y) => y.Selected === true)
        .map(function (x) {
          return x.Id;
        }),
      startDate: startDate,
      endDate: endDate,
      startTime: this.entryForm.value.startTime,
      endTime: this.entryForm.value.endTime,
    };

    // const formData = new FormData();
    // formData.append('Model', JSON.stringify(obj));

    this._service.post("course/notify-belated-trainees", obj).subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.toastr.success(res.Message, "Success!", { timeOut: 2000 });
        this.modalHide();
        this.filterForm.reset();
        this.traineeList = [];
        this.rows = [];
        this.entryForm.reset();
        this.selected_count = 0;
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", {
          closeButton: true,
          disableTimeOut: false,
          enableHtml: true,
        });
      }
    );

    this.submitted = true;
  }

  public openUploadDialog() {
    let dialogRef = this.dialog
      .open(UploadDialogComponent, {
        data: {
          url: this._service.generateUrl("Course/UploadCourseAssign"),
          whiteList: ["xlsx", "xls"],
          uploadtext: "Please upload an Excel file",
          title: "Upload trainee Course Assign File",
        },
        width: "50%",
        height: "50%",
      })
      .afterClosed()
      .subscribe((response) => {
        if (response) {
          this.toastr.success(response, "Success!", { timeOut: 2000 });
          this.filterList();
        }
      });
  }

  filterList() {
    this.submitted = true;

    if (this.filterForm.invalid) return;
    this.loadingIndicator = true;

    let courseId = this.filterForm.value.courseId;
    let examId = this.filterForm.value.examId;

    this._service
      .get("course/notified-trainees/" + courseId + "/" + examId)
      .subscribe(
        (res) => {
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }

          this.rows = res.Data.Records;

          console.log(this.rows);

          setTimeout(() => {
            this.loadingIndicator = false;
          }, 1000);
        },
        () => {}
      );
  }
  setSelectedTrainee(id, selected) {
    this.traineeList.forEach((s) => {
      if (id.indexOf(s.Id) >= 0) {
        s.Selected = selected;
        if (selected) this.selected_count++;
        else this.selected_count--;
      }
    });
  }
  selectAll(value) {
    if (value) {
      this.traineeList.forEach((s) => {
        s.Selected = true;
        this.selected_count++;
      });
      this.isSelected = true;
    } else {
      this.traineeList.forEach((s) => {
        s.Selected = false;
        this.selected_count--;
      });
      this.isSelected = false;
    }
  }
  onChangeCourse(event) {
    this.examList = [];
    this.filterForm.controls["examId"].setValue(null);
    this._service.get("exam/certificate/dropdown-list/" + event.Id).subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.examList = res.Data;
      },
      () => {}
    );
  }
  onChangeExam(event) {
    this.traineeList = [];
    this.selected_count = 0;
    this.rows = [];
    this.filterList();
    this.dueDate = "";
    this.dueDate = event.Date ? event.Date : "no ending date";
  }
}
