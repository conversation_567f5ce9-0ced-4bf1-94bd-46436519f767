﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.Services
{
    public class NotificationService : INotificationService
    {
        private readonly ApplicationDbContext _context;
        public NotificationService()
        {
            _context = new ApplicationDbContext();
        }

        public async Task<APIResponse> GetAllNotifications(int size, int pageNumber, UserType userType, ApplicationUser user, bool unseenOnly = false, Guid? activeNotificationId = null)
        {
            try
            {
                var query = _context.Notifications.Where(x => x.TargetUserType == userType).AsQueryable();
                var courseIds = _context.Courses.Where(x => x.Published == false).Select(s => s.Id.ToString()).AsQueryable();
                query = query.Where(x => !courseIds.Contains(x.Payload));
                if (unseenOnly) query = query.Where(x => x.SeenTime == null);
                if (userType == UserType.Trainee) query = query.Where(x => x.TargetTraineeId == user.Trainee.Id);
                int index=0;
                if (activeNotificationId!=null && !unseenOnly)
                {
                    var notification =await _context.Notifications.FirstOrDefaultAsync(x=>x.Id==activeNotificationId);
                    index = query.OrderByDescending(x => x.CreatedOn).ToList().IndexOf(notification);

                }

                var determinedSkipCount =Convert.ToInt16( activeNotificationId == null || unseenOnly ? size * pageNumber : (index/size) > 0 ? size * (Math.Floor(Convert.ToDecimal( index/size))):size*1);

                var list = await query.Select(x => new { x.Id, x.Title, x.Details, x.CreatedOn, NotificationType = x.NotificationType.ToString(), x.Payload, NavigateTo = x.NavigateTo.ToString(), Seen = x.SeenTime != null,x.SourceId })
                    .OrderByDescending(x => x.CreatedOn).Skip(determinedSkipCount).Take(size).ToListAsync();
                var data = list.Select(x => new { x.Id, x.Title, x.Details, CreatedOn = x.CreatedOn.ToLocalTime(), x.NotificationType, x.Payload, x.NavigateTo, x.Seen, x.SourceId, AutoOpenNextItem=string.IsNullOrEmpty(x.SourceId) ? false:true }).ToList();

                var count = await query.CountAsync();

                query = query.Where(x => x.SeenTime == null);

                var unseenCount = await query.CountAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count, UnseenTotal = unseenCount, PageNo= (determinedSkipCount /size)+1, Unseen= unseenOnly }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetUnseenCount(UserType userType, ApplicationUser user)
        {
            try
            {
                var query = _context.Notifications.Where(x => x.TargetUserType == userType && x.SeenTime == null).AsQueryable();
                var courseIds = _context.Courses.Where(x => x.Published == false).Select(s => s.Id.ToString()).AsQueryable();
                query = query.Where(x => !courseIds.Contains(x.Payload));
                if (userType == UserType.Trainee) query = query.Where(x => x.TargetTraineeId == user.Trainee.Id);
                var count = await query.CountAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = count
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> NotificationSeen(Guid id)
        {
            try
            {
                var notification = await _context.Notifications.FindAsync(id);

                if (notification != null)
                {
                    notification.SeenTime = DateTime.UtcNow.ToKindLocal();
                    _context.Entry(notification).State = EntityState.Modified;
                }

                await _context.SaveChangesAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> DeleteNotification(Guid id)
        {
            try
            {
                var notification = await _context.Notifications.FindAsync(id);

                if (notification != null)
                {
                    _context.Entry(notification).State = EntityState.Deleted;
                }

                await _context.SaveChangesAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Deleted"
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> TestNotification(NotificationType notificationType, ApplicationUser user)
        {
            try
            {
                var deviceTokens = await _context.TraineeDevices.Where(x => x.TraineeId == user.Trainee.Id).Select(x => x.Token).ToArrayAsync();
                Notification notification = null;
                switch (notificationType)
                {
                    case NotificationType.SequenceCompletion:
                        notification = await _context.Notifications.FirstOrDefaultAsync(x => x.TargetTraineeId == user.Trainee.Id && x.NotificationType == NotificationType.SequenceCompletion);
                        await new FirebaseMessagingClient().SendNotifications(deviceTokens, notification.Title, notification.Details, new
                        {
                            NavigateTo = notification.NavigateTo.ToString(),
                            notification.Payload,
                            notification.Id,
                            NotificationType = notification.NotificationType.ToString()
                        });
                        break;
                    case NotificationType.CourseEnrolmentByAdmin:
                        var enrolledCourse = await _context.CourseEnrollments.Where(x => x.TraineeId == user.Trainee.Id).Select(x => x.Course).FirstOrDefaultAsync();
                        notification = await _context.Notifications.FirstOrDefaultAsync(x => x.TargetTraineeId == user.Trainee.Id && x.NavigateTo == Navigation.CourseDetails);
                        await new FirebaseMessagingClient().SendNotifications(deviceTokens, "New Course Enrolment by Admin", $"The admin has enrolled you in a new course: {enrolledCourse.Title}", new
                        {
                            NavigateTo = notification.NavigateTo.ToString(),
                            notification.Payload,
                            notification.Id,
                            NotificationType = notification.NotificationType.ToString()
                        });
                        break;
                    case NotificationType.NewAvailableCourse:
                        var availableCourse = await _context.Courses.Where(x => x.SelfEnrollment)
                            .GroupJoin(_context.CourseEnrollments, x => new { CourseId = x.Id, TraineeId = user.Trainee.Id }, y => new { y.CourseId, y.TraineeId }, (x, y) => new { Course = x, Enrollments = y }).Where(x => !x.Enrollments.Any()).Select(x => x.Course).FirstOrDefaultAsync();
                        notification = await _context.Notifications.FirstOrDefaultAsync(x => x.TargetTraineeId == user.Trainee.Id && x.NavigateTo == Navigation.CoursePreview);
                        await new FirebaseMessagingClient().SendNotifications(deviceTokens, "New Course Available", $"A new course \"{ availableCourse.Title }\" has been introdueced. This course is open for all. You can enrol this course if you needed.", new
                        {
                            NavigateTo = notification.NavigateTo.ToString(),
                            notification.Payload,
                            notification.Id
                        });
                        break;
                    case NotificationType.SuccessfullCompletion:
                        var enrolledCourse2 = await _context.CourseEnrollments.Where(x => x.TraineeId == user.Trainee.Id).Select(x => x.Course).FirstOrDefaultAsync();
                        notification = await _context.Notifications.FirstOrDefaultAsync(x => x.TargetTraineeId == user.Trainee.Id && x.NavigateTo == Navigation.CourseReview);
                        await new FirebaseMessagingClient().SendNotifications(deviceTokens, "Course Completion", $"You have successfully completed the course: {enrolledCourse2.Title}", new
                        {
                            NavigateTo = notification.NavigateTo.ToString(),
                            notification.Payload,
                            notification.Id,
                            NotificationType = notification.NotificationType.ToString()
                        });
                        break;
                    case NotificationType.ForumComment:
                        var myForumPost = await _context.ForumPosts.FirstOrDefaultAsync(x => x.Topic.CreatorId == user.Id);
                        notification = await _context.Notifications.FirstOrDefaultAsync(x => x.TargetTraineeId == user.Trainee.Id && x.NavigateTo == Navigation.ForumComment);
                        await new FirebaseMessagingClient().SendNotifications(deviceTokens, "New comment on your post", $"{user.FirstName + " " + user.LastName} commented in your post \"{myForumPost.Topic.Title}\".", new
                        {
                            NavigateTo = notification.NavigateTo.ToString(),
                            notification.Payload,
                            notification.Id,
                            NotificationType = notification.NotificationType.ToString()
                        });
                        break;
                    case NotificationType.ForumReply:
                        var myForumRply = await _context.ForumPosts.FirstOrDefaultAsync(x => x.ParentPost.CreatorId == user.Id);
                        notification = await _context.Notifications.FirstOrDefaultAsync(x => x.TargetTraineeId == user.Trainee.Id && x.NavigateTo == Navigation.ForumReply);
                        await new FirebaseMessagingClient().SendNotifications(deviceTokens, "New reply to your comment", $"{user.FirstName + " " + user.LastName} replied to your comment.", new
                        {
                            NavigateTo = notification.NavigateTo.ToString(),
                            notification.Payload,
                            notification.Id,
                            NotificationType = notification.NotificationType.ToString()
                        });
                        break;
                    case NotificationType.ForumApproval:
                        var myForumTopic = await _context.ForumTopics.FirstOrDefaultAsync(x => x.CreatorId == user.Id);
                        notification = await _context.Notifications.FirstOrDefaultAsync(x => x.TargetTraineeId == user.Trainee.Id && x.NavigateTo == Navigation.ForumPostDetails);
                        await new FirebaseMessagingClient().SendNotifications(deviceTokens, $"Your Post {(myForumTopic.Status == ForumTopicStatus.Open ? "Approved" : "Closed")}", $"An Admin has {(myForumTopic.Status == ForumTopicStatus.Open ? "approved" : "closed")} your post \"{myForumTopic.Title}\".", new
                        {
                            NavigateTo = notification.NavigateTo.ToString(),
                            notification.Payload,
                            notification.Id,
                            NotificationType = notification.NotificationType.ToString()
                        });
                        break;
                    case NotificationType.CertificateTestResultPublish:
                        var traineeExam = await _context.TraineeExams.FirstOrDefaultAsync(x => x.TraineeId == user.Trainee.Id);
                        notification = await _context.Notifications.FirstOrDefaultAsync(x => x.TargetTraineeId == user.Trainee.Id && x.NavigateTo == Navigation.CertificateTestResult);
                        await new FirebaseMessagingClient().SendNotifications(deviceTokens, "Certificate Test Result Published", $"Your certificate test result on {traineeExam.Exam.Course.Title} has been published. Check the result.", new
                        {
                            NavigateTo = notification.NavigateTo.ToString(),
                            notification.Payload,
                            notification.Id,
                            NotificationType = notification.NotificationType.ToString()
                        });
                        break;
                    case NotificationType.EvaluationTestResultPublish:
                        notification = await _context.Notifications.FirstOrDefaultAsync(x => x.TargetTraineeId == user.Trainee.Id && x.NavigateTo == Navigation.EvaluationTestResult);
                        var evaluationTraineeExam = await _context.TraineeEvaluationExams.FirstOrDefaultAsync(x => x.TraineeId == user.Trainee.Id);
                        await new FirebaseMessagingClient().SendNotifications(deviceTokens, "Evaluation Test Result Published", $"Your evaluation test result of \"{evaluationTraineeExam.Exam.ExamName}\" has been published. Check the result.", new
                        {
                            NavigateTo = notification.NavigateTo.ToString(),
                            notification.Payload,
                            notification.Id,
                            NotificationType = notification.NotificationType.ToString()
                        });
                        break;
                }

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Sent"
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }


    }

    public interface INotificationService
    {
        Task<APIResponse> GetAllNotifications(int size, int pageNumber, UserType userType, ApplicationUser user, bool unseenOnly = false, Guid? activeNotificationId = null);
        Task<APIResponse> GetUnseenCount(UserType userType, ApplicationUser user);
        Task<APIResponse> NotificationSeen(Guid id);
        Task<APIResponse> DeleteNotification(Guid id);
        Task<APIResponse> TestNotification(NotificationType notificationType, ApplicationUser user);
    }
}
