<block-ui>
  <div class="col-12">
    <div class="card card-border-primary">
      <div class="card-header">
        <h5>Trainee Certificate Test List</h5>
      </div>
      <div class="card-block">
        <div class="row">
          <form [formGroup]="filterForm" class="col-12 custom-inline-form" autocomplete="off">
            <div class="row">
              <div class="mb-3 col-lg-3 col-12">
                <label class="col-form-label col-form-label-sm text-right required">
                  Course
                </label>
                <ng-select #selectElement (click)="handleSelectClick(selectElement)" *ngIf="courseList.length > 0"
                  formControlName="courseId" [clearable]="false" [clearOnBackspace]="false" [items]="courseList"
                  bindLabel="Title" class="form-control form-control-sm" bindValue="Id" placeholder="Select a course">
                </ng-select>
                <div *ngIf="submitted && f.courseId.errors" class="error-text">
                  <span *ngIf="f.courseId.errors.required" class="text-danger">Course is required</span>
                </div>
              </div>

              <div class="mb-3 col-lg-2 col-12">
                <label class="col-form-label col-form-label-sm"> Status </label>
                <ng-select #selectElementS (click)="handleSelectClick(selectElementS)" formControlName="status"
                  [clearable]="true" [clearOnBackspace]="true" [items]="statusList" class="form-control form-control-sm"
                  bindLabel="text" bindValue="id" placeholder="All">
                </ng-select>
              </div>

              <div class="mb-3 col-lg-3 col-12">
                <label class="col-form-label col-form-label-sm text-right">
                  Trainee
                </label>
                <ng-select #selectElementT (click)="handleSelectClick(selectElementT)"
                  class="form-control form-control-sm" [items]="trainee$ | async" formControlName="trainee"
                  bindLabel="Name" [hideSelected]="true" [loading]="traineeLoading"
                  typeToSearchText="Please enter 3 or more characters" [typeahead]="traineeInput$"
                  placeholder="Type trainee pin/name">
                  <ng-template ng-option-tmp let-item="item" let-index="index">
                    <div class="media">
                      <img *ngIf="item.ImagePath" [src]="baseUrl + item.ImagePath" class="rounded-circle me-3"
                        width="40" />
                      <img *ngIf="!item.ImagePath && !item.Gender" src="assets/images/user/user-avatar-blank.png"
                        class="rounded-circle me-3" width="40" />
                      <img *ngIf="!item.ImagePath && item.Gender == 'Others'" src="assets/images/user/other.jpg"
                        alt="user image" class="rounded-circle me-3" width="40" />
                      <img *ngIf="!item.ImagePath && item.Gender == 'Male'" src="assets/images/user/male.jpg"
                        alt="user image" class="rounded-circle me-3" width="40" />
                      <img *ngIf="!item.ImagePath && item.Gender == 'Female'" src="assets/images/user/female.jpg"
                        alt="user image" class="rounded-circle me-3" width="40" />

                      <div class="media-body">
                        <h5 class="mt-0">
                          <b>{{ item.Name }}</b> <br /><b>PIN:</b>
                          {{ item.PIN }} <br /><b>Division :</b>
                          {{ item.Division }}
                        </h5>
                      </div>
                    </div>
                  </ng-template>
                </ng-select>
              </div>

              <div class="mb-3 col-lg-4 col-12">
                <button class="btn btn-theme btn-sm me-2" (click)="filterList()">
                  <i class="feather icon-search"></i>
                  Search
                </button>
                <button class="btn btn-theme btn-sm me-2" (click)="downloadExcel()">
                  <i class="feather icon-download"></i> Download
                </button>
                <a class="btn btn-theme btn-sm text-white float-end" target="_blank" rel="noopener"
                  [routerLink]="['/trainee-certificate-test-publish']"> Bulk Result Publish <i
                    class="fas fa-arrow-right"></i>
                </a>
              </div>
            </div>
          </form>

          <div class="col-lg-12">
            <ngx-datatable class="material table-bordered" [rows]="rows" [loadingIndicator]="loadingIndicator"
              [columnMode]="ColumnMode.force" [headerHeight]="40" [footerHeight]="50" rowHeight="auto"
              [count]="page.totalElements" [offset]="page.pageNumber" [limit]="page.size" (page)="setPage($event)">
              <ngx-datatable-column [maxWidth]="80" name="PIN" prop="PIN" [draggable]="false" [sortable]="false">
                <ng-template let-value="value" ngx-datatable-cell-template>
                  <span title="{{ value }}">{{ value }}</span>
                </ng-template>
              </ngx-datatable-column>

              <ngx-datatable-column name="Name" prop="Name" [draggable]="false" [sortable]="false">
                <ng-template let-value="value" ngx-datatable-cell-template>
                  <span title="{{ value }}">{{ value }}</span>
                </ng-template>
              </ngx-datatable-column>

              <ngx-datatable-column [maxWidth]="180" prop="Division" [draggable]="false" [sortable]="false">
                <ng-template let-value="value" ngx-datatable-cell-template>
                  <span title="{{ value }}">{{ value }}</span>
                </ng-template>
              </ngx-datatable-column>

              <ngx-datatable-column [maxWidth]="185" name="Attend On" prop="StartDate" [draggable]="false"
                [sortable]="false" headerClass="text-center">
                <ng-template let-value="value" ngx-datatable-cell-template>
                  <span *ngIf="value" title="{{ value | amDateFormat : 'MMM DD, YYYY hh:mmA'}}">
                    {{ value | amDateFormat : 'MMM DD, YYYY hh:mmA' }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <ngx-datatable-column [maxWidth]="70" name="T. Marks" prop="TotalMarks" [draggable]="false"
                [sortable]="false">
                <ng-template let-value="value" ngx-datatable-cell-template>
                  <span title="{{ value }}">{{ value }}</span>
                </ng-template>
              </ngx-datatable-column>

              <ngx-datatable-column [maxWidth]="70" name="G. Marks" prop="GainedMarks" [draggable]="false"
                [sortable]="false">
                <ng-template let-value="value" ngx-datatable-cell-template>
                  <b title="{{ value }}">{{ value }}</b>
                </ng-template>
              </ngx-datatable-column>

              <ngx-datatable-column [maxWidth]="110" name="Status" prop="Status" [draggable]="false" [sortable]="false">
                <ng-template let-value="value" ngx-datatable-cell-template>
                  <b title="{{ value }}">{{ value }}</b>
                </ng-template>
              </ngx-datatable-column>

              <ngx-datatable-column [maxWidth]="150" name="Marked By" [draggable]="false" [sortable]="false">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span *ngIf="row.Checker" title="{{ row.Checker }}">{{
                    row.Checker
                    }}</span>
                  <span *ngIf="
                        (row.Status === 'Examined' ||
                          row.Status === 'Published') &&
                        !row.Checker
                      " title="AUTO">AUTO</span>
                </ng-template>
              </ngx-datatable-column>

              <ngx-datatable-column [maxWidth]="90" name="Result" prop="Result" [draggable]="false" [sortable]="false">
                <ng-template let-value="value" ngx-datatable-cell-template>
                  <span *ngIf="value === 'Passed'" class="badge bg-success">{{
                    value
                    }}</span>
                  <span *ngIf="value === 'Failed'" class="badge bg-danger">{{
                    value
                    }}</span>
                </ng-template>
              </ngx-datatable-column>

              <ngx-datatable-column [maxWidth]="120" name="Marked On" prop="MarkedOn" [draggable]="false"
                [sortable]="false">
                <ng-template let-value="value" ngx-datatable-cell-template>
                  <span *ngIf="value" title="{{ value | amDateFormat: 'MMM DD, YYYY' }}">
                    {{ value | amDateFormat: "MMM DD, YYYY" }}
                  </span>
                </ng-template>
              </ngx-datatable-column>

              <ngx-datatable-column [maxWidth]="110" name="Termination" prop="Terminated" [draggable]="false"
                [sortable]="false">
                <ng-template let-value="value" ngx-datatable-cell-template>
                  <b title="{{ value }}">{{ value }}</b>
                </ng-template>
              </ngx-datatable-column>

              <ngx-datatable-column [maxWidth]="120" name="Action" [draggable]="false" [sortable]="false"
                cellClass="text-center" headerClass="text-center">
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <button *ngIf="row.Result !== 'Passed'" class="btn btn-warning btn-mini me-1" title="Extend Quota"
                    (click)="ExtendQuota(row,templateExtendQuota)">
                    <i class="feather icon-edit"></i>
                  </button>
                  <button *ngIf="row.Status !== 'Submitted'" class="btn btn-info btn-mini me-1" title="Download PDF"
                    (click)="downloadAnswersheet(row)">
                    <i class="feather icon-download"></i>
                  </button>
                  <a target="blank" rel="noopener" class="btn btn-primary btn-mini" title="Check Answersheet"
                    [routerLink]="['/check-trainee-answersheet', row.Id]">
                    <i class="feather icon-arrow-right"></i></a>
                </ng-template>
              </ngx-datatable-column>
            </ngx-datatable>
          </div>
        </div>
      </div>
      <!-- <div class="card-footer">
          <button
            class="btn btn-theme btn-testz btn-sm"
            (click)="openUploadDialog()"
          >
            <i class="fas fa-file-upload"></i> Upload Marking File
          </button>
        </div> -->
      <!-- end of card-footer -->
    </div>
  </div>

  <ng-template #templateExtendQuota>
    <div class="modal-header">
      <h4 class="modal-title float-start">
        Extend quota for {{ExtendedQuotaUser}}
      </h4>
      <button type="button " class="btn-close" data-bs-dismiss="modal" aria-label="Close"
        (click)="modalHideExtendQuota()">
      </button>
    </div>
    <div class="modal-body">

      <!-- <p>Fill ExtendedQuota.</p> -->
      <form [formGroup]="extendQuota" autocomplete="off">
        <div class="mb-3 row">
          <label for="pin" class="col-sm-4 col-12 col-form-label">Extended Quota</label>
          <div class="col-sm-8 col-12">
            <input type="number" class="form-control form-control-sm" formControlName="ExtendQuota">
            <div *ngIf="submitted && f.ExtendQuota" class="invalid-feedback">
              <div *ngIf="f.ExtendQuota.errors['required']" class="text-danger">Quota is
                required
              </div>
            </div>
          </div>

        </div>
        <div class="d-grid">
          <button class="btn btn-primary btn-sm" (click)="onSaveExtendQuota()">Save</button>
        </div>
        <div class="text-center mt-3 fw-bold">
          <a href="javascript:;" class="tctl-style-1" (click)="modalHideExtendQuota()">Close</a>
        </div>
      </form>



    </div>
  </ng-template>
</block-ui>