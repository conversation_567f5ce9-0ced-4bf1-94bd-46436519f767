<block-ui>
  <!-- Page content-->
  <div class="container-fluid position-relative zindex-5 pb-0 mb-md-1 p-3 margin-top-neg-15">
    <div class="d-flex flex-column h-100 bg-tranparent-black-glass rounded-1 shadow-lg">
      <div class="pt-2 p-md-3">
        <div class="d-sm-flex align-items-center justify-content-between pb-1 text-center text-sm-start">
          <h1 class="h3 mb-2 text-break">{{ topic.Title }}</h1>
          <a class="btn btn-link text-decoration-none fs-4 fw-bold btn-sm mb-2" (click)="backClicked()">
            <i class="fs-4 ai-arrow-left fs-base me-2"></i>Go Back
          </a>
        </div>
        <hr class="mt-1 mb-4" />
        <!-- Content-->

        <div class="row">
          <div class="col-lg-8 border-end">
            <div class="border-bottom">
              <!-- <div class="d-flex align-items-center pb-1">
              <h2 class="h3 nav-heading"><a [routerLink]="['/forum-details/1']"
                  routerLinkActive="router-link-active">Simple steps to an effective brand
                  strategy. Real life examples</a></h2>
            </div> -->
              <p class="mb-1" [innerHTML]="topic.Description | safe: 'html'"></p>

              <div class="d-sm-flex align-items-center justify-content-start text-center text-sm-start py-2">
                <button class="btn btn-primary btn-sm me-2 py-1 px-2 fs-7">
                  #{{topic.Category}}
                </button>
                <button type="button" class="btn btn-outline-primary btn-sm me-2 py-1 px-2 fs-7"
                  *ngFor="let tag of topic.Tags">
                  #{{ tag }}
                </button>
              </div>


            </div>
            <!-- <hr class="mt-4"> -->

            <div class="row position-relative g-0 align-items-center border-top border-bottom mb-4">
              <div class="col-md-6 py-3 pe-md-3">
                <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                  <div class="d-flex align-items-center me-grid-gutter">
                    <img *ngIf="
                            !topic.CreatorImage && topic.CreatorGender == 'Male'
                          " class="rounded-circle me-1" src="assets/img/user/male.jpg" alt="{{ topic.Creator }}"
                      width="64" />
                    <img *ngIf="
                            !topic.CreatorImage &&
                            topic.CreatorGender == 'Female'
                          " class="rounded-circle me-1" src="assets/img/user/female.jpg" alt="{{ topic.Creator }}"
                      width="64" />
                    <img *ngIf="
                            !topic.CreatorImage &&
                            topic.CreatorGender == 'Others'
                          " class="rounded-circle me-1" src="assets/img/user/other.jpg" alt="{{ topic.Creator }}"
                      width="64" />
                    <img *ngIf="topic.CreatorImage" class="rounded-circle me-1" [src]="baseUrl + topic.CreatorImage"
                      alt="{{ topic.Creator }}" width="64" />
                    <div class="ps-2">
                      <div class="text-nowrap">
                        <h6 class="fs-sm mb-n1">
                          Posted by:
                          <span class="text-primary cursor-pointer" [popover]="popTemplate"
                            containerClass="custom-popover" container="body" [outsideClick]="true"
                            (onShown)="onTitleClick(topic.CreatorId)">
                            {{
                            topic.Creator
                            }}
                          </span>
                        </h6>
                        <span class="fs-xs text-muted">
                          {{ topic.CreatedDate | amTimeAgo }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="d-none d-md-block position-absolute border-start h-100 fc-style-1"></div>
              <div class="col-md-6 ps-md-3 py-3">
                <div class="d-flex align-items-center justify-content-center justify-content-md-end">
                  <!-- <button class="btn-like fs-5 cursor-default" type="button">
                  {{ topic.NoOfLikes }}
                </button> -->
                  <button class="btn-comment cursor-default" type="button" *ngIf="topic.NoOfReplies">
                    <i class="far fa-comment-alt text-primary fs-5"></i>
                    {{ topic.NoOfReplies }}
                  </button>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-8">
                <div class="d-sm-flex align-items-center justify-content-between pb-2 text-center text-sm-start">
                  <h1 class="h3 mb-0 text-nowrap">Comments</h1>
                </div>
              </div>
              <div class="col-md-4">
                <div *ngIf="topic.Status === 'Open'" class="d-flex align-items-center justify-content-center">
                  <a class="btn btn-primary d-block w-100" (click)="isCollapsedComment = !isCollapsedComment"
                    [attr.aria-expanded]="!isCollapsedComment" aria-controls="collapseComment">Write A Comment</a>
                </div>
                <div *ngIf="topic.Status !== 'Open'"
                  class="d-sm-flex align-items-center justify-content-end text-center text-sm-start">
                  <span class="badge bg-success me-2 px-3 py-2 fs-6 rounded-3" [ngClass]="{
                          'bg-warning': topic.Status === 'Pending',
                          'bg-danger': topic.Status === 'Closed'
                        }">
                    {{ topic.Status }}
                  </span>
                </div>
              </div>

              <div class="col-md-12">
                <div class="collapse py-3 pe-md-3" id="collapseComment" [collapse]="isCollapsedComment"
                  [isAnimated]="true">
                  <form [formGroup]="commentForm" autocomplete="off"
                    class="needs-validation bg-light rounded-3 shadow p-4">
                    <div class="mb-3">
                      <label class="form-label" for="com-text">
                        Your Comment
                        <sup class="text-danger ms-1">*</sup>
                      </label>
                      <textarea class="form-control" id="com-text" rows="4" placeholder="Write your comment  here"
                        formControlName="comment" required></textarea>
                      <div *ngIf="submitted && cf['comment'].errors" class="error-text">
                        <span *ngIf="cf['comment'].errors['required']" class="text-danger">
                          Comment is required
                        </span>
                        <span *ngIf="cf['comment'].errors['maxLength']" class="text-danger">
                          Comment can not more
                          than 2000 characters
                        </span>
                      </div>
                    </div>

                    <div class="col-4 float-end">
                      <div class="d-grid">
                        <button type="button" class="btn btn-primary" (click)="onSubmitPostForm()">
                          Post
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>

            <div class="py-3">
              <div class="comment" *ngFor="let item of postList">
                <p [innerHTML]="item.Comment | safe: 'html'"></p>
                <div class="d-flex justify-content-between align-items-center">
                  <div class="d-flex align-items-center">
                    <img *ngIf="
                            !item.CreatorImage && item.CreatorGender == 'Male'
                          " class="rounded-circle" src="assets/img/user/male.jpg" alt="{{ item.Creator }}"
                      width="42" />
                    <img *ngIf="
                            !item.CreatorImage && item.CreatorGender == 'Female'
                          " class="rounded-circle" src="assets/img/user/female.jpg" alt="{{ item.Creator }}"
                      width="42" />
                    <img *ngIf="
                            !item.CreatorImage && item.CreatorGender == 'Others'
                          " class="rounded-circle" src="assets/img/user/other.jpg" alt="{{ item.Creator }}"
                      width="42" />
                    <img *ngIf="item.CreatorImage" class="rounded-circle" [src]="baseUrl + item.CreatorImage"
                      alt="{{ item.Creator }}" width="42" />
                    <div class="ps-2 ms-1">
                      <h4 class="fs-sm mb-0 cursor-pointer" [popover]="popTemplate" containerClass="custom-popover"
                        container="body" [outsideClick]="true" (onShown)="onTitleClick(item.CreatorId)">
                        {{
                        item.Creator
                        }}
                      </h4>
                      <span class="fs-xs text-muted">
                        {{
                        item.CreatedDate | amTimeAgo
                        }}
                      </span>
                    </div>
                  </div>
                  <div class="d-flex align-items-center justify-content-center justify-content-md-end">
                    <button [disabled]="topic.Status !== 'Open'" (click)="onLikeButtonClick(item)"
                      class="btn-comment-like fs-5 fw-bold me-2" type="button">
                      <i class="fa-thumbs-up fa-lg me-1" [ngClass]="{
                              'fa-solid text-primary': item.Liked,
                              'fa-regular': !item.Liked
                            }"></i>
                      {{ item.NoOfLikes }}
                    </button>
                    <button class="btn-comment fs-5 fw-bold  cursor-default" type="button">
                      <i class="far fa-comment-alt fa-lg me-1"></i>
                      {{ item.Replies.length }}
                    </button>
                  </div>
                </div>
                <div class="comment mt-3" *ngFor="let reply of item.Replies">
                  <span>
                    {{ reply.Comment }}
                  </span>
                  <div class="d-inline-flex align-items-center">
                    –
                    <span class="fw-bold text-primary cursor-pointer" [popover]="popTemplate"
                      containerClass="custom-popover" container="body" [outsideClick]="true"
                      (onShown)="onTitleClick(reply.CreatorId)">
                      {{
                      reply.Creator
                      }}
                    </span>
                  </div>
                  <span class="text-muted">
                    {{
                    reply.CreatedDate | amCalendar
                    }}
                  </span>
                </div>

                <div class="row">
                  <div class="col-12 text-end mt-3">
                    <button *ngIf="topic.Status === 'Open'" type="button" class="btn btn-outline-primary btn-sm py-1"
                      (click)="onCollapse(item)" [attr.aria-expanded]="!item.IsCollapsed"
                      [attr.aria-controls]="item.Id">
                      <i class="ai-corner-up-left fs-base me-2 ms-n1"></i>
                      Reply
                    </button>
                  </div>
                  <div class="col-12" id="{{ item.Id }}" [collapse]="item.IsCollapsed" [isAnimated]="true">
                    <form [formGroup]="replyForm" autocomplete="off" class="needs-validation bg-light rounded-3 shadow">
                      <div class="mb-3">
                        <label class="form-label" for="com-text">
                          Your Reply
                          <sup class="text-danger ms-1">*</sup>
                        </label>
                        <textarea class="form-control" id="com-text" rows="4" placeholder="Write your reply here"
                          formControlName="comment" required></textarea>
                        <div *ngIf="replySubmitted && rf['comment'].errors" class="error-text">
                          <span *ngIf="rf['comment'].errors['required']" class="text-danger">
                            Reply is required
                          </span>
                          <span *ngIf="rf['comment'].errors['maxLength']" class="text-danger">
                            Reply can not more
                            than 1000 characters
                          </span>
                        </div>
                      </div>

                      <div class="col-4 float-end">
                        <div class="d-grid">
                          <button type="button" class="btn btn-primary" (click)="onSubmitReplyForm(item)">
                            Post
                          </button>
                        </div>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-4 sidebar bg-secondary pt-5 ps-lg-4 pb-md-2 border-end">
            <!-- <div class="widget mt-n1" *ngIf="myTopicList.length > 0">
            <h2 class="h3 pb-1">My posts</h2>

            <div class="d-flex align-items-center pb-1 mb-3" *ngFor="let item of myTopicList; let i = index">
              <div class="ms-1">
                <h4 class="fs-md nav-heading mb-1">
                  <a class="fw-medium fs-5" [routerLink]="['/forum-details', item.Id]"
                    routerLinkActive="router-link-active">{{ i + 1 }}. {{ item.Title }}</a>
                </h4>
              </div>
            </div>
          </div>

          <hr class="my-4" *ngIf="myTopicList.length > 0" /> -->

            <div class="widget mt-n1" *ngIf="popularList.length > 0">
              <h2 class="h3 pb-1">My Popular Posts</h2>

              <div class="d-flex align-items-center pb-1 mb-3" *ngFor="let item of popularList; let i = index">
                <div class="ms-1">
                  <h4 class="fs-md nav-heading mb-1">
                    <a class="fw-medium fs-5" [routerLink]="['/forum-details', item.Id]"
                      routerLinkActive="router-link-active">{{ i + 1 }}. {{ item.Title }}</a>
                  </h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <ng-template #popTemplate>
    <div class="card wid-100 mb-0" *ngIf="popoverObj">
      <div class="card-header p-2">
        <h5 class="card-title mb-0">{{popoverObj.Name}} - {{popoverObj.PIN}}</h5>
      </div>
      <div class="card-body p-2">
        <table class="table table-sm table-borderless table-hover mb-0">
          <tbody>
            <tr>
              <th>Email</th>
              <td class="text-wrap">{{popoverObj.Email}}</td>
              <th>Division</th>
              <td class="text-wrap">{{popoverObj.Division}}</td>
            </tr>
            <tr>
              <th>Phone</th>
              <td class="text-wrap">{{popoverObj.PhoneNo}}</td>
              <th>Department</th>
              <td class="text-wrap">{{popoverObj.Department}}</td>
            </tr>
            <tr>
              <th>Position</th>
              <td class="text-wrap">{{popoverObj.Position}}</td>
              <th>Unit</th>
              <td class="text-wrap">{{popoverObj.Unit}}</td>
            </tr>
            <tr>
              <th>Work Location</th>
              <td class="text-wrap">{{popoverObj.WorkLocation}}</td>
              <th>Sub Unit</th>
              <td class="text-wrap">{{popoverObj.SubUnit}}</td>
            </tr>
            <tr>
              <th>Line Manager</th>
              <td class="text-wrap">{{popoverObj.LineManagerName}} - {{popoverObj.LineManagerPIN}}</td>
              <th>Grade</th>
              <td class="text-wrap">{{popoverObj.Grade}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </ng-template>
</block-ui>